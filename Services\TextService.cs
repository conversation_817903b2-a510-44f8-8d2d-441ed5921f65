using Microsoft.EntityFrameworkCore;
using Pgvector;
using TranslationAgentServer.Helpers;
using TranslationAgentServer.Interfaces;
using TranslationAgentServer.Models;
using TranslationAgentServer.Models.Filtering;
using System.Text.Json;


namespace TranslationAgentServer.Services;

/// <summary>
/// Text servisi implementasyonu
/// Metin verilerinin CRUD işlemlerini ve özel sorguları gerçekleştirir
/// </summary>
public class TextService : ITextService
{
    private readonly IDatabaseService _databaseService;
    private readonly INlpService _nlpService;
    private readonly IGeminiService _geminiService;
    private readonly ILogger<TextService> _logger;

    public TextService(
        IDatabaseService databaseService,
        INlpService nlpService,
        IGeminiService geminiService,
        ILogger<TextService> logger)
    {
        _databaseService = databaseService;
        _nlpService = nlpService;
        _geminiService = geminiService;
        _logger = logger;
    }

    public async Task<List<Text>> GetAllTextsAsync(int projectId)
    {
        try
        {
            await using var client = _databaseService.GetProjectContext(projectId);


            var result = await client.Texts.ToListAsync();
            return result ?? new List<Text>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Metinler getirilirken hata oluştu. Proje: {ProjectId}", projectId);
            throw;
        }
    }

    public async Task<List<Text>> GetAllTextsAsync(int projectId, TextFilterQuery filterQuery)
    {
        try
        {
            await using var client = _databaseService.GetProjectContext(projectId);
            var query = client.Texts.AsQueryable();

            var filteredQuery = FilteringService.ApplyFilters(query, filterQuery);

            return await filteredQuery.ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Gelişmiş filtreleme ile metinler getirilirken hata oluştu. Proje: {ProjectId}", projectId);
            throw;
        }
    }

    public async Task<List<Text>> GetAllTextsAsync(int projectId, JsonElement filterElement)
    {
        try
        {
            var filterQuery = TextFilterQuery.FromJsonElement(filterElement);
            return await GetAllTextsAsync(projectId, filterQuery);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "JsonElement filtreleme ile metinler getirilirken hata oluştu. Proje: {ProjectId}", projectId);
            throw;
        }
    }

    public async Task<Text?> GetTextByIdAsync(int id, int projectId)
    {
        try
        {
            await using var client = _databaseService.GetProjectContext(projectId);
            var response = await client.Texts
                .Where(x => x.Id == id)
                .SingleAsync();

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Metin getirilirken hata oluştu. ID: {Id}, Şema: {projectId}", id, projectId);
            throw;
        }
    }

    public async Task<TextTranslation?> GetTextTranslationsAsync(int id, int projectId)
    {
        try
        {
            await using var client = _databaseService.GetProjectContext(projectId);

            // İlgili çeviri geçmişini getir (varsa)
            var translation = await client.TextTranslations
                .Where(x => x.Id == id) // Text ID ile eşleşen translation
                .OrderByDescending(x => x.ProcessedAt)
                .FirstOrDefaultAsync();

            if (translation == null)
                return null;

            return translation;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Metin ve çeviri geçmişi getirilirken hata oluştu. ID: {Id}, Proje: {ProjectId}", id, projectId);
            throw;
        }
    }

    public async Task<List<Text>> FindSimilarTextsAsync(float[] embedding, int limit, int projectId)
    {
        try
        {
            // Embedding benzerlik araması için özel SQL sorgusu gerekebilir
            // Şimdilik basit bir implementasyon
            var allTexts = await GetAllTextsAsync(projectId);

            // Embedding'i olan metinleri filtrele ve benzerlik hesapla
            var textsWithEmbedding = allTexts
                .Where(t => t.Embedding != null)
                .Select(t => new { Text = t })//, Similarity = EmbeddingHelper.CalculateCosineSimilarity(embedding, t.Embedding!)
                                              // .OrderByDescending(x => x.Similarity)
                .Take(limit)
                .Select(x => x.Text)
                .ToList();

            return textsWithEmbedding;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Benzer metin arama işleminde hata oluştu. Şema: {projectId}", projectId);
            throw;
        }
    }

    public async Task<Text?> CreateTextAsync(TextCreateDto textCreateDto, int projectId)
    {
        try
        {
            var text = new Text
            {
                RowID = textCreateDto.RowID,
                Namespace = textCreateDto.Namespace,
                Key = textCreateDto.Key,
                En = textCreateDto.En,
                Tr = textCreateDto.Tr,
                Status = (int)textCreateDto.Status,
                Note = textCreateDto.Note,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };


            // İngilizce metin varsa, embedding ve lemma oluşturur. Duplikat ve boş metinler için embedding ve lemma oluşturmaz.
            if (!string.IsNullOrEmpty(text.En) && text.Status != (int)TextStatus.DUPE && text.Status != (int)TextStatus.NULL)
            {
                var embeddingResponse = await _geminiService.GetEmbeddingAsync(text.En);
                if (embeddingResponse.Success)
                {
                    text.Embedding = new Vector(embeddingResponse.Embedding);
                }
                else
                {
                    throw new Exception($"Embedding oluşturulamadı. Hata: {embeddingResponse.ErrorMessage}");
                }

                text.Lemma = await _nlpService.ProcessTextToLemmaAsync(text.En);
            }


            return text;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Metin oluşturulurken hata oluştu. Şema: {projectId}", projectId);
            throw;
        }
    }

    public async Task<Text?> AddTextAsync(Text text, int projectId)
    {
        try
        {
            await using var client = _databaseService.GetProjectContext(projectId);
            var response = await client.Texts.AddAsync(text);
            var result = await client.SaveChangesAsync();

            return result > 0 ? response.Entity : null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Metin eklenirken hata oluştu. Şema: {projectId}", projectId);
            throw;
        }
    }

    public async Task<Text?> UpdateTextAsync(int id, TextUpdateDto textUpdateDto, int projectId)
    {
        try
        {
            var existingText = await GetTextByIdAsync(id, projectId);
            if (existingText == null)
            {
                return null;
            }
            // Eğer İngilizce metin güncellendiyse, embedding ve lemma'yı yeniden oluşturur. Duplikat ve boş metinler için embedding ve lemma oluşturmaz.
            if (textUpdateDto.En != existingText.En && !string.IsNullOrEmpty(textUpdateDto.En) &&
            textUpdateDto.Status != TextStatus.DUPE && textUpdateDto.Status != TextStatus.NULL)
            {
                existingText.Lemma = await _nlpService.ProcessTextToLemmaAsync(textUpdateDto.En);
                var embeddingResponse = await _geminiService.GetEmbeddingAsync(textUpdateDto.En);
                if (embeddingResponse.Success)
                {
                    existingText.Embedding = new Vector(embeddingResponse.Embedding);
                }
                else
                {
                    throw new Exception($"Embedding oluşturulamadı. Hata: {embeddingResponse.ErrorMessage}");
                }
            }

            // Eğer İngilizce metin güncellendiyse, embedding ve lemma'yı yeniden oluşturur. Duplikat ve boş metinler için embedding ve lemma oluşturmaz.
            if (!string.IsNullOrEmpty(textUpdateDto.En) && textUpdateDto.Status != TextStatus.DUPE && textUpdateDto.Status != TextStatus.NULL)
            {
                existingText.Lemma = await _nlpService.ProcessTextToLemmaAsync(textUpdateDto.En);
                var embeddingResponse = await _geminiService.GetEmbeddingAsync(textUpdateDto.En);
                if (embeddingResponse.Success)
                {
                    existingText.Embedding = new Vector(embeddingResponse.Embedding);
                }
                else
                {
                    throw new Exception($"Embedding oluşturulamadı. Hata: {embeddingResponse.ErrorMessage}");
                }
            }
            // Güncelleme alanlarını ayarla
            if (textUpdateDto.Namespace != null) existingText.Namespace = textUpdateDto.Namespace;
            if (textUpdateDto.Key != null) existingText.Key = textUpdateDto.Key;
            if (textUpdateDto.En != null) existingText.En = textUpdateDto.En;
            if (textUpdateDto.Tr != null) existingText.Tr = textUpdateDto.Tr;
            if (textUpdateDto.Note != null) existingText.Note = textUpdateDto.Note;
            if (textUpdateDto.Status.HasValue) existingText.Status = (int)textUpdateDto.Status;
            existingText.UpdatedAt = DateTime.UtcNow;

            await using var client = _databaseService.GetProjectContext(projectId);
            existingText = client.Texts.Update(existingText).Entity;
            var result = await client.SaveChangesAsync();

            return result > 0 ? existingText : null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Metin güncellenirken hata oluştu. ID: {Id}, Şema: {projectId}", id, projectId);
            throw;
        }
    }

    public async Task<bool> DeleteTextAsync(int id, int projectId)
    {
        try
        {
            await using var client = _databaseService.GetProjectContext(projectId);
            var deletedText = await client.Texts.Where(x => x.Id == id).SingleAsync();
            if (deletedText == null)
            {
                return false;
            }
            var deletedTextTranslations = await client.TextTranslations.Where(x => x.Id == id).SingleAsync();
            if (deletedTextTranslations != null)
            {
                client.TextTranslations.Remove(deletedTextTranslations);
            }
            client.Texts.Remove(deletedText);
            var result = await client.SaveChangesAsync();

            return result > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Metin silinirken hata oluştu. ID: {Id}, Şema: {projectId}", id, projectId);
            throw;
        }
    }

    public async Task<List<Text>> CreateTextsAsync(List<TextCreateDto> textCreateDtos, int projectId, CancellationToken cancellationToken)
    {
        try
        {
            await using var client = _databaseService.GetProjectContext(projectId);

            var texts = new List<Text>();
            foreach (var dto in textCreateDtos)
            {
                cancellationToken.ThrowIfCancellationRequested();
                var addtext = new Text();
                addtext.RowID = dto.RowID;
                addtext.Namespace = dto.Namespace;
                addtext.Key = dto.Key;
                addtext.En = dto.En;


                addtext.Tr = dto.Tr;
                addtext.Lemma = await _nlpService.ProcessTextToLemmaAsync(dto.En);

                addtext.Note = dto.Note;
                addtext.Status = (int)dto.Status;
                addtext.CreatedAt = DateTime.UtcNow;
                addtext.UpdatedAt = DateTime.UtcNow;
                texts.Add(addtext);

            }

            var toEmbedTexts = texts.Where(t => t.Embedding == null && !string.IsNullOrEmpty(t.En) &&
            t.Status != (int)TextStatus.DUPE && t.Status != (int)TextStatus.NULL).ToList();

            var batchSize = 100;
            for (int i = 0; i < toEmbedTexts.Count; i += batchSize)
            {
                var batch = toEmbedTexts.Skip(i).Take(batchSize).ToList();

                // Retry mekanizması ile embedding üretimi
                var embeddingsResponse = await _geminiService.GetEmbeddingsAsync(batch.Select(t => t.En!).ToList(), cancellationToken);

                if (!embeddingsResponse.Success)
                {
                    throw new Exception($"Embedding oluşturulamadı. Hata: {embeddingsResponse.ErrorMessage}");
                }

                for (int j = 0; j < batch.Count; j++)
                {
                    batch[j].Embedding = new Vector(embeddingsResponse.Embeddings[j]);
                }

                cancellationToken.ThrowIfCancellationRequested();

                // Batch'ler arası bekleme süresi (rate limiting için)
                await Task.Delay(3500);
            }
            return texts;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Toplu metin oluşturma işleminde hata oluştu. Şema: {projectId}", projectId);
            throw;
        }
    }


    public async Task<int> AddTextsAsync(List<Text> addTexts, int projectId, CancellationToken cancellationToken)
    {

        await using var client = _databaseService.GetProjectContext(projectId);
        await using var transaction = await client.Database.BeginTransactionAsync(cancellationToken);
        try
        {

            client.Texts.RemoveRange(client.Texts);
            await client.Texts.AddRangeAsync(addTexts, cancellationToken);
            await client.SaveChangesAsync(cancellationToken);
            await transaction.CommitAsync(cancellationToken);
        }
        catch
        {
            await transaction.RollbackAsync(cancellationToken);
            throw;
        }

        return addTexts.Count;
    }
    public async Task<int> UpdateTextsAsync(List<(int Id, TextUpdateDto UpdateDto)> textUpdates, int projectId, CancellationToken cancellationToken)
    {
        try
        {
            await using var client = _databaseService.GetProjectContext(projectId);
            var updatedTexts = new List<Text>();

            foreach (var (id, updateDto) in textUpdates)
            {
                var existingText = await GetTextByIdAsync(id, projectId);
                if (existingText == null)
                {
                    continue;
                }

                if (!string.IsNullOrEmpty(updateDto.En) && updateDto.Status != TextStatus.DUPE && updateDto.Status != TextStatus.NULL)
                {
                    existingText.Lemma = await _nlpService.ProcessTextToLemmaAsync(updateDto.En);
                }
                if (updateDto.En != existingText.En)
                {
                    existingText.Embedding = null;
                }

                if (updateDto.Namespace != null) existingText.Namespace = updateDto.Namespace;
                if (updateDto.Key != null) existingText.Key = updateDto.Key;
                if (updateDto.En != null) existingText.En = updateDto.En;
                if (updateDto.Tr != null) existingText.Tr = updateDto.Tr;
                if (updateDto.Note != null) existingText.Note = updateDto.Note;
                if (updateDto.Status.HasValue) existingText.Status = (int)updateDto.Status;
                existingText.UpdatedAt = DateTime.UtcNow;

                cancellationToken.ThrowIfCancellationRequested();
                updatedTexts.Add(existingText);
            }

            var toEmbedTexts = updatedTexts.Where(t => t.Embedding == null && !string.IsNullOrEmpty(t.En) &&
            t.Status != (int)TextStatus.DUPE && t.Status != (int)TextStatus.NULL).ToList();

            var batchSize = 100;
            for (int i = 0; i < toEmbedTexts.Count; i += batchSize)
            {
                var batch = toEmbedTexts.Skip(i).Take(batchSize).ToList();
                // Retry mekanizması ile embedding üretimi
                var embeddingsResponse = await _geminiService.GetEmbeddingsAsync(batch.Select(t => t.En!).ToList(), cancellationToken);
                if (!embeddingsResponse.Success)
                {
                    throw new Exception($"Embedding oluşturulamadı. Hata: {embeddingsResponse.ErrorMessage}");
                }
                for (int j = 0; j < batch.Count; j++)
                {
                    batch[j].Embedding = new Vector(embeddingsResponse.Embeddings[j]);
                }
                // Batch'ler arası bekleme süresi (rate limiting için)
                await Task.Delay(3500);
            }
            await using var transaction = await client.Database.BeginTransactionAsync(cancellationToken);
            try
            {
                client.Texts.UpdateRange(updatedTexts);
                await client.SaveChangesAsync(cancellationToken);
                await transaction.CommitAsync(cancellationToken);
            }
            catch
            {
                await transaction.RollbackAsync(cancellationToken);
                throw;
            }
            return updatedTexts.Count;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Toplu metin güncelleme işleminde hata oluştu. Şema: {projectId}", projectId);
            throw;
        }
    }

    public async Task<int> DeleteTextsAsync(List<int> ids, int projectId)
    {
        try
        {
            await using var client = _databaseService.GetProjectContext(projectId);
            foreach (var id in ids)
            {
                var deletedText = await client.Texts.Where(x => x.Id == id).SingleAsync();
                if (deletedText == null)
                {
                    continue;
                }
                var deletedTextTranslations = await client.TextTranslations.Where(x => x.Id == id).SingleAsync();
                if (deletedTextTranslations != null)
                {
                    client.TextTranslations.Remove(deletedTextTranslations);
                }
                client.Texts.Remove(deletedText);
            }
            var result = await client.SaveChangesAsync();
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Toplu metin silme işleminde hata oluştu. Şema: {projectId}", projectId);
            throw;
        }
    }


    public async Task<TextStatistics> GetTextStatisticsAsync(int projectId)
    {
        try
        {
            var allTexts = await GetAllTextsAsync(projectId);

            var statistics = new TextStatistics
            {
                TotalTexts = allTexts.Count,
                EnglishTexts = allTexts.Count(t => t.Status == (int)TextStatus.EN),
                TurkishTexts = allTexts.Count(t => t.Status == (int)TextStatus.TR),
                DuplicateTexts = allTexts.Count(t => t.Status == (int)TextStatus.DUPE),
                NullTexts = allTexts.Count(t => t.Status == (int)TextStatus.NULL),
                TextsWithEmbedding = allTexts.Count(t => t.Embedding != null),
                LemmatizedTexts = allTexts.Count(t => !string.IsNullOrEmpty(t.Lemma)),
                NamespaceDistribution = allTexts
                    .Where(t => !string.IsNullOrEmpty(t.Namespace))
                    .GroupBy(t => t.Namespace!)
                    .ToDictionary(g => g.Key, g => g.Count()),
                SheetDistribution = allTexts
                    .GroupBy(t => t.RowID)
                    .ToDictionary(g => g.Key, g => g.Count())
            };

            return statistics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Metin istatistikleri hesaplanırken hata oluştu. Şema: {projectId}", projectId);
            throw;
        }
    }
}