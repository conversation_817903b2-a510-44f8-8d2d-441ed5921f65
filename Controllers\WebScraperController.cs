using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TranslationAgentServer.Interfaces;

namespace TranslationAgentServer.Controllers
{
    /// <summary>
    /// Web scraping işlemleri için API controller'ı
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class WebScraperController : ControllerBase
    {
        private readonly IWebScraperService _webScraperService;
        private readonly ILogger<WebScraperController> _logger;

        public WebScraperController(IWebScraperService webScraperService, ILogger<WebScraperController> logger)
        {
            _webScraperService = webScraperService;
            _logger = logger;
        }

        /// <summary>
        /// Belirtilen URL'den içerik çıkarır
        /// </summary>
        /// <param name="url">İçerik çıkarılacak web sitesi URL'si</param>
        /// <returns>Çıkarılan içerik</returns>
        [HttpGet("scrape")]
        public async Task<IActionResult> ScrapeContent([FromQuery] string url)
        {
            try
            {
                _logger.LogInformation("İçerik çıkarma isteği alındı: {Url}", url);
                var content = await _webScraperService.ScrapeContentAsync(url);
                return Ok(new { url, content });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "İçerik çıkarılırken hata oluştu: {Url}", url);
                return StatusCode(500, new { error = ex.Message });
            }
        }

        /// <summary>
        /// Belirtilen URL'den belirli HTML elementlerini çıkarır
        /// </summary>
        /// <param name="url">İçerik çıkarılacak web sitesi URL'si</param>
        /// <param name="selector">CSS veya XPath seçici</param>
        /// <returns>Seçilen elementlerin içeriği</returns>
        [HttpGet("elements")]
        public async Task<IActionResult> ScrapeElements([FromQuery] string url, [FromQuery] string selector)
        {
            try
            {
                _logger.LogInformation("Element çıkarma isteği alındı: {Url}, Selector: {Selector}", url, selector);
                var elements = await _webScraperService.ScrapeElementsAsync(url, selector);
                return Ok(new { url, selector, elements });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Elementler çıkarılırken hata oluştu: {Url}, Selector: {Selector}", url, selector);
                return StatusCode(500, new { error = ex.Message });
            }
        }

        /// <summary>
        /// Belirtilen URL'den başlayarak belirli derinlikte ve sayfa sayısında içerik çıkarır
        /// </summary>
        /// <param name="url">Başlangıç URL'si</param>
        /// <param name="selector">CSS veya XPath seçici</param>
        /// <param name="maxDepth">Maksimum derinlik seviyesi</param>
        /// <param name="maxPages">Maksimum sayfa sayısı</param>
        /// <param name="urlPattern">URL filtreleme için regex deseni (isteğe bağlı)</param>
        /// <param name="contentPattern">İçerik filtreleme için regex deseni (isteğe bağlı)</param>
        /// <returns>URL ve içerik eşleştirmelerini içeren sözlük</returns>
        [HttpGet("website")]
        public async Task<IActionResult> ScrapeWebsite(
            [FromQuery] string url,
            [FromQuery] string? selector = null,
            [FromQuery] int maxDepth = 1,
            [FromQuery] int maxPages = 10,
            [FromQuery] string? urlPattern = null,
            [FromQuery] string? contentPattern = null)
        {
            try
            {
                _logger.LogInformation("Web sitesi kazıma isteği alındı: {Url}, MaxDepth: {MaxDepth}, MaxPages: {MaxPages}", url, maxDepth, maxPages);

                // URL filtreleme fonksiyonu oluştur
                Func<string, bool>? urlFilter = null;
                if (!string.IsNullOrEmpty(urlPattern))
                {
                    var regex = new System.Text.RegularExpressions.Regex(urlPattern);
                    urlFilter = (u) => regex.IsMatch(u);
                }

                // İçerik filtreleme fonksiyonu oluştur
                Func<string, bool>? contentFilter = null;
                if (!string.IsNullOrEmpty(contentPattern))
                {
                    var regex = new System.Text.RegularExpressions.Regex(contentPattern);
                    contentFilter = (c) => regex.IsMatch(c);
                }

                var results = await _webScraperService.ScrapeWebsiteAsync(url, selector, maxDepth, maxPages, urlFilter, contentFilter);
                return Ok(new { url, selector, maxDepth, maxPages, urlPattern, contentPattern, pageCount = results.Count, results });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Web sitesi kazınırken hata oluştu: {Url}", url);
                return StatusCode(500, new { error = ex.Message });
            }
        }

        /// <summary>
        /// Playwright tarayıcısını başlatır
        /// </summary>
        /// <returns>Başlatma işlemi sonucu</returns>
        [HttpPost("initialize")]
        public async Task<IActionResult> InitializeBrowser()
        {
            try
            {
                _logger.LogInformation("Tarayıcı başlatma isteği alındı");
                var result = await _webScraperService.InitializeBrowserAsync();
                return Ok(new { success = result });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Tarayıcı başlatılırken hata oluştu");
                return StatusCode(500, new { error = ex.Message });
            }
        }

        /// <summary>
        /// Playwright tarayıcısını kapatır
        /// </summary>
        /// <returns>Kapatma işlemi sonucu</returns>
        [HttpPost("close")]
        public async Task<IActionResult> CloseBrowser()
        {
            try
            {
                _logger.LogInformation("Tarayıcı kapatma isteği alındı");
                var result = await _webScraperService.CloseBrowserAsync();
                return Ok(new { success = result });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Tarayıcı kapatılırken hata oluştu");
                return StatusCode(500, new { error = ex.Message });
            }
        }
    }
}