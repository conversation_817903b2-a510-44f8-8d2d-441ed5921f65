{"type":"entity","name":"TranslationAgentServer","entityType":"project","observations":["`Project` modeli, `IProjectService`, `ProjectService` ve `ProjectController` sınıfları oluşturuldu.",".NET 9.0 Web API projesi","Render.com deployment desteği","Session tabanlı kimlik doğrulama sistemi","Google Sheets API entegrasyonu","Modüler klasör yapısı (Controllers, Services, Interfaces, Models, Configurations)","Docker containerization desteği","Health check endpoint'leri","RESTful API tasarımı","Catalyst NLP kütüphanesi entegrasyonu tamamlandı","INlpService arayüzü oluşturuldu","NlpService implementasyonu eklendi","NlpController API endpoint'leri eklendi","NlpModels model sınıfları oluşturuldu","TextProcessingHelper'a NLP servisi entegrasyonu yapıldı","Program.cs'de DI container'a NLP servisi eklendi","NLP servisi arka planda başlatılacak şekilde yapılandırıldı","API endpoint'leri: /api/nlp/status, /api/nlp/lemmatize, /api/nlp/analyze, /api/nlp/process-multiple, /api/nlp/initialize","Gemini AI servisi başarıyla eklendi","mscraftsman/generative-ai kütüphanesi projeye entegre edildi","GeminiService sadece servis olarak eklendi, controller eklenmedi","Generate content fonksiyonu model ve ince ayar seçimi ile eklendi","Generate embedding fonksiyonu tekli ve çoklu metin için ayrı olarak eklendi","Dönen yapı gelen mesajı döndürüyor","Proje başarıyla build edildi","Context modeli, servisi ve kontrolcüsü başarıyla eklendi","Program.cs dosyasına ContextService DI kaydı eklendi","Context API endpoint'leri Program.cs'e eklendi","Tüm derleme hataları çözüldü ve proje başarıyla derlendi","ContextService Supabase API kullanımı düzeltildi","Yeni CreateTerimceSheet endpoint'i başarıyla GoogleSheetsController.cs dosyasına eklendi","Endpoint Supabase'den terms_columns verisini alıp parse ederek Terimce sayfası oluşturuyor","Program.cs dosyasında ana sayfa endpoint'ine createTerimceSheet eklendi","Proje başarıyla derlendi, sadece Information/Warning seviyesinde sorunlar var","CreateTerimceSheet endpoint'ine sayfa varlık kontrolü eklendi","Endpoint artık aynı isimde sayfa varsa Conflict (409) döndürüyor","Proje başarıyla derlendi, hiç ERROR seviyesinde sorun yok","Context yönetimi sistemi eklendi","Gemini AI entegrasyonu tamamlandı","NLP servisi Catalyst ile entegre edildi","Embedding tabanlı arama özellikleri","Full-text search implementasyonu","Şema tabanlı çoklu proje desteği","Helper sınıfları ile yardımcı fonksiyonlar","Modüler ve genişletilebilir mimari","Retry mekanizması başarıyla eklendi","RetryHelper sınıfı ile HTTP istekleri için otomatik retry desteği","GeminiService.GetEmbeddingsAsync metoduna retry mekanizması entegre edildi","TextService.CreateTextsAsync metodundaki batch embedding işlemine retry desteği eklendi","Too Many Requests (429) hatalarına karşı exponential backoff ile retry","Maksimum 3 deneme, 2 saniye temel bekleme süresi","Rate limiting için batch'ler arası bekleme süresi 3.5 saniyeden 1.5 saniyeye düşürüldü","Proje başarıyla derlendi, ERROR seviyesinde hiç sorun yok","Dinamik şema desteği başarıyla implement edildi","ApplicationDbContext'te Projects, Processes, MainData tabloları public şemada sabit","DynamicSchemaDbContext oluşturuldu - dinamik şema desteği için","IDatabaseService'e CreateDynamicContext ve CreateNewDynamicContext metotları eklendi","DatabaseService'e dinamik şema desteği eklendi","TermService, TextService, ContextService projeye göre dinamik şema desteği ile çalışıyor","Controller'larda projectID parametresi ile dinamik şema erişimi sağlanıyor","PostgreSQL enum hatası çözüldü - ProcessStatus ve ProcessTaskType enum'ları string conversion ile yapılandırıldı","ApplicationDbContext.cs'te HasColumnType yerine HasConversion<string>() kullanılarak enum'lar string olarak saklanacak şekilde düzenlendi","Npgsql.PostgresException '42883: operator does not exist: ProcessStatus = integer' hatası giderildi","PostgreSQL enum hatası çözüldü: ApplicationDbContext.cs'te HasConversion kaldırıldı ve Program.cs'te Npgsql enum mapping eklendi","ProcessStatus ve ProcessTaskType enum'ları artık PostgreSQL'deki enum tiplerle doğrudan eşleşiyor","Proje başarıyla derlendi ve ERROR seviyesinde hiç sorun yok","PostgreSQL enum hatası tamamen çözüldü - ApplicationDbContext.cs'te Process enum'ları için HasColumnType yapılandırması eklendi","ProcessStatus ve ProcessTaskType enum'ları artık PostgreSQL enum tipleriyle doğru şekilde eşleşiyor","ProcessService.cs'te Process ekleme işlemi artık hatasız çalışıyor","Supabase servisi kaldırıldı, yerine PostgreSQL tabanlı DatabaseService eklendi","Entity Framework Core ile PostgreSQL veritabanı entegrasyonu","IDatabaseService ve DatabaseService ile modüler veritabanı yönetimi","Npgsql ve Pgvector desteği ile PostgreSQL native özellikleri","Raw SQL sorgu çalıştırma ve dinamik şema yönetimi","ApplicationDbContext ve DynamicSchemaDbContext ile çift katmanlı veritabanı yapısı","PostgreSQL stored procedure desteği","Text servisi endpoint tutarsızlıkları giderildi","Program.cs'deki text endpoint'leri ProjectId ile güncellendi","TextController'a eksik bulk create endpoint'i eklendi","Term servisi endpoint tutarsızlıkları giderildi","Program.cs'deki term endpoint'leri ProjectId ile güncellendi","TermController'a eksik bulk create ve bulk update endpoint'leri eklendi","Hem Text hem Term servisleri için endpoint tutarsızlıkları giderildi","Context servisi için endpoint tutarsızlıkları giderildi ve eksik endpoint'ler eklendi.","TextTranslations ve TermTranslations modelleri başarıyla oluşturuldu","Models klasörüne read-only çeviri sonuçları için yeni modeller eklendi","DynamicSchemaDbContext.cs'e TextTranslations ve TermTranslations DbSet'leri eklendi","project_32 şemasındaki text_translations ve term_translations tablolarına Entity Framework mapping'i yapıldı","Çeviri sonuçları için read-only veri erişimi sağlandı","AI model sonuçları, prompt'lar, token sayıları ve işlenme tarihleri takip edilebilir","JsonDocument kullanarak JSONB veri tipine destek eklendi","TextWithTranslations ve TermWithTranslations DTO modelleri oluşturuldu","TextController ve TermController'daki GetById endpoint'leri çeviri verilerini de döndürecek şekilde güncellendi","ITextService ve ITermService arayüzlerine GetWithTranslationsAsync metodları eklendi","TextService ve TermService'e çeviri verileriyle birlikte veri getiren metodlar implement edildi","Process tablosuna settings sütunu eklendi (jsonb tipinde)","Process.cs modelinde Settings özelliği eklendi","ProcessCreateDto ve ProcessUpdateDto sınıflarına Settings özelliği eklendi","ApplicationDbContext.cs'de Process entity yapılandırmasına settings sütunu eklendi","ProcessService.cs'de CreateAndStartProcessAsync ve UpdateProcessAsync metodlarında Settings desteği eklendi","Settings özelliği Dictionary<string, object>? türüne çevrildi","Proje başarıyla derlendi ve hata yok","Process tablosundaki settings sütunu tamamen projeye entegre edildi","Successfully implemented filtering methods for all services (TextService, TermService, ContextService, ProjectService, ProcessService)","Added three overloads for each service: FilterQuery object, JSON string, and JsonElement parameters","Fixed static FilteringService usage errors across all services","Corrected IProcessService interface to include projectId parameter in all filtering methods","Project compiles successfully with only warnings (no errors)","Tüm controller'lara filtreleme endpoint'leri başarıyla eklendi","TextController, TermController, ContextController, ProjectController ve ProcessController'a POST /filter ve POST /filter/json endpoint'leri eklendi","ProjectController'a ayrıca proje işlemleri için filtreleme endpoint'leri eklendi","Proje başarıyla derlendi, sadece uyarılar var","Program.cs'taki enum hataları düzeltildi: TextStatus.Pending -> TextStatus.EN, ProcessStatus.Running -> ProcessStatus.InProgress, ProcessTaskType.Translation -> ProcessTaskType.TextTranslation","System.Text.Json using direktifi geri eklendi","Proje başarıyla derlendi, sadece 48 uyarı var (hiç ERROR yok)","TestFilteringExamples metodu başarıyla çalışıyor ve filtreleme örnekleri startup'ta gösteriliyor","PostgreSQL JSONB hatası çözüldü - ApplicationDbContext.cs'te Project ve Process entity'lerinin Settings özelliklerine HasConversion eklendi","Dictionary<string, object> tipindeki Settings özelliklerinin JSON string'e dönüştürülmesi için value converter implementasyonu eklendi","System.Text.Json.JsonSerializer kullanarak JSONB ile uyumlu hale getirildi","Proje başarıyla derlendi, sadece dosya kilitleme uyarıları var","Dictionary<string, object> tipindeki Settings özelliklerini JsonDocument tipine dönüştürme işlemi başarıyla tamamlandı","Project.cs, Process.cs modellerindeki Settings özelliklerinin tipleri JsonDocument? olarak güncellendi","ProjectCreateDto, ProjectUpdateDto, ProcessCreateDto, ProcessUpdateDto sınıflarındaki Settings özelliklerinin tipleri JsonDocument? olarak güncellendi","ProjectService.cs'te Settings kullanımı JsonDocument.Parse ile uyumlu hale getirildi","ProcessWorker.cs'te Settings kullanımları JsonDocument.RootElement.TryGetProperty ile güncellendi","JsonDocument kullanımı ile JSONB veri tipine tam uyumluluk sağlandı","JsonDocument Settings özelliklerinin serialize sorunu çözüldü","JsonDocumentConverter sınıfı oluşturuldu - JsonDocument'ı düzgün serialize/deserialize eder","Process.cs ve Project.cs modellerindeki Settings özelliklerine JsonConverter attribute'u eklendi","Settings verisi artık sadece tür bilgisi değil, gerçek JSON içeriğini döndürüyor","Newtonsoft.Json ile System.Text.Json arasında uyumlu dönüştürme sağlandı","API response'larında Settings verisi düzgün görüntüleniyor","Docker container'da Playwright desteği eklendi","Ubuntu tabanlı .NET runtime image (mcr.microsoft.com/dotnet/aspnet:9.0-jammy) kullanılıyor","Playwright için gerekli sistem bağımlılıkları (webkit, chromium dependencies) yükleniyor","PowerShell Core 7.4.0 container'a yükleniyor","Playwright CLI ve tarayıcıları container build sırasında yükleniyor","WebScraperService Docker container ortamı için optimize edildi","Browser launch options'a --no-sandbox, --disable-setuid-sandbox gibi container-safe parametreler eklendi","docker-compose.yml dosyası oluşturuldu","Playwright browser cache için volume desteği eklendi",".dockerignore dosyası Playwright dosyaları için güncellendi","Playwright kurulum stratejisi değiştirildi: PowerShell yerine Node.js/npm kullanılıyor","Docker container'da Playwright CLI npm ile global olarak yükleniyor","Sadece Chromium tarayıcısı ve bağımlılıkları yükleniyor (optimize edilmiş)","Node.js 18.x LTS sürümü container'a eklendi","Playwright kurulum hatası çözüldü","Docker container'da Playwright sistem bağımlılıkları eklendi","libgbm1 paketi Dockerfile'a eklenerek Playwright host validation uyarısı çözüldü","Node.js ve Playwright kurulum adımları optimize edildi","Playwright tarayıcıları artık tüm gerekli sistem bağımlılıklarıyla birlikte yükleniyor","Playwright kurulum stratejisi .NET CLI kullanacak şekilde değiştirildi","Node.js bağımlılığı kaldırıldı, Microsoft.Playwright.CLI global tool olarak yüklendi","Playwright tarayıcıları .NET uygulaması ile uyumlu yolda kurulacak şekilde yapılandırıldı","PLAYWRIGHT_BROWSERS_PATH environment variable'ı /root/.cache/ms-playwright olarak ayarlandı","Gerekli sistem bağımlılıkları (libnss3, libatk-bridge2.0-0, libdrm2, vb.) eklendi","Docker container'da Playwright executable path sorunu çözüldü","Docker container'da .NET SDK sorunu çözüldü","Runtime stage'de .NET SDK image (mcr.microsoft.com/dotnet/sdk:9.0) kullanılıyor","Playwright CLI kurulumu için gerekli .NET SDK desteği eklendi","Docker build hatası 'No .NET SDKs were found' giderildi","Multi-stage build yapısı korunarak Playwright desteği sağlandı","Playwright CLI proje bulunamama sorunu çözüldü","Playwright kurulumu proje dosyaları kopyalanmadan önce yapılıyor","Playwright CLI tam yol (/root/.dotnet/tools/playwright) ile çağrılıyor","Docker build sırasında Playwright tarayıcı kurulumu optimize edildi","Proje bağımlılığı olmadan Playwright CLI kurulumu sağlandı","Playwright CLI'a --force parametresi eklendi","Docker build sırasında proje bağımlılığı olmadan Playwright tarayıcı kurulumu sağlandı","playwright install chromium --force komutu ile 'Couldn't find project using Playwright' hatası çözüldü","Playwright CLI artık proje dosyaları olmadan da tarayıcı kurulumu yapabiliyor","Context sınıfı project_35 şemasına göre güncellendi - category ve title nullable yapıldı, scrap_id alanı eklendi","WebScrap modeli webscraps tablosuna göre oluşturuldu","DynamicSchemaDbContext'e WebScrap DbSet'i eklendi ve model yapılandırması tamamlandı","Supabase project_35 şeması entegrasyonu başarıyla tamamlandı","WebScrap modeli gerçek webscraps tablo yapısına göre düzeltildi - sadece id, url, content, created_at alanları","DynamicSchemaDbContext WebScrap yapılandırması gerçek tablo yapısına göre güncellendi","Supabase project_35 şeması entegrasyonu başarıyla tamamlandı ve doğrulandı"]}
{"type":"entity","name":"Models","entityType":"folder","observations":["Veri modellerini barındıran klasör","Project - proje veri modeli","Process - işlem veri modeli","LoginRequest, LoginResponse - kimlik doğrulama modelleri","MainData - sistem ana veri modeli","GoogleSheetsModels - Google Sheets veri modelleri","Context - context veri modeli ve DTO'ları","GeminiModels - Gemini AI veri modelleri","TextAnalysisResult - NLP analiz sonuçları","ContextStatistics - context istatistikleri","Embedding desteği olan modeller","TextTranslations - metin çevirilerinin AI sonuçları modeli","TermTranslations - terim çevirilerinin AI sonuçları modeli","Read-only çeviri geçmişi ve analiz verileri","AI model adı, prompt, sonuç ve token takibi","JsonDocument ile JSONB desteği","TextWithTranslations.cs dosyası oluşturuldu - Text ve TextTranslations verilerini birleştiren DTO","TermWithTranslations.cs dosyası oluşturuldu - Term ve TermTranslations verilerini birleştiren DTO"]}
{"type":"entity","name":"Configurations","entityType":"folder","observations":["Uygulama konfigürasyon sınıfları","Options pattern implementasyonu","appsettings.json ile bağlantılı","Environment-specific ayarlar","Environment-specific konfigürasyonlar"]}
{"type":"entity","name":"CreateSchemaResult","entityType":"Model","observations":["Schema_Created ve Tables_Created alanları eklendi","Yeni JSON formatına uygun olarak güncellendi: {\"message\":\"...\",\"success\":true,\"schema_created\":true,\"tables_created\":[...]}","Created alanı Schema_Created olarak yeniden adlandırıldı","Tables_Created alanı List<string> tipinde eklendi","Success, Message, Schema_Created ve Tables_Created özelliklerini içerir"]}
{"type":"entity","name":"Catalyst","entityType":"NLP Library","observations":["curiosity-ai/catalyst kütüphanesi context7 ile araştırıldı","Catalyst.Models.English paketleri projede zaten mevcuttu","NLP pipeline'ı için English modeli kullanılıyor","Lemmatization, text analysis ve multiple text processing özellikleri eklendi","Pipeline statik fonksiyonlarda kullanılabilir hale getirildi"]}
{"type":"entity","name":"Text","entityType":"Model","observations":["Represents the text data model.","Kapsamlı filtreleme sistemi başarıyla uygulandı","TextFilterQuery sınıfı ile Query Object Pattern kullanılıyor","Tüm Text alanları için filtreleme desteği eklendi","HTTP ve veritabanı JSON kaynaklarından filtreleme destekleniyor"]}
{"type":"entity","name":"ITextService","entityType":"Interface","observations":["Defines the contract for text management services.","Includes methods for CRUD operations and other text-related functionalities.","Sayfalama parametreleri (page, pageSize) tüm liste döndüren metot imzalarına eklendi","GetAllTextsAsync, GetTextsBySheetIdAsync, GetTextsByNamespaceAsync, GetTextsByStatusAsync, SearchTextsAsync metotlarına sayfalama parametreleri eklendi","Varsayılan değerler: page = 1, pageSize = 50","XML dokümantasyon yorumları güncellendi"]}
{"type":"entity","name":"TextService","entityType":"Service","observations":["Implements the `ITextService` interface.","Handles the business logic for text operations.","Sayfalama parametreleri (page, pageSize) tüm liste döndüren metotlara eklendi","GetAllTextsAsync, GetTextsBySheetIdAsync, GetTextsByNamespaceAsync, GetTextsByStatusAsync, SearchTextsAsync metotlarına sayfalama desteği eklendi","Supabase Range() metodu kullanılarak offset ve limit hesaplaması yapılıyor","Varsayılan sayfa boyutu 50, sayfa numarası 1'den başlar","Log mesajlarına sayfa ve boyut bilgileri eklendi","GetAllTextsAsync metoduna Namespace, Key, En, Tr ve Status alanlarına göre filtreleme özelliği eklendi","Filtreleme, veritabanından veri alındıktan sonra LINQ ile uygulanıyor","Status filtresi enum değerine göre, diğer filtreler Contains metodu ile çalışıyor","Proje başarıyla build edildi ve filtreleme özelliği çalışır durumda","CreateTextsAsync metodundaki batch embedding işlemine retry eklendi","Her batch için ayrı retry mekanizması","Batch'ler arası bekleme süresi 1.5 saniyeye optimize edildi","Rate limiting ve retry ile daha güvenilir embedding üretimi","PostgreSQL veritabanı ile IDatabaseService üzerinden etkileşim kurar","Yeni GetAllTextsAsync overload eklendi","TextFilterQuery desteği eklendi","Geriye dönük uyumluluk korundu","FindSimilarTextsAsync metodundaki ambiguity hatası çözüldü"]}
{"type":"entity","name":"TextController","entityType":"Controller","observations":["Provides RESTful API endpoints for text management.","Uses the `ITextService` to perform text operations.","Sayfalama parametreleri (page, pageSize) tüm liste döndüren endpoint'lere eklendi","GetAllTexts, GetTextsBySheetId, GetTextsByNamespace, GetTextsByStatus, SearchTexts metotlarına sayfalama desteği eklendi","Sayfa numarası 1'den başlar, varsayılan sayfa boyutu 50","Maksimum sayfa boyutu 1000 ile sınırlandırıldı","Query parametreleri olarak [FromQuery] ile alınıyor","Program.cs'deki text endpoint'leri ProjectId parametresi ile güncellendi","Eksik olan toplu metin oluşturma (bulk create) endpoint'i eklendi","TextController artık ITextService interface'i ile tam uyumlu","Tüm endpoint'ler {projectId} parametresi kullanıyor","CreateTexts bulk endpoint'i POST {ProjectId}/bulk olarak eklendi","GetTextById endpoint'i TextWithTranslations döndürecek şekilde güncellendi","Artık metin verisiyle birlikte ilgili çeviri geçmişini de döndürüyor","Yeni filtreleme endpoint'leri eklendi","POST ve GET metodları ile filtreleme desteği","JSON query string desteği"]}
{"type":"entity","name":"TermController","entityType":"Controller","observations":["TermController başarıyla TextController'ın endpoint yapısına benzer şekilde düzenlendi","Tüm endpoint'ler {schemaName} parametresi ile başlayacak şekilde güncellendi","ITermService arayüzündeki metotlara uygun olarak schemaName parametreleri eklendi","Endpoint'ler: GET {schemaName}, GET {schemaName}/{id}, GET {schemaName}/sheet/{sheetId}, GET {schemaName}/status/{status}, GET {schemaName}/search, GET {schemaName}/fulltext-search, GET {schemaName}/find/english/{englishTerm}, GET {schemaName}/find/turkish/{turkishTerm}, GET {schemaName}/find/lemma/{lemma}, GET {schemaName}/untranslated, GET {schemaName}/without-embedding, GET {schemaName}/statistics, POST {schemaName}/similar, POST {schemaName}, POST {schemaName}/bulk, PUT {schemaName}/{id}, DELETE {schemaName}/{id}, DELETE {schemaName}/bulk, DELETE {schemaName}/sheet/{sheetId}, PUT {schemaName}/{id}/embedding, PUT {schemaName}/embeddings/bulk","Proje başarıyla build oldu, hiç hata yok","Program.cs dosyasındaki terms endpoint'leri TermController'ın yeni schemaName parametreli yapısına uygun olarak güncellendi","Tüm endpoint'ler {schemaName} parametresi ile başlayacak şekilde düzenlendi","Program.cs'deki term endpoint'leri ProjectId parametresi ile güncellendi","Eksik olan toplu terim oluşturma (bulk create) endpoint'i eklendi","Eksik olan toplu terim güncelleme (bulk update) endpoint'i eklendi","TermController artık ITermService interface'i ile tam uyumlu","Tüm endpoint'ler {projectId} parametresi kullanıyor","CreateTerms bulk endpoint'i POST {projectID}/bulk olarak eklendi","UpdateTerms bulk endpoint'i PUT {projectID}/bulk olarak eklendi","TermUpdateRequest yardımcı sınıfı eklendi","Comment'ler 'şema' yerine 'proje' olarak güncellendi","GetTermById endpoint'i TermWithTranslations döndürecek şekilde güncellendi","Artık terim verisiyle birlikte ilgili çeviri geçmişini de döndürüyor"]}
{"type":"entity","entityType":"Service","name":"ContextService","observations":["ContextService.cs dosyasındaki Supabase API kullanım hataları düzeltildi","Constants.Ordering.Descending kullanımları Supabase.Postgrest.Constants.Ordering.Descending olarak güncellendi","Or() metodu Where() ile Contains() kullanımına çevrildi","TextSearch() metodu Where() ile Contains() kullanımına çevrildi","Not() metodu Where() ile null kontrolüne çevrildi","Proje başarıyla derlendi ve hiç hata kalmadı"]}
{"type":"entity","name":"ContextController","entityType":"Controller","observations":["ContextController'daki tüm endpoint'lerde schemaName parametresi query parametresinden route parametresine başarıyla dönüştürüldü","Tüm HTTP metodları (GET, POST, PUT, DELETE) için route yapısı {schemaName} ile başlayacak şekilde güncellendi","Program.cs dosyasındaki context endpoint URL'leri yeni route yapısına uygun olarak güncellendi","CreateContext metodundaki CreatedAtAction çağrısı parametre sırası düzeltilerek güncellendi","Proje başarıyla derlendi ve ERROR seviyesinde problem bulunmuyor","`Program.cs` dosyasındaki context endpoint'leri `{projectId}` parametresi ile güncellendi.","Eksik toplu context güncelleme (bulk update) endpoint'i eklendi.","`IContextService` arayüzü ile tam uyumlu hale getirildi.","Tüm endpoint'ler `{projectId}` parametresi kullanıyor.","`UpdateContexts` bulk endpoint'i eklendi.","`ContextUpdateRequest` yardımcı sınıfı eklendi ve güncellendi.","Yorumlar güncellendi."]}
{"type":"entity","name":"ContextController.cs","entityType":"Dosya","observations":["`GetAllContexts` endpoint'ine `categoryFilter`, `titleFilter` ve `contentFilter` parametreleri eklenerek filtreleme özelliği uygulandı."]}
{"type":"entity","name":"ContextService.cs","entityType":"Dosya","observations":[]}
{"type":"entity","name":"TermService.cs","entityType":"Dosya","observations":["Birincil oluşturucu kullanımı, gereksiz `using` yönergesi, koleksiyon başlatma kolaylaştırması ve desen eşleştirme kullanımı ile ilgili bilgi düzeyindeki sorunlar tespit edildi."]}
{"type":"entity","name":"TermController.cs","entityType":"Dosya","observations":["Birincil oluşturucu kullanımı ve `Count` değerinin `Any()` yerine 0 ile karşılaştırılması gerektiği ile ilgili bilgi düzeyindeki sorunlar tespit edildi."]}
{"type":"entity","name":"TranslationAgentServer Projesi","entityType":"Proje","observations":["`ContextController.cs` dosyasındaki `GetAllContexts` endpoint'ine filtreleme parametreleri eklendi.","Tüm tespit edilen hatalar giderildi ve proje tutarlı hale getirildi.","Debug modunda duplicate logging sorunu çözüldü","Program.cs'te logging provider'lar temizlenip sadece console provider eklendi","builder.Logging.ClearProviders() ve builder.Logging.AddConsole() ile duplicate log sorunu giderildi","appsettings.Development.json'da Console logging konfigürasyonu eklendi"]}
{"type":"entity","name":"GoogleSheetsController","entityType":"controller","observations":["Her iki endpoint de spreadsheetId ve sheetId parametrelerini alır ve doğrulama sonucunu döner"]}
{"type":"entity","name":"ProcessBackgroundService","entityType":"Service","observations":["IHostedService arayüzünü uygulayan arka plan servisi oluşturuldu","ProcessTaskInfo sınıfı ile süreç bilgilerini yönetir","Süreçleri başlatma, durdurma, iptal etme, duraklatma, devam ettirme işlevleri","Durum ve ilerleme takibi yapabilir","Program.cs'e DI container'a eklendi","ProcessService ile entegre çalışacak şekilde tasarlandı"]}
{"type":"entity","name":"IProcessBackgroundService","entityType":"Interface","observations":["ProcessBackgroundService için arayüz oluşturuldu","StartTaskAsync, CancelTaskAsync, PauseTaskAsync, ResumeTaskAsync metotları","GetTaskStatusAsync, GetTaskProgressAsync, GetActiveTasksAsync metotları","ProcessTaskStatus enum'u tanımlandı"]}
{"type":"entity","name":"IContextService","entityType":"Interface","observations":["Context servisi için arayüz tanımlar","CRUD işlemleri için metot imzaları","Arama ve filtreleme metotları","Embedding tabanlı benzerlik arama","Toplu işlemler için metotlar","Context istatistikleri metodu","Şema tabanlı operasyonlar"]}
{"type":"entity","name":"Context","entityType":"Model","observations":["Category, Title, Content alanları","CombinedText ve CombinedTsvector otomatik alanları","Embedding vektörü desteği","CreatedAt ve UpdatedAt timestamp alanları","ContextCreateDto ve ContextUpdateDto DTO sınıfları","ContextStatistics model sınıfı"]}
{"type":"entity","name":"GeminiService","entityType":"Service","observations":["IGeminiService arayüzünü uygular","Google Gemini AI modelleri ile entegrasyon","İçerik üretimi (GenerateContentAsync)","Tekli ve çoklu embedding üretimi","Model bilgisi alma özellikleri","mscraftsman/generative-ai kütüphanesi kullanır","Sadece servis olarak eklendi, controller yok","GetEmbeddingsAsync metoduna retry mekanizması eklendi","GetEmbeddingsInternalAsync private metodu ile asıl işlem ayrıştırıldı","RetryHelper.ExecuteWithRetryAsync kullanarak otomatik retry","3 deneme, 2 saniye temel bekleme süresi","Too Many Requests hatalarına karşı korumalı","GenerateContentAsync fonksiyonuna RetryHelper özelliği başarıyla eklendi","Fonksiyon artık retry mekanizması ile çalışıyor (3 deneme, 2 saniye temel bekleme)","GenerateContentInternalAsync private metodu oluşturuldu","IsRetryableError metodu eklendi - HTTP ve timeout hatalarını kontrol eder","Rate limiting (429), service unavailable (503), bad gateway (502), gateway timeout (504) ve internal server error (500) hatalarına karşı retry desteği","Retry edilemeyecek hatalar için direkt response döndürülüyor","GetEmbeddingsAsync metodundaki retry sistemi ile aynı yapıda implement edildi","GenerateEmbeddingAsync fonksiyonuna da RetryHelper özelliği eklendi","GetEmbeddingsInternalAsync metoduna IsRetryableError kontrolü eklendi","Tüm retry mekanizmaları 3 deneme ve 2000ms base delay ile çalışıyor","HTTP 429, 500, 502, 503, 504 hataları ve timeout durumlarında retry yapılıyor","`GetEmbeddingAsync` fonksiyonuna `RetryHelper` özelliği eklendi.","`GetEmbeddingInternalAsync` metoduna `IsRetryableError` kontrolü eklendi."]}
{"type":"entity","name":"IGeminiService","entityType":"Interface","observations":["Gemini AI servisi için arayüz tanımlar","İçerik üretimi metotları","Embedding üretimi metotları","Model bilgisi alma metotları","Servis başlatma metodu"]}
{"type":"entity","name":"GeminiModels","entityType":"Model","observations":["Gemini AI servisi için veri modelleri","GeminiContentRequest - içerik üretim isteği","GeminiContentResponse - içerik üretim yanıtı","GeminiEmbeddingResponse - tekli embedding yanıtı","GeminiMultipleEmbeddingResponse - çoklu embedding yanıtı","GeminiModel - model bilgisi","Temperature, TopP, TopK parametreleri","JSON çıktı formatı desteği","Google Search grounding desteği"]}
{"type":"entity","name":"NlpService","entityType":"Service","observations":["INlpService arayüzünü uygular","Catalyst NLP kütüphanesi kullanır","Lemmatization işlemleri","Detaylı metin analizi","Pipeline başlatma ve hazırlık","TextAnalysisResult döndürür","Arka planda başlatılacak şekilde yapılandırılmış"]}
{"type":"entity","name":"INlpService","entityType":"Interface","observations":["NLP servisi için arayüz tanımlar","Pipeline başlatma metodu","Lemmatization metodu","Metin analizi metodu","Pipeline durumu kontrolü"]}
{"type":"entity","name":"NlpController","entityType":"Controller","observations":["NLP işlemleri için RESTful API endpoint'leri","Status kontrolü endpoint'i","Lemmatization endpoint'i","Metin analizi endpoint'i","Çoklu metin işleme endpoint'i","Pipeline başlatma endpoint'i","API endpoint'leri: /api/nlp/status, /api/nlp/lemmatize, /api/nlp/analyze, /api/nlp/process-multiple, /api/nlp/initialize"]}
{"type":"entity","name":"TextAnalysisResult","entityType":"Model","observations":["Metin analiz sonuçlarını temsil eder","IsSuccess, ErrorMessage, ErrorDetails alanları","OriginalText, CharacterCount, WordCount, SentenceCount","LemmatizedText alanı","NLP servisi tarafından döndürülür"]}
{"type":"entity","name":"EmbeddingHelper","entityType":"Helper","observations":["Embedding oluşturma işlemleri için yardımcı sınıf","Tekli ve çoklu embedding üretimi","small-embedding fonksiyonunu çağırır","Cosine benzerlik hesaplama"]}
{"type":"entity","name":"TextProcessingHelper","entityType":"Helper","observations":["Metin işleme için yardımcı fonksiyonlar","Stopword temizleme","Lemmatization işlemleri","NLP servisi entegrasyonu","Async ve sync metotlar","Metin kısaltma fonksiyonu","Catalyst Pipeline desteği"]}
{"type":"entity","name":"Helpers","entityType":"folder","observations":["Yardımcı fonksiyonları barındıran klasör","EmbeddingHelper - embedding oluşturma işlemleri","TextProcessingHelper - metin işleme fonksiyonları","NLP servisi entegrasyonu","Cosine benzerlik hesaplama","Stopword temizleme ve lemmatization"]}
{"type":"entity","name":"Controllers Folder","entityType":"Folder","observations":["API endpoint'lerini barındıran klasör","8 adet controller dosyası içerir","AuthController - kimlik doğrulama endpoint'leri","ContextController - context CRUD işlemleri","GoogleSheetsController - Google Sheets API işlemleri","HealthController - sistem durumu kontrolü","ProcessController - background işlem yönetimi","ProjectController - proje CRUD işlemleri","TermController - terim yönetimi","TextController - metin yönetimi","RESTful API tasarım prensiplerine uygun"]}
{"type":"entity","name":"Services Folder","entityType":"Folder","observations":["İş mantığını barındıran servis sınıfları","10 adet servis dosyası içerir","AuthService - kimlik doğrulama işlemleri","ContextService - context CRUD işlemleri","GeminiService - AI içerik üretimi ve embedding","GoogleSheetsService - Google Sheets API işlemleri","NlpService - doğal dil işleme operasyonları","ProcessService - background işlem yönetimi","ProjectService - proje CRUD işlemleri","TermService - terim yönetimi","TextService - metin yönetimi","Dependency injection pattern kullanımı","DatabaseService - PostgreSQL veritabanı yönetimi"]}
{"type":"entity","name":"Interfaces Folder","entityType":"Folder","observations":["Servis arayüzlerini barındıran klasör","10 adet interface dosyası içerir","Loose coupling ve testability sağlar","Contract-based programming yaklaşımı","IAuthService, IContextService, IDatabaseService","IGeminiService, IGoogleSheetsService, INlpService","IProcessService, IProjectService, ITermService, ITextService"]}
{"type":"entity","name":"Models Folder","entityType":"Folder","observations":["Veri modellerini barındıran klasör","12 adet model dosyası içerir","Context.cs - context veri modeli","CreateSchemaResult.cs - şema oluşturma sonucu","GeminiModels.cs - Gemini AI modelleri","GoogleSheetsModels.cs - Google Sheets veri modelleri","LoginRequest.cs, LoginResponse.cs - kimlik doğrulama modelleri","MainData.cs - ana sistem verileri","Process.cs - background işlem modeli","Project.cs - proje veri modeli","Term.cs - terim veri modeli","Text.cs - metin veri modeli","TextAnalysisResult.cs - metin analiz sonuçları"]}
{"type":"entity","name":"Helpers Folder","entityType":"Folder","observations":["Yardımcı sınıfları barındıran klasör","2 adet helper dosyası içerir","EmbeddingHelper.cs - embedding işlemleri","TextProcessingHelper.cs - metin işleme operasyonları","NLP servisi entegrasyonu","Statik yardımcı metodlar"]}
{"type":"entity","name":"Configurations Folder","entityType":"Folder","observations":["Yapılandırma sınıflarını barındıran klasör","Options pattern implementasyonu","Dependency injection ile yapılandırma"]}
{"type":"entity","name":"Middleware Folder","entityType":"Folder","observations":["Özel middleware'leri barındıran klasör","AuthenticationMiddleware.cs - kimlik doğrulama middleware'i","Session validation","Public endpoint'ler tanımlaması","401 Unauthorized response yönetimi"]}
{"type":"entity","name":"Authentication System","entityType":"Feature","observations":["Session tabanlı kimlik doğrulama","Tek şifre ile giriş sistemi","30 dakika session süresi","Cookie tabanlı session yönetimi","HttpOnly, Secure, SameSite cookie ayarları","Thread-safe session yönetimi","ConcurrentDictionary ile aktif session'lar","Otomatik expired session temizleme","Public endpoint'ler: /health, /api/auth/*, /swagger"]}
{"type":"entity","name":"Google Sheets Integration","entityType":"Feature","observations":["Google Sheets API v4 entegrasyonu","Service account JSON kimlik doğrulama","Spreadsheet ve worksheet yönetimi","Veri okuma ve yazma işlemleri","Batch operations desteği","Satır ekleme, silme ve güncelleme","Header validation işlevselliği","'#|Namespace|Key|EN|TR|EN-TR' formatı desteği","HeaderValidationResult modeli","Production-ready durum"]}
{"type":"entity","name":"Gemini AI Integration","entityType":"Feature","observations":["mscraftsman/generative-ai kütüphanesi entegrasyonu","Generate content fonksiyonu","Model ve ince ayar seçimi","Generate embedding fonksiyonu","Tekli ve çoklu metin embedding","AI destekli içerik üretimi"]}
{"type":"entity","name":"NLP Integration","entityType":"Feature","observations":["Catalyst NLP kütüphanesi entegrasyonu","Doğal dil işleme operasyonları","Lemmatization işlemleri","Text analysis özellikleri","Multiple text processing","Background service olarak çalışma","API endpoint'leri: /api/nlp/status, /api/nlp/lemmatize, /api/nlp/analyze, /api/nlp/process-multiple, /api/nlp/initialize"]}
{"type":"entity","name":"Background Process System","entityType":"Feature","observations":["Background task yönetimi","Process durumu takibi (Running, Paused, Completed, Failed)","Task type desteği (Translation, Analysis, Export, Import)","Real-time işlem durumu","Resume, pause, cancel işlemleri","Active task listesi","Progress tracking","Background service entegrasyonu"]}
{"type":"entity","name":"Database Schema","entityType":"Feature","observations":["projects tablosu - proje bilgileri","main tablosu - sistem ayarları ve şifre","processes tablosu - background işlem durumları","contexts tablosu - context verileri","terms tablosu - terim verileri","texts tablosu - metin verileri","Embedding vektör desteği","Full-text search özellikleri","Row Level Security (RLS) politikaları","Real-time özellikler","REST API desteği","PostgreSQL veritabanı","Entity Framework Core ile yönetilen şema yapısı","Npgsql ve Pgvector desteği"]}
{"type":"entity","name":"Deployment Configuration","entityType":"Feature","observations":["Docker multi-stage build","mcr.microsoft.com/dotnet/sdk:9.0 build image","mcr.microsoft.com/dotnet/aspnet:9.0 runtime image","Port 8080 expose","Render.com deployment yapılandırması","render.yaml konfigürasyonu","Auto-deploy aktif","Health check path: /health","Free plan konfigürasyonu","Environment variables yönetimi"]}
{"type":"entity","name":"ProcessService","entityType":"Service","observations":["ExecuteTextTranslationTask fonksiyonu Google Sheets ve Text servisleri ile entegre edildi","Google Sheets'ten veri çekme ve toplu metin oluşturma işlevselliği eklendi","IProjectService ve ITextService bağımlılıkları eklendi","GoogleSheetsApiResponse.Success property'si kullanılarak hata düzeltildi","Process task'larını yöneten ana servis sınıfı","TaskType'a göre ayar filtreleme sistemi başarıyla eklendi","FilterSettingsByTaskType metodu ile proje ayarları işlem türüne göre filtreleniyor","TermsDetection: TermDetectModel -> Model, TermDetectSendCount -> SendCount, TermDetectWorkerCount -> WorkerCount, TermDetectIncludeNamespaceAndKey -> IncludeNamespaceAndKey","TermsTranslation: TermTranslateModel -> Model, TermTranslateWorkerCount -> WorkerCount, TermTranslateIncludeNamespaceAndKey -> IncludeNamespaceAndKey","TextTranslation: TextStrategyModel -> StrategyModel, TextTranslateModel -> TranslateModel, TextTranslateHighModel -> HighQualityModel, TextTranslateWorkerCount -> WorkerCount, TextTranslateIncludeNamespaceAndKey -> IncludeNamespaceAndKey","ProjectCreation ve ExportToGoogleSheets için tüm ayarlar korunuyor","GetJsonElementValue yardımcı metodu ile JsonElement değerleri uygun C# tiplerine dönüştürülüyor","Hata durumunda orijinal proje ayarları korunuyor","Process oluşturulurken Settings null ise proje ayarları TaskType'a göre filtrelenerek kullanılıyor"]}
{"type":"entity","name":"RetryHelper","entityType":"Helper Class","observations":["HTTP istekleri için retry mekanizması sağlayan static helper sınıf","ExecuteWithRetryAsync generic metodu ile herhangi bir async operasyonu retry edebilir","429, 500, 502, 503, 504 HTTP hata kodları için retry desteği","Exponential backoff algoritması ile bekleme süreleri","Jitter eklenerek thundering herd problemini önler","Maksimum 30 saniye bekleme süresi sınırı","Detaylı loglama ile retry süreçlerini takip eder","Debug modunda duplicate logging sorunu çözüldü - RetryHelper'daki intermediate loglar kaldırıldı","Artık sadece ana servislerde (GeminiService gibi) logging yapılıyor, RetryHelper sessiz çalışıyor","Bu sayede 'Getting embeddings' ve 'Successfully retrieved embeddings' logları tek seferde düşüyor"]}
{"type":"entity","name":"ProjectService Filtreleme ve Sayfalama","entityType":"Feature","observations":["GetAllProjectsAsync metoduna filtreleme ve sayfalama özellikleri başarıyla eklendi","IProjectService arayüzüne nameFilter, spreadsheetIdFilter, page, pageSize parametreleri eklendi","ProjectService implementasyonunda Contains filtrelemesi ve Skip/Take sayfalama uygulandı","ProjectController endpoint'ine [FromQuery] parametreleri eklendi","Sayfa boyutu maksimum 1000 ile sınırlandırıldı","Diğer servislerdeki (TextService, TermService, ContextService) pattern'e uygun olarak implement edildi","Proje başarıyla derlendi, ERROR seviyesinde hiç sorun yok","Log mesajlarına filtreleme ve sayfalama bilgileri eklendi","GetProjectProcesses metoduna filtreleme ve sayfalama özellikleri eklendi","IProcessService arayüzüne statusFilter, typeFilter, page ve pageSize parametreleri eklendi","ProcessService implementasyonunda filtreleme ve sayfalama mantığı uygulandı","ProjectController GetProjectProcesses endpoint'ine filtreleme parametreleri eklendi","Process modelinde Name özelliği olmadığı için nameFilter kaldırıldı","Sadece statusFilter ve typeFilter kullanılıyor","Proje hatasız derlendi ve çalışır durumda"]}
{"type":"entity","name":"DatabaseService","entityType":"Service","observations":["PostgreSQL veritabanı servisi implementasyonu","Entity Framework Core ile veritabanı işlemlerini yönetir","IDatabaseService arayüzünü uygular","ApplicationDbContext ve DynamicSchemaDbContext yönetimi","Raw SQL sorgusu çalıştırma özelliği","Veritabanı backup ve restore metotları (henüz implementasyona alınmamış)","Dinamik şema desteği ile proje bazlı DbContext oluşturma","Npgsql ve Pgvector desteği","Connection string yönetimi","Şema oluşturma ve yönetimi (AddSchemaAsync)","GetProjectContext metodu ile proje bazlı veritabanı erişimi","PostgreSQL stored procedure çağırma desteği"]}
{"type":"entity","name":"IDatabaseService","entityType":"Interface","observations":["PostgreSQL veritabanı servisi için arayüz","Entity Framework Core ile veritabanı işlemlerini yönetir","GetContext() - ApplicationDbContext döndürür","GetProjectContext(int projectId) - Proje bazlı DynamicSchemaDbContext döndürür","ExecuteRawSqlAsync() - Raw SQL sorgusu çalıştırır","CreateBackupAsync() - Veritabanı backup'ı alır","RestoreBackupAsync() - Backup'tan veritabanını geri yükler","CreateDynamicContext() - Belirtilen şema için dinamik DbContext oluşturur","AddSchemaAsync() - Proje adını kullanarak şema ekler","PostgreSQL ve Pgvector desteği"]}
{"type":"entity","name":"AuthService","entityType":"Service","observations":["Session tabanlı kimlik doğrulama servisi","IAuthService arayüzünü uygular","Tek şifre ile giriş sistemi","30 dakika session süresi","Cookie tabanlı session yönetimi","Thread-safe session yönetimi","ConcurrentDictionary ile aktif session'lar","Otomatik expired session temizleme"]}
{"type":"entity","name":"GoogleSheetsService","entityType":"Service","observations":["IGoogleSheetsService arayüzünü uygular","Google Sheets API v4 entegrasyonu","Service account JSON kimlik doğrulama","Spreadsheet ve worksheet yönetimi","Veri okuma ve yazma işlemleri","Batch operations desteği","Satır ekleme, silme ve güncelleme","Header validation işlevselliği"]}
{"type":"entity","name":"ProjectService","entityType":"Service","observations":["IProjectService arayüzünü uygular","Proje CRUD işlemleri","PostgreSQL veritabanı ile IDatabaseService üzerinden etkileşim","Filtreleme ve sayfalama desteği","Dinamik şema yönetimi","Proje bazlı veritabanı context'i"]}
{"type":"entity","name":"IAuthService","entityType":"Interface","observations":["Kimlik doğrulama servisi için arayüz","Login ve logout metotları","Session yönetimi metotları","Kullanıcı doğrulama metotları"]}
{"type":"entity","name":"IGoogleSheetsService","entityType":"Interface","observations":["Google Sheets servisi için arayüz","Spreadsheet okuma ve yazma metotları","Worksheet yönetimi metotları","Header validation metotları","Batch operations metotları"]}
{"type":"entity","name":"IProjectService","entityType":"Interface","observations":["Proje servisi için arayüz","CRUD işlemleri metotları","Filtreleme ve sayfalama metotları","Şema yönetimi metotları"]}
{"type":"entity","name":"IProcessService","entityType":"Interface","observations":["Process servisi için arayüz","Background işlem yönetimi metotları","Task durumu takibi metotları","Process başlatma ve durdurma metotları"]}
{"type":"entity","name":"ITermService","entityType":"Interface","observations":["Terim servisi için arayüz","CRUD işlemleri metotları","Arama ve filtreleme metotları","Embedding işlemleri metotları","Şema tabanlı operasyonlar"]}
{"type":"entity","name":"TermService","entityType":"Service","observations":["ITermService arayüzünü uygular","Terim CRUD işlemleri","PostgreSQL veritabanı ile IDatabaseService üzerinden etkileşim","Şema tabanlı operasyonlar","Embedding tabanlı arama özellikleri","Full-text search implementasyonu","Filtreleme ve sayfalama desteği","Toplu işlemler (bulk operations)"]}
{"type":"entity","name":"TextFilterHelper","entityType":"Helper Class","observations":["Expression Builder pattern ile dinamik LINQ sorguları oluşturuluyor","Reflection kullanarak OrderBy/ThenBy metodları çağrılıyor","String, Integer, Enum, DateTime ve Vector filtreleme desteği","CS0173 ve CS1973 hatalarını çözmek için Expression casting eklendi"]}
{"type":"entity","name":"ProcessWorker","entityType":"Service","observations":["Thread-safety analizi tamamlandı: _texts field'ı List<ProcessText> tipinde ve sadece lock (_texts) ile korunuyor","Mevcut lock kullanımı yeterli - _texts listesi sadece tek bir yerde (567-579 satırları) lock ile korunuyor","_texts.Count kontrolü, LINQ sorgusu ve IsUsing=true ataması aynı lock bloğu içinde yapılıyor","Ayrı bir _lockObject oluşturmaya gerek yok - mevcut lock (_texts) implementasyonu thread-safe","_texts listesi sadece okunuyor ve elemanların IsUsing property'si değiştiriliyor, liste yapısı değişmiyor","ProcessText.IsUsing property'si lock dışında da değiştirilebilir ama bu durumda race condition riski var","Önerilen iyileştirme: private readonly object _lockObject = new object() kullanımı daha iyi practice olur","ExecuteTermsDetectionTask metodunda Gemini API response parsing implementasyonu tamamlandı","JSON response'u regex ile parse eden kod eklendi - markdown code block desteği","```json``` formatındaki response'ları destekler","terms array'ini List<string> formatına dönüştürür","Hata yönetimi ve logging eklendi","System.Text.RegularExpressions using direktifi eklendi"]}
{"type":"entity","name":"WebScraperService","entityType":"Service","observations":["Playwright kütüphanesini kullanarak web scraping işlemleri yapan bir servis","IWebScraperService arayüzünü uygular","ScrapeContentAsync, ScrapeWebsiteAsync, ScrapeElementsAsync, InitializeBrowserAsync ve CloseBrowserAsync metodlarını içerir","Belirli bir derinlikte ve sayfa sayısında web sitelerinden içerik çıkarabilir","URL ve içerik filtreleme özellikleri sunar","WebScrap sınıfına uygun CRUD metodları eklendi: CreateWebScrapAsync, GetWebScrapByIdAsync, GetAllWebScrapsAsync, GetWebScrapsByUrlAsync, UpdateWebScrapAsync, DeleteWebScrapAsync, GetWebScrapStatisticsAsync, ScrapeAndSaveAsync","DynamicSchemaDbContext bağımlılığı eklendi","Entity Framework Core kullanarak veritabanı işlemleri yapabilir","URL'den içerik çıkarıp doğrudan veritabanına kaydedebilir"]}
{"type":"entity","name":"IWebScraperService","entityType":"Interface","observations":["Web scraping işlemleri için arayüz tanımı","ScrapeContentAsync, ScrapeWebsiteAsync, ScrapeElementsAsync, InitializeBrowserAsync ve CloseBrowserAsync metodlarını tanımlar","WebScraperService tarafından uygulanır","CRUD metodları için arayüz tanımları eklendi","WebScrap veritabanı işlemleri için metot imzaları içerir"]}
{"type":"entity","name":"WebScraperController","entityType":"Controller","observations":["Web scraping işlemleri için API controller'ı","IWebScraperService'i kullanarak web scraping işlemlerini API üzerinden erişilebilir hale getirir","ScrapeContent, ScrapeElements, ScrapeWebsite, InitializeBrowser ve CloseBrowser endpoint'lerini içerir"]}
{"type":"entity","name":"Process","entityType":"Model","observations":["Process sınıfına kendini veritabanından güncelleme metotları eklendi","UpdateSelfAsync metodu - işlemi tamamen veritabanında günceller","UpdateStatusAsync metodu - durum, ilerleme, sonuç ve hata mesajını günceller","UpdateProgressAsync metodu - sadece ilerleme yüzdesini günceller","Metotlar IDatabaseService bağımlılığı kullanarak ApplicationDbContext'e erişir","Otomatik LastPing güncelleme ve CompletedAt zamanı ayarlama","Exception handling ile güvenli güncelleme işlemleri","Progress değeri 0-100 arasında sınırlandırılıyor","Entity Framework Core ile veritabanı güncelleme işlemleri","Proje işlemlerini takip eder ve yönetir","UUID kimliği, proje bağlantısı, durum takibi","ProcessStatus ve ProcessTaskType enum'ları","JSON formatında ayarlar desteği","DTO sınıfları: ProcessCreateDto, ProcessUpdateDto"]}
{"type":"relation","from":"TranslationAgentServer","to":"Models","relationType":"contains"}
{"type":"relation","from":"TranslationAgentServer","to":"Configurations","relationType":"contains"}
{"type":"relation","from":"TranslationAgentServer","to":"CreateSchemaResult","relationType":"contains"}
{"type":"relation","from":"TranslationAgentServer","to":"Catalyst","relationType":"uses"}
{"type":"relation","from":"TextController","to":"ITextService","relationType":"uses"}
{"type":"relation","from":"TextService","to":"ITextService","relationType":"implements"}
{"type":"relation","from":"TextService","to":"Text","relationType":"uses"}
{"type":"relation","from":"ProcessBackgroundService","to":"IProcessBackgroundService","relationType":"implements"}
{"type":"relation","from":"ContextController","to":"IContextService","relationType":"depends on"}
{"type":"relation","from":"ContextService","to":"IContextService","relationType":"implements"}
{"type":"relation","from":"GeminiService","to":"IGeminiService","relationType":"implements"}
{"type":"relation","from":"NlpService","to":"INlpService","relationType":"implements"}
{"type":"relation","from":"NlpController","to":"INlpService","relationType":"depends on"}
{"type":"relation","from":"TextProcessingHelper","to":"INlpService","relationType":"integrates with"}
{"type":"relation","from":"GeminiModels","to":"GeminiService","relationType":"used by"}
{"type":"relation","from":"TextAnalysisResult","to":"NlpService","relationType":"returned by"}
{"type":"relation","from":"Helpers","to":"EmbeddingHelper","relationType":"contains"}
{"type":"relation","from":"Helpers","to":"TextProcessingHelper","relationType":"contains"}
{"type":"relation","from":"Models","to":"Context","relationType":"contains"}
{"type":"relation","from":"Models","to":"GeminiModels","relationType":"contains"}
{"type":"relation","from":"Models","to":"TextAnalysisResult","relationType":"contains"}
{"type":"relation","from":"TranslationAgentServer","to":"Controllers Folder","relationType":"contains"}
{"type":"relation","from":"TranslationAgentServer","to":"Services Folder","relationType":"contains"}
{"type":"relation","from":"TranslationAgentServer","to":"Interfaces Folder","relationType":"contains"}
{"type":"relation","from":"TranslationAgentServer","to":"Models Folder","relationType":"contains"}
{"type":"relation","from":"TranslationAgentServer","to":"Helpers Folder","relationType":"contains"}
{"type":"relation","from":"TranslationAgentServer","to":"Configurations Folder","relationType":"contains"}
{"type":"relation","from":"TranslationAgentServer","to":"Middleware Folder","relationType":"contains"}
{"type":"relation","from":"TranslationAgentServer","to":"Authentication System","relationType":"implements"}
{"type":"relation","from":"TranslationAgentServer","to":"Google Sheets Integration","relationType":"implements"}
{"type":"relation","from":"TranslationAgentServer","to":"Gemini AI Integration","relationType":"implements"}
{"type":"relation","from":"TranslationAgentServer","to":"NLP Integration","relationType":"implements"}
{"type":"relation","from":"TranslationAgentServer","to":"Background Process System","relationType":"implements"}
{"type":"relation","from":"TranslationAgentServer","to":"Database Schema","relationType":"uses"}
{"type":"relation","from":"TranslationAgentServer","to":"Deployment Configuration","relationType":"configured_with"}
{"type":"relation","from":"Controllers Folder","to":"Services Folder","relationType":"depends_on"}
{"type":"relation","from":"Services Folder","to":"Interfaces Folder","relationType":"implements"}
{"type":"relation","from":"Services Folder","to":"Models Folder","relationType":"uses"}
{"type":"relation","from":"Services Folder","to":"Helpers Folder","relationType":"uses"}
{"type":"relation","from":"Services Folder","to":"Database Schema","relationType":"interacts_with"}
{"type":"relation","from":"Authentication System","to":"Middleware Folder","relationType":"implemented_in"}
{"type":"relation","from":"Google Sheets Integration","to":"Services Folder","relationType":"implemented_in"}
{"type":"relation","from":"Gemini AI Integration","to":"Services Folder","relationType":"implemented_in"}
{"type":"relation","from":"NLP Integration","to":"Services Folder","relationType":"implemented_in"}
{"type":"relation","from":"Background Process System","to":"Services Folder","relationType":"implemented_in"}
{"type":"relation","from":"TranslationAgentServer","to":"RetryHelper","relationType":"uses"}
{"type":"relation","from":"GeminiService","to":"RetryHelper","relationType":"integrates"}
{"type":"relation","from":"TextService","to":"RetryHelper","relationType":"integrates"}
{"type":"relation","from":"GeminiService","to":"TranslationAgentServer","relationType":"belongs_to"}
{"type":"relation","from":"TextService","to":"TranslationAgentServer","relationType":"belongs_to"}
{"type":"relation","from":"RetryHelper","to":"TranslationAgentServer","relationType":"belongs_to"}
{"type":"relation","from":"DatabaseService","to":"IDatabaseService","relationType":"implements"}
{"type":"relation","from":"DatabaseService","to":"TranslationAgentServer","relationType":"belongs_to"}
{"type":"relation","from":"IDatabaseService","to":"TranslationAgentServer","relationType":"belongs_to"}
{"type":"relation","from":"TextService","to":"DatabaseService","relationType":"uses"}
{"type":"relation","from":"ContextService","to":"DatabaseService","relationType":"uses"}
{"type":"relation","from":"TermService","to":"DatabaseService","relationType":"uses"}
{"type":"relation","from":"ProjectService","to":"DatabaseService","relationType":"uses"}
{"type":"relation","from":"ProcessService","to":"DatabaseService","relationType":"uses"}
{"type":"relation","from":"AuthService","to":"IAuthService","relationType":"implements"}
{"type":"relation","from":"GoogleSheetsService","to":"IGoogleSheetsService","relationType":"implements"}
{"type":"relation","from":"ProjectService","to":"IProjectService","relationType":"implements"}
{"type":"relation","from":"ProcessService","to":"IProcessService","relationType":"implements"}
{"type":"relation","from":"TermService","to":"ITermService","relationType":"implements"}
{"type":"relation","from":"AuthService","to":"TranslationAgentServer","relationType":"belongs_to"}
{"type":"relation","from":"GoogleSheetsService","to":"TranslationAgentServer","relationType":"belongs_to"}
{"type":"relation","from":"ProjectService","to":"TranslationAgentServer","relationType":"belongs_to"}
{"type":"relation","from":"ProcessService","to":"TranslationAgentServer","relationType":"belongs_to"}
{"type":"relation","from":"TermService","to":"TranslationAgentServer","relationType":"belongs_to"}
{"type":"relation","from":"IAuthService","to":"TranslationAgentServer","relationType":"belongs_to"}
{"type":"relation","from":"IGoogleSheetsService","to":"TranslationAgentServer","relationType":"belongs_to"}
{"type":"relation","from":"IProjectService","to":"TranslationAgentServer","relationType":"belongs_to"}
{"type":"relation","from":"IProcessService","to":"TranslationAgentServer","relationType":"belongs_to"}
{"type":"relation","from":"ITermService","to":"TranslationAgentServer","relationType":"belongs_to"}
{"type":"relation","from":"AuthService","to":"DatabaseService","relationType":"uses"}
{"type":"relation","from":"WebScraperService","to":"IWebScraperService","relationType":"implements"}
{"type":"relation","from":"WebScraperController","to":"IWebScraperService","relationType":"uses"}
{"type":"relation","from":"Program","to":"WebScraperService","relationType":"registers"}