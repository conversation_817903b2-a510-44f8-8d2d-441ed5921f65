using System.Text.Json;

namespace TranslationAgentServer.Models.Filtering;

/// <summary>
/// Process modeli için <PERSON>ştirilmiş filtreleme sorgusu
/// </summary>
public class ProcessFilterQuery : FilterQuery
{
    /// <summary>
    /// ProjectId filtresi
    /// </summary>
    public int? ProjectId { get; set; }
    
    /// <summary>
    /// Progress filtresi (minimum)
    /// </summary>
    public int? ProgressMin { get; set; }
    
    /// <summary>
    /// Progress filtresi (maksimum)
    /// </summary>
    public int? ProgressMax { get; set; }
    
    /// <summary>
    /// Status filtresi
    /// </summary>
    public ProcessStatus? Status { get; set; }
    
    /// <summary>
    /// TaskType filtresi
    /// </summary>
    public ProcessTaskType? TaskType { get; set; }
    
    /// <summary>
    /// CreatedAt başlangıç tarihi
    /// </summary>
    public DateTime? CreatedAtFrom { get; set; }
    
    /// <summary>
    /// CreatedAt bitiş tarihi
    /// </summary>
    public DateTime? CreatedAtTo { get; set; }
    
    /// <summary>
    /// CompletedAt başlangıç tarihi
    /// </summary>
    public DateTime? CompletedAtFrom { get; set; }
    
    /// <summary>
    /// CompletedAt bitiş tarihi
    /// </summary>
    public DateTime? CompletedAtTo { get; set; }
    
    /// <summary>
    /// JSON string'den ProcessFilterQuery oluşturur
    /// </summary>
    public static new ProcessFilterQuery FromJson(string json)
    {
        if (string.IsNullOrEmpty(json))
            return new ProcessFilterQuery();
            
        try
        {
            return JsonSerializer.Deserialize<ProcessFilterQuery>(json, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            }) ?? new ProcessFilterQuery();
        }
        catch
        {
            return new ProcessFilterQuery();
        }
    }
    
    /// <summary>
    /// JsonElement'den ProcessFilterQuery oluşturur
    /// </summary>
    public static new ProcessFilterQuery FromJsonElement(JsonElement element)
    {
        try
        {
            return JsonSerializer.Deserialize<ProcessFilterQuery>(element.GetRawText(), new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            }) ?? new ProcessFilterQuery();
        }
        catch
        {
            return new ProcessFilterQuery();
        }
    }
}