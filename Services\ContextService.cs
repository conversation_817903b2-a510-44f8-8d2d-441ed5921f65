using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Pgvector;
using TranslationAgentServer.Interfaces;
using TranslationAgentServer.Models;
using TranslationAgentServer.Models.Filtering;
using System.Text.Json;

namespace TranslationAgentServer.Services;

/// <summary>
/// Context servisi implementasyonu
/// Context verilerinin CRUD işlemlerini ve özel sorguları gerçekleştirir
/// </summary>
public class ContextService : IContextService
{
    private readonly IDatabaseService _databaseService;
    private readonly ILogger<ContextService> _logger;
    private readonly IGeminiService _geminiService;

    public ContextService(IDatabaseService databaseService, ILogger<ContextService> logger, IGeminiService geminiService)
    {
        _databaseService = databaseService;
        _logger = logger;
        _geminiService = geminiService;
    }

    /// <summary>
    /// Tüm context'leri getirir
    /// </summary>
    public async Task<List<Context>> GetAllContextsAsync(int projectId)
    {
        try
        {
            await using var client = _databaseService.GetProjectContext(projectId);

            // Önce tüm verileri al

            var query = client.Contexts.Where(c => c != null);



            var result = await query.ToListAsync();
            return result ?? new List<Context>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Context'ler getirilirken hata oluştu. Şema: {projectId}", projectId);
            throw;
        }
    }

    public async Task<List<Context>> GetAllContextsAsync(int projectId, ContextFilterQuery filterQuery)
    {
        try
        {
            await using var client = _databaseService.GetProjectContext(projectId);
            var query = client.Contexts.AsQueryable();


            var filteredQuery = FilteringService.ApplyFilters(query, filterQuery);


            return await filteredQuery.ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Gelişmiş filtreleme ile context'ler getirilirken hata oluştu. Proje: {ProjectId}", projectId);
            throw;
        }
    }

    public async Task<List<Context>> GetAllContextsAsync(int projectId, JsonElement filterElement)
    {
        try
        {
            var filterQuery = ContextFilterQuery.FromJsonElement(filterElement);
            return await GetAllContextsAsync(projectId, filterQuery);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "JsonElement filtreleme ile context'ler getirilirken hata oluştu. Proje: {ProjectId}", projectId);
            throw;
        }
    }

    /// <summary>
    /// ID'ye göre context getirir
    /// </summary>
    public async Task<Context?> GetContextByIdAsync(int id, int projectId)
    {
        try
        {
            await using var client = _databaseService.GetProjectContext(projectId);
            var response = await client.Contexts
                .Where(x => x.Id == id)
                .SingleAsync();

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Context getirilirken hata oluştu. ID: {Id}, Schema: {projectId}", id, projectId);
            return null;
        }
    }


    /// <summary>
    /// Embedding vektörüne göre benzer context'leri bulur
    /// </summary>
    public async Task<List<Context>> FindSimilarContextsAsync(float[] embedding, int limit, int projectId)
    {
        try
        {
            await using var client = _databaseService.GetProjectContext(projectId);
            // Vector similarity search için RPC fonksiyonu kullanılabilir
            // Bu örnekte basit bir yaklaşım kullanıyoruz
            var response = await client.Contexts
                .Where(x => x.Embedding != null)
                .Take(limit)
                .OrderByDescending(x => x.CreatedAt)
                .ToListAsync();

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Benzer context'ler bulunurken hata oluştu. Schema: {projectId}, Limit: {Limit}",
                projectId, limit);
            throw;
        }
    }

    /// <summary>
    /// Yeni context oluşturur
    /// </summary>
    public async Task<Context> CreateContextAsync(ContextCreateDto contextCreateDto, int projectId)
    {
        try
        {
            await using var client = _databaseService.GetProjectContext(projectId);
            var context = new Context
            {
                Category = contextCreateDto.Category,
                Title = contextCreateDto.Title,
                Content = contextCreateDto.Content,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            var combined_text = $"{contextCreateDto.Category} {contextCreateDto.Title} {contextCreateDto.Content}";
            if (!string.IsNullOrEmpty(combined_text))
            {
                var embeddingResponse = await _geminiService.GetEmbeddingAsync(combined_text);
                if (embeddingResponse.Success)
                {
                    context.Embedding = new Vector(embeddingResponse.Embedding);
                }
                else
                {
                    throw new Exception($"Embedding oluşturulamadı. Hata: {embeddingResponse.ErrorMessage}");
                }


            }

            var response = await client.Contexts.AddAsync(context);
            var result = await client.SaveChangesAsync();

            return result > 0 ? response.Entity : null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Context oluşturulurken hata oluştu. Schema: {projectId}, Title: {Title}",
                projectId, contextCreateDto.Title);
            throw;
        }
    }

    /// <summary>
    /// Context günceller
    /// </summary>
    public async Task<ContextUpdateDto?> UpdateContextAsync(int id, ContextUpdateDto contextUpdateDto, int projectId)
    {
        try
        {
            await using var client = _databaseService.GetProjectContext(projectId);
            var context = await client.Contexts.Where(x => x.Id == id).FirstOrDefaultAsync();
            if (context != null)
            {
                context.Category = contextUpdateDto.Category ?? context.Category;
                context.Title = contextUpdateDto.Title ?? context.Title;
                context.Content = contextUpdateDto.Content ?? context.Content;
                context.UpdatedAt = DateTime.UtcNow;

                var combined_text = $"{context.Category} {context.Title} {context.Content}";
                if (!string.IsNullOrEmpty(combined_text))
                {
                    var embeddingResponse = await _geminiService.GetEmbeddingAsync(combined_text);
                    if (embeddingResponse.Success)
                    {
                        context.Embedding = new Vector(embeddingResponse.Embedding);
                    }
                    else
                    {
                        throw new Exception($"Embedding oluşturulamadı. Hata: {embeddingResponse.ErrorMessage}");
                    }
                }
            }

            context = client.Contexts.Update(context).Entity;
            var response = await client.SaveChangesAsync();

            return response > 0 ? contextUpdateDto : null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Context güncellenirken hata oluştu. ID: {Id}, Schema: {projectId}", id, projectId);
            return null;
        }
    }

    /// <summary>
    /// Context siler
    /// </summary>
    public async Task<bool> DeleteContextAsync(int id, int projectId)
    {
        try
        {
            await using var client = _databaseService.GetProjectContext(projectId);
            var deletedContext = await client.Contexts
                .Where(x => x.Id == id).SingleAsync();
            if (deletedContext == null)
            {
                return false;
            }

            client.Contexts.Remove(deletedContext);
            var response = await client.SaveChangesAsync();

            return response > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Context silinirken hata oluştu. ID: {Id}, Schema: {projectId}", id, projectId);
            return false;
        }
    }

    /// <summary>
    /// Context'leri toplu olarak oluşturur
    /// </summary>
    public async Task<List<Context>> CreateContextsAsync(List<ContextCreateDto> contextCreateDtos, int projectId, CancellationToken cancellationToken)
    {
        try
        {
            await using var client = _databaseService.GetProjectContext(projectId);
            var contexts = contextCreateDtos.Select(dto => new Context
            {
                Category = dto.Category,
                Title = dto.Title,
                Content = dto.Content,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            }).ToList();

            var batchSize = 100;
            for (int i = 0; i < contexts.Count; i += batchSize)
            {
                var batch = contexts.Skip(i).Take(batchSize).ToList();
                // Retry mekanizması ile embedding üretimi
                var embeddingsResponse = await _geminiService.GetEmbeddingsAsync(batch.Select(t => $"{t.Category} {t.Title} {t.Content}").ToList(), cancellationToken);


                if (!embeddingsResponse.Success)
                {
                    throw new Exception($"Embedding oluşturulamadı. Hata: {embeddingsResponse.ErrorMessage}");
                }

                for (int j = 0; j < batch.Count; j++)
                {
                    batch[j].Embedding = new Vector(embeddingsResponse.Embeddings[j]);
                }
                // Batch'ler arası bekleme süresi (rate limiting için)
                await Task.Delay(3500);
            }

            await client.Contexts.AddRangeAsync(contexts);
            var ret = await client.SaveChangesAsync();

            return ret > 0 ? contexts : new List<Context>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Context'ler toplu olarak oluşturulurken hata oluştu. Schema: {projectId}, Count: {Count}",
                projectId, contextCreateDtos.Count);
            throw;
        }
    }

    /// <summary>
    /// Context'leri toplu olarak günceller
    /// </summary>
    public async Task<List<Context>> UpdateContextsAsync(List<(int Id, ContextUpdateDto UpdateDto)> contextUpdates, int projectId, CancellationToken cancellationToken)
    {
        try
        {
            var updatedContexts = new List<Context>();
            foreach (var (id, updateDto) in contextUpdates)
            {
                var context = await GetContextByIdAsync(id, projectId);
                if (context == null)
                {
                    continue;
                }

                if (updateDto.Category != null) context.Category = updateDto.Category;
                if (updateDto.Title != null) context.Title = updateDto.Title;
                if (updateDto.Content != null) context.Content = updateDto.Content;
                context.UpdatedAt = DateTime.UtcNow;

                updatedContexts.Add(context);
            }

            var batchSize = 100;
            for (int i = 0; i < updatedContexts.Count; i += batchSize)
            {
                var batch = updatedContexts.Skip(i).Take(batchSize).ToList();
                // Retry mekanizması ile embedding üretimi
                var embeddingsResponse = await _geminiService.GetEmbeddingsAsync(batch.Select(t => $"{t.Category} {t.Title} {t.Content}").ToList(), cancellationToken);

                if (!embeddingsResponse.Success)
                {
                    throw new Exception($"Embedding oluşturulamadı. Hata: {embeddingsResponse.ErrorMessage}");
                }

                for (int j = 0; j < batch.Count; j++)
                {
                    batch[j].Embedding = new Vector(embeddingsResponse.Embeddings[j]);
                }
                // Batch'ler arası bekleme süresi (rate limiting için)
                await Task.Delay(3500);
            }



            await using var client = _databaseService.GetProjectContext(projectId);
            await using var transaction = await client.Database.BeginTransactionAsync(cancellationToken);
            try
            {
                client.Contexts.UpdateRange(updatedContexts);
                await client.SaveChangesAsync(cancellationToken);
                await transaction.CommitAsync(cancellationToken);
            }
            catch
            {
                await transaction.RollbackAsync(cancellationToken);
                throw;
            }

            return updatedContexts;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Context'ler toplu olarak güncellenirken hata oluştu. Schema: {projectId}, Count: {Count}",
                projectId, contextUpdates.Count);
            throw;
        }
    }

    /// <summary>
    /// Context'leri toplu olarak siler
    /// </summary>
    public async Task<bool> DeleteContextsAsync(List<long> ids, int projectId)
    {
        try
        {
            await using var client = _databaseService.GetProjectContext(projectId);
            var response = await client.Contexts
                .Where(x => ids.Contains(x.Id)).ToListAsync();
            client.Contexts.RemoveRange(response);
            var result = await client.SaveChangesAsync();

            return result > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Context'ler toplu olarak silinirken hata oluştu. Schema: {projectId}, Count: {Count}",
                projectId, ids.Count);
            return false;
        }
    }

    /// <summary>
    /// Context istatistiklerini getirir
    /// </summary>
    public async Task<ContextStatistics> GetContextStatisticsAsync(int projectId)
    {
        try
        {
            await using var client = _databaseService.GetProjectContext(projectId);
            var contexts = await client.Contexts.ToListAsync();

            var statistics = new ContextStatistics
            {
                TotalContexts = contexts.Count,
                ContextsWithEmbedding = contexts.Count(c => c.Embedding != null),
                CategoryDistribution = contexts
                    .GroupBy(c => c.Category)
                    .ToDictionary(g => g.Key, g => g.Count()),
                LastCreatedAt = contexts.Max(c => c.CreatedAt),
                LastUpdatedAt = contexts.Max(c => c.UpdatedAt)
            };

            return statistics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Context istatistikleri getirilirken hata oluştu. Schema: {projectId}", projectId);
            throw;
        }
    }
}