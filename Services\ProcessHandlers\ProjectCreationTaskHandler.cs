using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using TranslationAgentServer.Data;
using TranslationAgentServer.Helpers;
using TranslationAgentServer.Interfaces;
using TranslationAgentServer.Models;

namespace TranslationAgentServer.Services.ProcessHandlers
{
    /// <summary>
    /// Proje oluşturma görevini işler.
    /// </summary>
    public class ProjectCreationTaskHandler : ProcessHandlerBase
    {
        public ProjectCreationTaskHandler(
            IDatabaseService databaseService,
            IGoogleSheetsService googleSheetsService,
            IProjectService projectService,
            ITextService textService,
            ITermService termService,
            IContextService contextService,
            IGeminiService geminiService,
            IWebScraperService webScraperService,
            ILogger<ProjectCreationTaskHandler> logger,
            IProcessUpdateService processUpdateService) : base(
                databaseService,
                googleSheetsService,
                projectService,
                textService,
                termService,
                contextService,
                geminiService,
                webScraperService,
                logger,
                processUpdateService)
        {
        }

        /// <summary>
        /// Proje oluşturma görevini yürütür.
        /// </summary>
        public override async Task ExecuteAsync(Process process, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Proje oluşturma task'ı çalıştırılıyor: {ProcessId}", process.Id);

            try
            {
                await ProcessTextsFromGoogleSheets(process, cancellationToken);
                await ProcessTermsFromGoogleSheets(process, cancellationToken);
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation("Proje oluşturma task'ı iptal edildi: {ProcessId}", process.Id);
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Proje oluşturma task'ı çalıştırılırken hata oluştu: {ProcessId}", process.Id);
                throw;
            }
        }

        /// <summary>
        /// Google Sheets'ten metinleri işler ve veritabanına kaydeder
        /// </summary>
        private async Task ProcessTextsFromGoogleSheets(Process process, CancellationToken cancellationToken)
        {
            process.Result = "Metinler için Proje bilgileri alınıyor...";
            process.Progress = 5;
            await UpdateProcess(process);

            var project = await _projectService.GetProjectByIdAsync(process.ProjectId);
            if (project == null)
                throw new InvalidOperationException($"Proje bulunamadı: {process.ProjectId}");

            if (string.IsNullOrEmpty(project.SpreadsheetId) || string.IsNullOrEmpty(project.TextsTable))
                throw new InvalidOperationException("Proje için Google Sheets bilgileri eksik (SpreadsheetId veya TextsTable)");

            if (!_googleSheetsService.IsServiceInitialized())
                throw new InvalidOperationException("Google Sheets servisi başlatılmamış");

            await using var mainclient = _databaseService.GetContext();
            var textsColumns = await mainclient.MainData.SingleAsync(x => x.Name == "texts_columns", cancellationToken);

            var options = new SheetProcessingOptions
            {
                HeaderTemplate = textsColumns.Value,
                SkipHeader = true
            };

            // RowMapper fonksiyonu: Sheet satırı + başlık -> TextCreateDto
            TextCreateDto RowMapper(System.Collections.Generic.List<string> row, System.Collections.Generic.List<string> headers)
            {
                var status = TextStatus.EN;
                var textStatus = _googleSheetsService.GetColumnValue(row, headers, "status");
                if (textStatus?.Equals("TR", StringComparison.OrdinalIgnoreCase) == true)
                    status = TextStatus.TR;
                else if (textStatus?.Equals("DUPE", StringComparison.OrdinalIgnoreCase) == true)
                    status = TextStatus.DUPE;
                else if (textStatus?.Equals("NULL", StringComparison.OrdinalIgnoreCase) == true)
                    status = TextStatus.NULL;

                return new TextCreateDto
                {
                    RowID = int.TryParse(_googleSheetsService.GetColumnValue(row, headers, "row_id"), out var rowId) ? rowId : -1,
                    Namespace = _googleSheetsService.GetColumnValue(row, headers, "namespace"),
                    Key = _googleSheetsService.GetColumnValue(row, headers, "key"),
                    En = _googleSheetsService.GetColumnValue(row, headers, "en"),
                    Tr = _googleSheetsService.GetColumnValue(row, headers, "tr"),
                    Status = status
                };
            }

            var result = await _googleSheetsService.ProcessSheetDataAsync<TextCreateDto>(
                project.SpreadsheetId,
                project.TextsTable,
                options,
                RowMapper);

            if (!result.Success || result.ProcessedItems.Count == 0)
                throw new InvalidOperationException(result.ErrorMessage ?? "İşlenebilir veri bulunamadı");

            process.Progress = 30;
            process.Result = $"{result.ValidRows} geçerli metin kaydı hazırlandı";
            await UpdateProcess(process);

            cancellationToken.ThrowIfCancellationRequested();

            process.Result = "Veritabanına metinler kaydediliyor...";
            await UpdateProcess(process);

            var texts = await _textService.CreateTextsAsync(result.ProcessedItems, process.ProjectId, cancellationToken);

            await using var client = _databaseService.GetProjectContext(process.ProjectId);
            await using var transaction = await client.Database.BeginTransactionAsync(cancellationToken);
            try
            {
                client.Texts.RemoveRange(client.Texts);
                await client.Texts.AddRangeAsync(texts, cancellationToken);
                await client.SaveChangesAsync(cancellationToken);
                await transaction.CommitAsync(cancellationToken);
            }
            catch
            {
                await transaction.RollbackAsync(cancellationToken);
                throw;
            }

            cancellationToken.ThrowIfCancellationRequested();

            var statistics = await _textService.GetTextStatisticsAsync(process.ProjectId);
            var resultMessage = $"İşlem tamamlandı. Toplam {result.ValidRows} metin kaydedildi. " +
                              $"Veritabanında toplam {statistics.TotalTexts} metin bulunuyor.";

            process.Progress = 50;
            process.Result = resultMessage;
            await UpdateProcess(process);

            _logger.LogInformation("Text create task'ı başarıyla tamamlandı: {ProcessId} - {Count} metin işlendi",
                process.Id, result.ValidRows);
        }


        /// <summary>
        /// Google Sheets'ten terimleri işler ve veritabanına kaydeder (yeni servis ile).
        /// </summary>
        private async Task ProcessTermsFromGoogleSheets(Process process, CancellationToken cancellationToken)
        {
            process.Result = "Terimler için Proje bilgileri alınıyor...";
            process.Progress = 50;
            await UpdateProcess(process);

            var project = await _projectService.GetProjectByIdAsync(process.ProjectId);
            if (project == null)
                throw new InvalidOperationException($"Proje bulunamadı: {process.ProjectId}");

            if (string.IsNullOrEmpty(project.SpreadsheetId) || string.IsNullOrEmpty(project.TermsTable))
                throw new InvalidOperationException("Proje için Google Sheets bilgileri eksik (SpreadsheetId veya TermsTable)");

            if (!_googleSheetsService.IsServiceInitialized())
                throw new InvalidOperationException("Google Sheets servisi başlatılmamış");

            await using var mainclient = _databaseService.GetContext();
            var termsColumns = await mainclient.MainData.SingleAsync(x => x.Name == "terms_columns", cancellationToken);

            var options = new SheetProcessingOptions
            {
                HeaderTemplate = termsColumns.Value,
                SkipHeader = true
            };

            // RowMapper fonksiyonu: Sheet satırı + başlık -> TermCreateDto
            TermCreateDto RowMapper(System.Collections.Generic.List<string> row, System.Collections.Generic.List<string> headers)
            {
                var status = TermStatus.EN;
                var termStatus = _googleSheetsService.GetColumnValue(row, headers, "status");
                if (termStatus?.Equals("TR", StringComparison.OrdinalIgnoreCase) == true)
                    status = TermStatus.TR;

                return new TermCreateDto
                {
                    RowId = int.TryParse(_googleSheetsService.GetColumnValue(row, headers, "row_id"), out var rowId) ? rowId : -1,
                    En = _googleSheetsService.GetColumnValue(row, headers, "en"),
                    Tr = _googleSheetsService.GetColumnValue(row, headers, "tr"),
                    Category = _googleSheetsService.GetColumnValue(row, headers, "category"),
                    Info = _googleSheetsService.GetColumnValue(row, headers, "info"),
                    Status = status
                };
            }

            var result = await _googleSheetsService.ProcessSheetDataAsync<TermCreateDto>(
                project.SpreadsheetId,
                project.TermsTable,
                options,
                RowMapper);

            if (!result.Success || result.ProcessedItems.Count == 0)
                throw new InvalidOperationException(result.ErrorMessage ?? "İşlenebilir veri bulunamadı");

            process.Progress = 90;
            process.Result = $"{result.ValidRows} geçerli terim kaydı hazırlandı";
            await UpdateProcess(process);

            cancellationToken.ThrowIfCancellationRequested();

            process.Result = "Veritabanına terimler kaydediliyor...";
            await UpdateProcess(process);

            var terms = await _termService.CreateTermsAsync(result.ProcessedItems, process.ProjectId, cancellationToken);

            await using var client = _databaseService.GetProjectContext(process.ProjectId);
            await using var transaction = await client.Database.BeginTransactionAsync(cancellationToken);
            try
            {
                client.Terms.RemoveRange(client.Terms);
                await client.Terms.AddRangeAsync(terms, cancellationToken);
                await client.SaveChangesAsync(cancellationToken);
                await transaction.CommitAsync(cancellationToken);
            }
            catch
            {
                await transaction.RollbackAsync(cancellationToken);
                throw;
            }

            cancellationToken.ThrowIfCancellationRequested();

            var statistics = await _termService.GetTermStatisticsAsync(process.ProjectId);
            var resultMessage = $"İşlem tamamlandı. Toplam {result.ValidRows} terim kaydedildi. " +
                                $"Veritabanında toplam {statistics.TotalTerms} terim bulunuyor.";

            process.Progress = 100;
            process.Result = resultMessage;
            process.Status = (int)ProcessStatus.Completed;
            await UpdateProcess(process);

            _logger.LogInformation("Term create task'ı başarıyla tamamlandı: {ProcessId} - {Count} terim işlendi",
                process.Id, result.ValidRows);
        }
    }
}