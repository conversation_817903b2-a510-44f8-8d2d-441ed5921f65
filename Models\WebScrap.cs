using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TranslationAgentServer.Models;

/// <summary>
/// Web scraping verilerini temsil eden model sınıfı
/// </summary>
public class WebScrap
{
    /// <summary>
    /// <PERSON><PERSON><PERSON> kimlik
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// Scraping yapılan URL
    /// </summary>
    public string? Url { get; set; }

    /// <summary>
    /// Scraping ile elde edilen içerik
    /// </summary>
    public string? Content { get; set; }

    /// <summary>
    /// İşlenme durumu
    /// </summary>
    public bool Processed { get; set; }

    /// <summary>
    /// Oluşturulma tarihi
    /// </summary>
    public DateTime CreatedAt { get; set; }


    public override bool Equals(object? obj)
    {
        return obj is WebScrap other && Id == other.Id;
    }

    public override int GetHashCode()
    {
        return Id.GetHashCode();
    }
}

/// <summary>
/// Web scraping verisi oluşturma DTO'su
/// </summary>
public class WebScrapCreateDto
{
    /// <summary>
    /// Scraping yapılacak URL
    /// </summary>
    public string? Url { get; set; }

    /// <summary>
    /// Scraping ile elde edilen içerik
    /// </summary>
    public string? Content { get; set; }

    public bool Processed { get; set; }
}

/// <summary>
/// Web scraping verisi güncelleme DTO'su
/// </summary>
public class WebScrapUpdateDto
{
    /// <summary>
    /// Scraping yapılan URL
    /// </summary>
    public string? Url { get; set; }

    /// <summary>
    /// Scraping ile elde edilen içerik
    /// </summary>
    public string? Content { get; set; }

    public bool Processed { get; set; }
}

/// <summary>
/// Web scraping istatistikleri DTO'su
/// </summary>
public class WebScrapStatistics
{
    /// <summary>
    /// Toplam web scraping sayısı
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// En son scraping tarihi
    /// </summary>
    public DateTime? LastScrapedAt { get; set; }
}