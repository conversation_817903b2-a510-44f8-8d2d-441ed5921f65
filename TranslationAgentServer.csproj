<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Catalyst" Version="1.0.54164" />
    <PackageReference Include="Catalyst.Models.English" Version="1.0.30952" />
    <PackageReference Include="EFCore.SqlServer.VectorSearch" Version="9.0.0-preview.2" />
    <PackageReference Include="Google.Apis.Drive.v3" Version="1.70.0.3834" />
    <PackageReference Include="LLMSharp.OpenAi.Tokenizer" Version="2.0.3" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="9.0.7" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.6" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.7" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.0" />
    <PackageReference Include="Microsoft.Playwright" Version="1.53.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.4" />
    <PackageReference Include="OpenAI-DotNet" Version="8.8.1" />
    <PackageReference Include="Pgvector" Version="0.3.2" />
    <PackageReference Include="Pgvector.EntityFrameworkCore" Version="0.2.2" />
    <PackageReference Include="Google.Apis.Sheets.v4" Version="1.70.0.3819" />
    <PackageReference Include="Google.Apis.Auth" Version="1.70.0" />
    <PackageReference Include="Mscc.GenerativeAI" Version="2.6.8" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="SpellDictionaries\**">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

</Project>
