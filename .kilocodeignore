# .dockerignore - Docker build sırasında dahil edilmeyecek dosyalar
# Performansı artırır ve image boyutunu küçültür

# Build artifacts
bin/
obj/

# Visual Studio files
.vs/
*.user
*.suo
*.userosscache
*.sln.docstates

# Git files
.git/
.gitignore
.gitattributes

# Documentation
README*

# Test files
**/TestResults/
**/*Test*/
**/*Tests*/

# Logs
logs/
*.log

# Runtime files
*.tmp
*.temp

# IDE files
.vscode/
.idea/
.trae/
.gemini/
.netcoredbg/

# OS files
Thumbs.db
.DS_Store

# Package files
*.nupkg
*.snupkg

# Playwright browsers (will be installed in container)
bin/Debug/net*/playwright.ps1
bin/Release/net*/playwright.ps1

# Environment files
.env
.env.local
.env.development
.env.production

# Docker files (kendini dahil etme)
Dockerfile*
.dockerignore

# node modules
node_modules/

memory.json