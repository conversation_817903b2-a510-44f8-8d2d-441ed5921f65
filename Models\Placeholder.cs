using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TranslationAgentServer.Models
{
    /// <summary>
    /// Veritabanındaki bir yer tutucu (placeholder) varlığını temsil eder.
    /// </summary>
    [Table("placeholders")]
    public class Placeholder
    {
        /// <summary>
        /// Yer tutucu için benzersiz tanımlayıcıyı alır veya ayarlar.
        /// </summary>
        [Key]
        [Column("id")]
        public long Id { get; set; }

        /// <summary>
        /// Yer tutucunun türünü alır veya ayarlar.
        /// </summary>
        [Column("type")]
        public string? Type { get; set; }

        /// <summary>
        /// Yer tutucu için düzenli ifadeyi (regex) alır veya ayarlar.
        /// </summary>
        [Column("regex")]
        public string? Regex { get; set; }

        /// <summary>
        /// Yer tutucu için örneklerin listesini alır veya ayarlar.
        /// </summary>
        [Column("examples", TypeName = "text[]")]
        public List<string>? Examples { get; set; }

        /// <summary>
        /// Yer tutucunun oluşturulma zaman damgasını alır veya ayarlar.
        /// </summary>
        [Column("created_at")]
        public DateTimeOffset CreatedAt { get; set; } = DateTimeOffset.Now;
    }
}