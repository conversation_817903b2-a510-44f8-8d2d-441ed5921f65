using System.Text.Json;

namespace TranslationAgentServer.Models.Filtering;

/// <summary>
/// Term modeli için <PERSON>tirilmiş filtreleme sorgusu
/// </summary>
public class TermFilterQuery : FilterQuery
{
    /// <summary>
    /// İngilizce terim filtresi
    /// </summary>
    public string? En { get; set; }
    
    /// <summary>
    /// Türkçe terim filtresi
    /// </summary>
    public string? Tr { get; set; }
    
    /// <summary>
    /// Kategori filtresi
    /// </summary>
    public string? Category { get; set; }
    
    /// <summary>
    /// Bilgi filtresi
    /// </summary>
    public string? Info { get; set; }
    
    /// <summary>
    /// Status filtresi
    /// </summary>
    public TermStatus? Status { get; set; }
    
    /// <summary>
    /// RowId filtresi
    /// </summary>
    public int? RowId { get; set; }
    
    /// <summary>
    /// CreatedAt başlangıç tarihi
    /// </summary>
    public DateTime? CreatedAtFrom { get; set; }
    
    /// <summary>
    /// CreatedAt bitiş tarihi
    /// </summary>
    public DateTime? CreatedAtTo { get; set; }
    
    /// <summary>
    /// UpdatedAt başlangıç tarihi
    /// </summary>
    public DateTime? UpdatedAtFrom { get; set; }
    
    /// <summary>
    /// UpdatedAt bitiş tarihi
    /// </summary>
    public DateTime? UpdatedAtTo { get; set; }
    
    /// <summary>
    /// JSON string'den TermFilterQuery oluşturur
    /// </summary>
    public static new TermFilterQuery FromJson(string json)
    {
        if (string.IsNullOrEmpty(json))
            return new TermFilterQuery();
            
        try
        {
            return JsonSerializer.Deserialize<TermFilterQuery>(json, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            }) ?? new TermFilterQuery();
        }
        catch
        {
            return new TermFilterQuery();
        }
    }
    
    /// <summary>
    /// JsonElement'den TermFilterQuery oluşturur
    /// </summary>
    public static new TermFilterQuery FromJsonElement(JsonElement element)
    {
        try
        {
            return JsonSerializer.Deserialize<TermFilterQuery>(element.GetRawText(), new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            }) ?? new TermFilterQuery();
        }
        catch
        {
            return new TermFilterQuery();
        }
    }
}