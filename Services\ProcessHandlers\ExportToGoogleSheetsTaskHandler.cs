using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using TranslationAgentServer.Data;
using TranslationAgentServer.Interfaces;
using TranslationAgentServer.Models;

namespace TranslationAgentServer.Services.ProcessHandlers
{
    /// <summary>
    /// Google Sheets'e aktarım görevini işler.
    /// </summary>
    public class ExportToGoogleSheetsTaskHandler : ProcessHandlerBase
    {
        public ExportToGoogleSheetsTaskHandler(
            IDatabaseService databaseService,
            IGoogleSheetsService googleSheetsService,
            IProjectService projectService,
            ITextService textService,
            ITermService termService,
            IContextService contextService,
            IGeminiService geminiService,
            IWebScraperService webScraperService,
            ILogger<ExportToGoogleSheetsTaskHandler> logger,
            IProcessUpdateService processUpdateService) : base(
                databaseService,
                googleSheetsService,
                projectService,
                textService,
                termService,
                contextService,
                geminiService,
                webScraperService,
                logger,
                processUpdateService)
        {
        }

        private async Task ExecuteExportToGoogleSheetsTask(Process process, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Google Sheets'e aktarım task'ı çalıştırılıyor: {ProcessId}", process.Id);

            try
            {
                process.Result = "Proje bilgileri alınıyor...";
                process.Progress = 5;
                await UpdateProcess(process);

                var project = await _projectService.GetProjectByIdAsync(process.ProjectId);
                if (project == null)
                    throw new InvalidOperationException($"Proje bulunamadı: {process.ProjectId}");

                if (string.IsNullOrEmpty(project.SpreadsheetId))
                    throw new InvalidOperationException("Proje için Google Sheets bilgileri eksik (SpreadsheetId)");

                if (!_googleSheetsService.IsServiceInitialized())
                    throw new InvalidOperationException("Google Sheets servisi başlatılmamış");

                await using var mainClient = _databaseService.GetContext();
                var textsColumns = (await mainClient.MainData.SingleAsync(x => x.Name == "texts_columns", cancellationToken)).Value;
                var termsColumns = (await mainClient.MainData.SingleAsync(x => x.Name == "terms_columns", cancellationToken)).Value;

                cancellationToken.ThrowIfCancellationRequested();

                // Metinleri aktar
                process.Result = "Metinler Google Sheets'e aktarılıyor...";
                process.Progress = 20;
                await UpdateProcess(process);

                var dbTexts = await _textService.GetAllTextsAsync(process.ProjectId);
                var textUpdates = dbTexts.Select(t => new RowUpdateData
                {
                    RowId = t.RowID,
                    Values = new List<string> { t.RowID.ToString(), t.Namespace, t.Key, t.En, t.Tr, t.Status.ToString() }
                }).ToList();

                if (textUpdates.Any())
                {
                    await _googleSheetsService.UpdateRowsByRowIdAsync(project.SpreadsheetId, project.TextsTable, textUpdates, textsColumns);
                }

                cancellationToken.ThrowIfCancellationRequested();

                // Terimleri aktar
                process.Result = "Terimler Google Sheets'e aktarılıyor...";
                process.Progress = 60;
                await UpdateProcess(process);

                var dbTerms = await _termService.GetAllTermsAsync(process.ProjectId);
                var termUpdates = dbTerms.Select(t => new RowUpdateData
                {
                    RowId = t.RowId,
                    Values = new List<string> { t.RowId.ToString(), t.En, t.Tr, t.Category, t.Info, t.Status.ToString() }
                }).ToList();

                if (termUpdates.Any())
                {
                    await _googleSheetsService.UpdateRowsByRowIdAsync(project.SpreadsheetId, project.TermsTable, termUpdates, termsColumns);
                }

                process.Result = "Aktarım tamamlandı.";
                process.Progress = 100;
                await UpdateProcess(process);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Google Sheets'e aktarım sırasında hata oluştu: {ProcessId}", process.Id);
                throw;
            }
        }

        /// <summary>
        /// Google Sheets'e aktarım görevini yürütür.
        /// </summary>
        public override async Task ExecuteAsync(Process process, CancellationToken cancellationToken)
        {
            await ExecuteExportToGoogleSheetsTask(process, cancellationToken);
        }
    }
}