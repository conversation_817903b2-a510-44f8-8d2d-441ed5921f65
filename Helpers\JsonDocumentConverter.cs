using System.Text.Json;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace TranslationAgentServer.Helpers;

/// <summary>
/// JsonDocument için Newtonsoft.Json converter'ı
/// JsonDocument'ı düzgün serialize/deserialize eder
/// </summary>
public class JsonDocumentConverter : JsonConverter<JsonDocument?>
{
    /// <summary>
    /// JsonDocument'ı JSON string'e dönüştürür
    /// </summary>
    public override void WriteJson(JsonWriter writer, JsonDocument? value, Newtonsoft.Json.JsonSerializer serializer)
    {
        if (value == null)
        {
            writer.WriteNull();
            return;
        }

        // JsonDocument'ı JToken'a dönüştür
        var jsonString = value.RootElement.GetRawText();
        var jToken = JToken.Parse(jsonString);
        jToken.WriteTo(writer);
    }

    /// <summary>
    /// JSON string'i JsonDocument'a dönüştürür
    /// </summary>
    public override JsonDocument? ReadJson(JsonReader reader, Type objectType, JsonDocument? existingValue, bool hasExistingValue, Newtonsoft.Json.JsonSerializer serializer)
    {
        if (reader.TokenType == JsonToken.Null)
        {
            return null;
        }

        var jToken = JToken.Load(reader);
        var jsonString = jToken.ToString(Formatting.None);
        return JsonDocument.Parse(jsonString);
    }
}