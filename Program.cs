using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Serialization;
using Npgsql;
using TranslationAgentServer.Data;
using TranslationAgentServer.Helpers;
using TranslationAgentServer.Interfaces;
using TranslationAgentServer.Middleware;
using TranslationAgentServer.Models;
using TranslationAgentServer.Services;
using TranslationAgentServer.Services.ProcessHandlers;

var builder = WebApplication.CreateBuilder(args);

/*
 * Logging konfigürasyonu
 * Bu blok, duplicate (çift) log yazımını önler.
 * Sadece Console log provider'ı aktif edilir.
 */
builder.Logging.ClearProviders();
builder.Logging.AddConsole();

// Add services to the container.

// JSON serialization'ı Newtonsoft.Json ile değiştir
builder.Services.AddControllers().AddNewtonsoftJson(opts =>
    {
        opts.SerializerSettings.Converters = new List<Newtonsoft.Json.JsonConverter> { new StringEnumConverter() };
        opts.SerializerSettings.ContractResolver = new Newtonsoft.Json.Serialization.DefaultContractResolver();
    });


// Database Service
builder.Services.AddSingleton<IDatabaseService, DatabaseService>();

// Authentication servisini DI container'a ekle
builder.Services.AddScoped<IAuthService, AuthService>();

// Project servisini DI container'a ekle
builder.Services.AddSingleton<IProjectService, ProjectService>();

// Process servisini DI container'a ekle
builder.Services.AddSingleton<IProcessService, ProcessService>();

// Process update servisini DI container'a ekle
builder.Services.AddSingleton<IProcessUpdateService, ProcessUpdateService>();

// Google Sheets servisini DI container'a ekle
builder.Services.AddSingleton<IGoogleSheetsService, GoogleSheetsService>();

// NLP servisini DI container'a ekle
builder.Services.AddSingleton<INlpService, NlpService>();

// Gemini servisini DI container'a ekle
builder.Services.AddSingleton<IGeminiService, GeminiService>();

// OpenAI servisini DI container'a ekle
builder.Services.AddSingleton<IOpenAIService, OpenAIService>();

// Text servisini DI container'a ekle
builder.Services.AddSingleton<ITextService, TextService>();

// Term servisini DI container'a ekle
builder.Services.AddSingleton<ITermService, TermService>();

// Context servisini DI container'a ekle
builder.Services.AddSingleton<IContextService, ContextService>();


// Process task handler'ları DI container'a ekle
builder.Services.AddSingleton<IProcessTaskHandler, ProjectCreationTaskHandler>();
builder.Services.AddSingleton<IProcessTaskHandler, TextTranslationTaskHandler>();
builder.Services.AddSingleton<IProcessTaskHandler, TermsDetectionTaskHandler>();
builder.Services.AddSingleton<IProcessTaskHandler, TermsTranslationTaskHandler>();
builder.Services.AddSingleton<IProcessTaskHandler, ExportToGoogleSheetsTaskHandler>();
builder.Services.AddSingleton<IProcessTaskHandler, WebScrapingTaskHandler>();
builder.Services.AddSingleton<IProcessTaskHandler, ContextCreationTaskHandler>();
builder.Services.AddSingleton<IProcessTaskHandler, ProjectSyncTaskHandler>();

// Web Scraper servisini DI container'a ekle
builder.Services.AddSingleton<IWebScraperService, WebScraperService>();

// Placeholder servisini DI container'a ekle
builder.Services.AddSingleton<IPlaceholderService, PlaceholderService>();

builder.Services.AddControllers();
// Learn more about configuring OpenAPI at https://aka.ms/aspnet/openapi
builder.Services.AddOpenApi();

WebApplication? app;

try
{
    app = builder.Build();
}
catch (Exception ex)
{
    Console.WriteLine($"❌ WebApplication oluşturulurken hata oluştu: {ex.Message}");
    throw;
}

var _databaseService = app.Services.GetRequiredService<IDatabaseService>();
using (var client = _databaseService.GetContext())
{

    // Google Sheets servisini otomatik olarak başlat
    try
    {
        var googleSheetsService = app.Services.GetRequiredService<IGoogleSheetsService>();

        // service_account verisini çek
        var serviceAccountData = await client.MainData.SingleAsync(x => x.Name == "service_account");

        if (serviceAccountData != null)
        {
            // Google Sheets servisini başlat
            var initResult = await googleSheetsService.InitializeServiceAsync(serviceAccountData.Value);

            if (initResult.Success)
            {
                Console.WriteLine("✅ Google Sheets servisi başarıyla başlatıldı.");
            }
            else
            {
                Console.WriteLine($"❌ Google Sheets servisi başlatılamadı: {initResult.ErrorMessage}");
            }
        }
        else
        {
            Console.WriteLine("⚠️ Main tablosunda 'service_account' verisi bulunamadı.");
        }
    }
    catch (Exception ex)
    {
        Console.WriteLine($"❌ Google Sheets servisi başlatılırken hata oluştu: {ex.Message}");
    }


    // Gemini servisini başlat
    try
    {
        var geminiService = app.Services.GetRequiredService<IGeminiService>();


        // gemini_api verisini çek
        var gemini_api = await client.MainData.SingleAsync(x => x.Name == "gemini_api");

        // Gemini servisini başlat
        try
        {
            var initialized = geminiService.InitializeAsync(gemini_api.Value);
            if (initialized)
            {
                Console.WriteLine("✅ Gemini servisi başarıyla başlatıldı.");
            }
            else
            {
                Console.WriteLine("❌ Gemini servisi başlatılamadı.");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Gemini servisi başlatılırken hata oluştu: {ex.Message}");
        }

    }
    catch (Exception ex)
    {
        Console.WriteLine($"❌ Gemini servisi yapılandırılırken hata oluştu: {ex.Message}");
    }

    // OpenAI servisini başlat
    try
    {
        var openAiService = app.Services.GetRequiredService<IOpenAIService>();

        // openai_api verisini çek
        var openAiApi = await client.MainData.SingleAsync(x => x.Name == "openai_api");

        if (openAiApi != null)
        {
            var initialized = await openAiService.InitializeAsync(openAiApi.Value);
            if (initialized)
            {
                Console.WriteLine("✅ OpenAI servisi başarıyla başlatıldı.");
            }
            else
            {
                Console.WriteLine("❌ OpenAI servisi başlatılamadı.");
            }
        }
        else
        {
            Console.WriteLine("⚠️ Main tablosunda 'openai_api' verisi bulunamadı.");
        }
    }
    catch (Exception ex)
    {
        Console.WriteLine($"❌ OpenAI servisi yapılandırılırken hata oluştu: {ex.Message}");
    }

}
// NLP servisini başlat ve TextProcessingHelper'a set et
try
{
    var nlpService = app.Services.GetRequiredService<INlpService>();

    // TextProcessingHelper'a NLP servisini set et
    TextProcessingHelper.SetNlpService(nlpService);

    // NLP servisini arka planda başlat (blocking olmayan)
    _ = Task.Run(async () =>
    {
        try
        {
            var initialized = await nlpService.InitializeAsync();
            if (initialized)
            {
                Console.WriteLine("✅ NLP servisi başarıyla başlatıldı.");
            }
            else
            {
                Console.WriteLine("❌ NLP servisi başlatılamadı.");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ NLP servisi başlatılırken hata oluştu: {ex.Message}");
        }
    });
}
catch (Exception ex)
{
    Console.WriteLine($"❌ NLP servisi yapılandırılırken hata oluştu: {ex.Message}");
}

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.MapOpenApi();
}

// Development ortamında HTTPS redirection'ı devre dışı bırak
if (!app.Environment.IsDevelopment())
{
    app.UseHttpsRedirection();
}

// Authentication middleware'ini ekle
app.UseMiddleware<AuthenticationMiddleware>();

app.UseAuthorization();

app.MapControllers();

/// <summary>
/// Ana sayfa endpoint'i - API'nin çalıştığını doğrular
/// </summary>
app.MapGet("/", () => Results.Ok(new
{
    message = "Translation Agent Server API",
    version = "1.0.0",
    status = "running",
    timestamp = DateTime.UtcNow,
    endpoints = new
    {
        health = "/health",
        swagger = "/swagger",
        auth = new
        {
            login = "/api/auth/login",
            logout = "/api/auth/logout",
            status = "/api/auth/status"
        },
        projects = new
        {
            list = "/api/project",
            create = "/api/project",
            get = "/api/project/{id}",
            update = "/api/project/{id}",
            delete = "/api/project/{id}",
            processes = "/api/project/{projectId}/processes",
            filter = "/api/project/filter",
            processesFilter = "/api/project/{projectId}/processes/filter"
        },
        processes = new
        {
            get = "/api/process/{id}",
            createandstart = "/api/process",
            update = "/api/process/{id}",
            delete = "/api/process/{id}",
            cancel = "/api/process/{id}/cancel",
            filter = "/api/process/project/{projectId}/filter"
        },
        googleSheets = new
        {
            initialize = "/api/googlesheets/initialize",
            status = "/api/googlesheets/status",
            spreadsheets = "/api/googlesheets/spreadsheets",
            createSpreadsheet = "/api/googlesheets/spreadsheets",
            getSpreadsheet = "/api/googlesheets/spreadsheets/{spreadsheetId}",
            sheets = "/api/googlesheets/spreadsheets/{spreadsheetId}/sheets",
            createSheet = "/api/googlesheets/spreadsheets/{spreadsheetId}/sheets",
            deleteSheet = "/api/googlesheets/spreadsheets/{spreadsheetId}/sheets/{sheetId}",
            sheetData = "/api/googlesheets/spreadsheets/{spreadsheetId}/sheets/{sheetName}/data",
            rangeData = "/api/googlesheets/spreadsheets/{spreadsheetId}/range?range={range}",
            addRow = "/api/googlesheets/spreadsheets/{spreadsheetId}/sheets/{sheetName}/rows",
            addRows = "/api/googlesheets/spreadsheets/{spreadsheetId}/sheets/{sheetName}/rows/batch",
            deleteRow = "/api/googlesheets/spreadsheets/{spreadsheetId}/sheets/{sheetId}/rows/{rowIndex}",
            deleteRows = "/api/googlesheets/spreadsheets/{spreadsheetId}/sheets/{sheetId}/rows?startRowIndex={start}&endRowIndex={end}",
            updateCell = "/api/googlesheets/spreadsheets/{spreadsheetId}/cell?range={range}",
            updateRange = "/api/googlesheets/spreadsheets/{spreadsheetId}/range?range={range}",
            batchUpdate = "/api/googlesheets/spreadsheets/{spreadsheetId}/batch",
            validateHeader = "/api/googlesheets/validate-header",
            validateTextsColumns = "/api/googlesheets/validate-texts-columns/{spreadsheetId}/{sheetName}",
            validateTermsColumns = "/api/googlesheets/validate-terms-columns/{spreadsheetId}/{sheetName}",
            createTerimceSheet = "/api/googlesheets/spreadsheets/{spreadsheetId}/create-terimce-sheet"
        },
        texts = new
        {
            list = "/api/text/{projectId}?page={page}&pageSize={pageSize}&namespaceFilter={namespace}&keyFilter={key}&enFilter={en}&trFilter={tr}&statusFilter={status}",
            get = "/api/text/{projectId}/{id}",
            create = "/api/text/{projectId}",
            update = "/api/text/{projectId}/{id}",
            delete = "/api/text/{projectId}/{id}",
            similar = "/api/text/{projectId}/similar",
            bulk = "/api/text/{projectId}/bulk",
            statistics = "/api/text/{projectId}/statistics",
            filter = "/api/text/{projectId}/filter"
        },
        terms = new
        {
            list = "/api/term/{projectId}",
            get = "/api/term/{projectId}/{id}",
            create = "/api/term/{projectId}",
            bulk = "/api/term/{projectId}/bulk",
            update = "/api/term/{projectId}/{id}",
            delete = "/api/term/{projectId}/{id}",
            statistics = "/api/term/{projectId}/statistics",
            filter = "/api/term/{projectId}/filter"
        },
        contexts = new
        {
            list = "/api/context/{projectId}",
            get = "/api/context/{projectId}/{id}",
            create = "/api/context/{projectId}",
            update = "/api/context/{projectId}/{id}",
            delete = "/api/context/{projectId}/{id}",
            similar = "/api/context/{projectId}/similar?limit={limit}",
            bulk = "/api/context/{projectId}/bulk",
            statistics = "/api/context/{projectId}/statistics",
            filter = "/api/context/{projectId}/filter"
        },
        webScraper = new
        {
            scrapeContent = "/api/webscraper/scrape?url={url}",
            scrapeElements = "/api/webscraper/elements?url={url}&selector={selector}",
            scrapeWebsite = "/api/webscraper/website?url={url}&maxDepth={maxDepth}&maxPages={maxPages}&urlPattern={urlPattern}&contentPattern={contentPattern}",
            initialize = "/api/webscraper/initialize",
            close = "/api/webscraper/close"
        },
        placeholders = new
        {
            detect = "/api/placeholder/detect",
            process = "/api/placeholder/process"
        },
    }
}));

app.Run();
