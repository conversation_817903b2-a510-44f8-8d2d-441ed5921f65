{"mcpServers": {"context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"], "env": {"DEFAULT_MINIMUM_TOKENS": ""}}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "env": {"MEMORY_FILE_PATH": "G:\\repos\\TranslationAgentServer\\memory.json"}, "fromGalleryId": "modelcontextprotocol.servers_memory", "disabled": true, "alwaysAllow": []}, "supabase": {"command": "cmd", "args": ["/c", "npx", "-y", "@supabase/mcp-server-supabase@latest", "--read-only", "--project-ref=dbzlkykhinyktgjhuorb"], "env": {"SUPABASE_ACCESS_TOKEN": "********************************************"}}, "Playwright": {"command": "npx", "args": ["-y", "@executeautomation/playwright-mcp-server"], "env": {}, "fromGalleryId": "executeautomation.mcp-playwright"}, "interactive-feedback-mcp": {"command": "uv", "args": ["--directory", "G:\\repos\\translation_agent\\interactive-feedback-mcp", "run", "server.py"], "timeout": 600, "autoApprove": ["interactive_feedback"], "disabled": true, "alwaysAllow": []}}}