using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Playwright;
using TranslationAgentServer.Data;
using TranslationAgentServer.Interfaces;
using TranslationAgentServer.Models;

namespace TranslationAgentServer.Services
{
    /// <summary>
    /// Playwright kütüphanesi kullanarak web sitelerinden içerik çıkaran servis implementasyonu
    /// </summary>
    public class WebScraperService : IWebScraperService
    {
        private readonly ILogger<WebScraperService> _logger;
        private readonly IDatabaseService _databaseService;
        private IPlaywright? _playwright;
        private IBrowser? _browser;
        private readonly SemaphoreSlim _semaphore = new SemaphoreSlim(1, 1);

        public WebScraperService(ILogger<WebScraperService> logger, IDatabaseService databaseService)
        {
            _logger = logger;
            _databaseService = databaseService;
        }

        /// <summary>
        /// Playwright tarayıcısını başlatır
        /// </summary>
        /// <returns>Başlatma işlemi sonucu</returns>
        public async Task<bool> InitializeBrowserAsync()
        {
            try
            {
                await _semaphore.WaitAsync();


                if (_browser != null && _playwright != null)
                {
                    _logger.LogInformation("Tarayıcı zaten başlatılmış");
                    return true;
                }

                _logger.LogInformation("Playwright tarayıcısı başlatılıyor");
                _playwright = await Playwright.CreateAsync();

                // Docker container ortamı için optimize edilmiş browser seçenekleri
                // --no-sandbox ve --disable-setuid-sandbox seçenekleri Docker ortamında çalışırken gereklidir
                var launchOptions = new BrowserTypeLaunchOptions
                {
                    Headless = true,
                    Args = new[]
                    {
                        "--no-sandbox",
                        "--enable-webgl",
                        "--lang=en-US",
                        "--disable-setuid-sandbox",
                        "--disable-dev-shm-usage",
                        "--disable-accelerated-2d-canvas",
                        "--no-first-run",
                        "--no-zygote",
                        "--disable-gpu",
                        "--disable-background-timer-throttling",
                        "--disable-backgrounding-occluded-windows",
                        "--disable-renderer-backgrounding",
                        "--disable-features=TranslateUI",
                        "--disable-ipc-flooding-protection",
                        "--disable-blink-features=AutomationControlled",
        "--disable-web-security",
        "--disable-features=VizDisplayCompositor",
        "--disable-background-networking",
        "--disable-client-side-phishing-detection",
        "--disable-default-apps",
        "--disable-hang-monitor",
        "--disable-popup-blocking",
        "--disable-prompt-on-repost",
        "--disable-sync",
        "--metrics-recording-only",
        "--no-default-browser-check",
        "--safebrowsing-disable-auto-update",
        "--enable-automation=false",
        "--password-store=basic",
        "--use-mock-keychain"
                    }
                };


                _browser = await _playwright.Chromium.LaunchAsync(launchOptions);

                _logger.LogInformation("Playwright tarayıcısı başarıyla başlatıldı");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Playwright tarayıcısı başlatılırken hata oluştu");
                return false;
            }
            finally
            {
                _semaphore.Release();
            }
        }

        /// <summary>
        /// Playwright tarayıcısını kapatır
        /// </summary>
        /// <returns>Kapatma işlemi sonucu</returns>
        public async Task<bool> CloseBrowserAsync()
        {
            try
            {
                await _semaphore.WaitAsync();


                if (_browser == null || _playwright == null)
                {
                    _logger.LogInformation("Tarayıcı zaten kapalı");
                    return true;
                }

                _logger.LogInformation("Playwright tarayıcısı kapatılıyor");
                await _browser.DisposeAsync();
                _playwright.Dispose();


                _browser = null;
                _playwright = null;

                _logger.LogInformation("Playwright tarayıcısı başarıyla kapatıldı");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Playwright tarayıcısı kapatılırken hata oluştu");
                return false;
            }
            finally
            {
                _semaphore.Release();
            }
        }

        /// <summary>
        /// Belirtilen URL'den içerik çıkarır
        /// </summary>
        /// <param name="url">İçerik çıkarılacak web sitesi URL'si</param>
        /// <returns>Çıkarılan içerik</returns>
        public async Task<string> ScrapeContentAsync(string url)
        {
            try
            {
                if (_browser == null)
                {
                    await InitializeBrowserAsync();
                }

                if (_browser == null)
                {
                    throw new InvalidOperationException("Tarayıcı başlatılamadı");
                }

                _logger.LogInformation("İçerik çıkarılıyor: {Url}", url);
                var page = await _browser.NewPageAsync();


                try
                {
                    await page.GotoAsync(url, new PageGotoOptions
                    {
                        WaitUntil = WaitUntilState.NetworkIdle,
                        Timeout = 60000
                    });

                    // Sayfanın tam içeriğini al
                    var content = await page.TextContentAsync("body");
                    // Normalizasyon işlemleri
                    content = NormalizeWhitespace(content);
                    return content;
                }
                finally
                {
                    await page.CloseAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "İçerik çıkarılırken hata oluştu: {Url}", url);
                return string.Empty;
            }
        }

        /// <summary>
        /// Belirtilen URL'den belirli HTML elementlerini çıkarır
        /// </summary>
        /// <param name="url">İçerik çıkarılacak web sitesi URL'si</param>
        /// <param name="selector">CSS veya XPath seçici</param>
        /// <returns>Seçilen elementlerin içeriği</returns>
        public async Task<List<string>> ScrapeElementsAsync(string url, string selector)
        {
            try
            {
                if (_browser == null)
                {
                    await InitializeBrowserAsync();
                }

                if (_browser == null)
                {
                    throw new InvalidOperationException("Tarayıcı başlatılamadı");
                }

                _logger.LogInformation("Elementler çıkarılıyor: {Url}, Selector: {Selector}", url, selector);
                var page = await _browser.NewPageAsync();


                try
                {
                    await page.GotoAsync(url, new PageGotoOptions
                    {
                        WaitUntil = WaitUntilState.NetworkIdle,
                        Timeout = 60000
                    });

                    // Belirtilen seçiciye uyan tüm elementleri bul
                    var elements = await page.QuerySelectorAllAsync(selector);
                    var results = new List<string>();

                    foreach (var element in elements)
                    {
                        var text = await element.TextContentAsync();
                        if (!string.IsNullOrWhiteSpace(text))
                        {
                            results.Add(text.Trim());
                        }
                    }

                    return results;
                }
                finally
                {
                    await page.CloseAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Elementler çıkarılırken hata oluştu: {Url}, Selector: {Selector}", url, selector);
                return new List<string>();
            }
        }

        /// <summary>
        /// Belirtilen URL'den başlayarak belirli derinlikte ve sayfa sayısında içerik çıkarır
        /// </summary>
        /// <param name="url">Başlangıç URL'si</param>
        /// <param name="maxDepth">Maksimum derinlik seviyesi</param>
        /// <param name="maxPages">Maksimum sayfa sayısı</param>
        /// <param name="urlFilter">URL filtreleme fonksiyonu (isteğe bağlı)</param>
        /// <param name="contentFilter">İçerik filtreleme fonksiyonu (isteğe bağlı)</param>
        /// <returns>URL ve içerik eşleştirmelerini içeren sözlük</returns>
        public async Task<List<WebScrapCreateDto>> ScrapeWebsiteAsync(string url, string selector = null, int maxDepth = 1, int maxPages = 10, Func<string, bool>? urlFilter = null, Func<string, bool>? contentFilter = null, Process process = null)
        {
            try
            {
                if (_browser == null)
                {
                    await InitializeBrowserAsync();
                }

                if (_browser == null)
                {
                    throw new InvalidOperationException("Tarayıcı başlatılamadı");
                }

                _logger.LogInformation("Web sitesi kazınıyor: {Url}, MaxDepth: {MaxDepth}, MaxPages: {MaxPages}", url, maxDepth, maxPages);

                var results = new List<WebScrapCreateDto>();
                var visitedUrls = new HashSet<string>();
                var urlQueue = new Queue<(string Url, int Depth)>();

                // Başlangıç URL'sini kuyruğa ekle
                urlQueue.Enqueue((url, 0));
                visitedUrls.Add(url);
                if (process != null)
                {
                    process.Result = "Web sitesi kazıma başladı.";
                    process.Progress = 5;
                    await UpdateProcess(process);
                }

                while (urlQueue.Count > 0 && results.Count < maxPages)
                {
                    if (process != null)
                    {
                        process.Result = $"İşleniyor: {maxPages - results.Count} URL kaldı.";
                        // maks 80 olarak ilerleme güncelle
                        process.Progress = Math.Min(80, 5 + (results.Count * 75 / maxPages));
                        await UpdateProcess(process);
                    }

                    var (currentUrl, currentDepth) = urlQueue.Dequeue();

                    // Maksimum derinliğe ulaşıldıysa devam etme
                    if (currentDepth > maxDepth)
                    {
                        continue;
                    }

                    BrowserNewPageOptions options = new BrowserNewPageOptions
                    {
                        UserAgent = "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                        ViewportSize = new ViewportSize { Width = 1920, Height = 1080 },
                        DeviceScaleFactor = 1,
                        IsMobile = false,
                        HasTouch = false,
                        Locale = "tr-TR",
                        TimezoneId = "Europe/Istanbul",
                        ExtraHTTPHeaders = new Dictionary<string, string>
    {
        { "Accept-Language", "tr-TR,tr;q=0.9,en-US;q=0.8,en;q=0.7" },
        { "Accept-Encoding", "gzip, deflate, br" },
        { "Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8" },
        { "Sec-Fetch-Site", "none" },
        { "Sec-Fetch-Mode", "navigate" },
        { "Sec-Fetch-User", "?1" },
        { "Sec-Fetch-Dest", "document" }
    }
                    };
                    // Sayfayı aç ve içeriğini al
                    var page = await _browser.NewPageAsync(options);
                    var script = @"
    // WebDriver property'sini tamamen kaldır
    Object.defineProperty(navigator, 'webdriver', {
        get: () => undefined,
    });
    
    // Chrome runtime ekle
    window.chrome = {
        runtime: {},
        loadTimes: function() {},
        csi: function() {}
    };
    
    // Plugin array'ini güncelle
    Object.defineProperty(navigator, 'plugins', {
        get: () => [
            {
                0: {type: 'application/x-google-chrome-pdf', suffixes: 'pdf', description: 'Portable Document Format', filename: 'internal-pdf-viewer'},
                description: 'Portable Document Format',
                filename: 'internal-pdf-viewer',
                length: 1,
                name: 'Chrome PDF Plugin'
            }
        ]
    });
    
    // Languages array'ini güncelle
    Object.defineProperty(navigator, 'languages', {
        get: () => ['tr-TR', 'tr', 'en-US', 'en']
    });
    
    // Permissions API'sini override et
    const originalQuery = window.navigator.permissions.query;
    window.navigator.permissions.query = (parameters) => (
        parameters.name === 'notifications' ?
            Promise.resolve({ state: Cypress.env('NOTIFICATION_PERMISSION') || 'granted' }) :
            originalQuery(parameters)
    );
    
    // WebGL vendor bilgilerini gizle
    const getParameter = WebGLRenderingContext.getParameter;
    WebGLRenderingContext.prototype.getParameter = function(parameter) {
        if (parameter === 37445) {
            return 'Intel Inc.';
        }
        if (parameter === 37446) {
            return 'Intel Iris OpenGL Engine';
        }
        return getParameter(parameter);
    };
";
                    await page.AddInitScriptAsync(script);

                    await page.RouteAsync("**/*", async route =>
                    {
                        var request = route.Request;
                        var resourceType = request.ResourceType;
                        var url = request.Url.ToLower();

                        // Gereksiz resource türlerini engelle

                        if (resourceType == "image" ||

                            resourceType == "stylesheet" ||

                            resourceType == "font" ||
                            resourceType == "media")
                        {
                            await route.AbortAsync();
                            return;
                        }


                        await route.ContinueAsync();
                    });
                    try
                    {
                        await page.GotoAsync(currentUrl, new PageGotoOptions
                        {
                            WaitUntil = WaitUntilState.DOMContentLoaded,
                            Timeout = 60000
                        });

                        if (selector != null)
                        {
                            var elements = await page.QuerySelectorAllAsync(selector);
                            var allContent = new List<string>();

                            foreach (var element in elements)
                            {
                                var text = await element.TextContentAsync();
                                if (!string.IsNullOrWhiteSpace(text))
                                {
                                    allContent.Add(text.Trim());
                                }
                            }

                            var content = string.Join("\n\n", allContent);
                            if (contentFilter == null || contentFilter(content))
                            {
                                results.Add(new WebScrapCreateDto { Url = currentUrl, Content = content, Processed = false });
                            }

                        }
                        else
                        {
                            var content = await page.TextContentAsync("body");
                            // Normalizasyon işlemleri
                            content = NormalizeWhitespace(content);
                            // İçerik filtreleme kontrolü
                            if (contentFilter == null || contentFilter(content))
                            {
                                results.Add(new WebScrapCreateDto { Url = currentUrl, Content = content, Processed = false });
                            }

                        }


                        // Maksimum sayfa sayısına ulaşıldıysa döngüyü sonlandır

                        if (results.Count >= maxPages)
                        {
                            break;
                        }

                        // Bir sonraki derinlik seviyesine geçilecekse bağlantıları topla
                        if (currentDepth < maxDepth)
                        {
                            // Sayfadaki tüm bağlantıları topla
                            var links = await page.QuerySelectorAllAsync("a[href]");

                            foreach (var link in links)
                            {
                                var href = await link.GetAttributeAsync("href");

                                if (string.IsNullOrEmpty(href) || href.StartsWith("#") || href.StartsWith("javascript:"))
                                {
                                    continue;
                                }

                                // Göreceli URL'leri mutlak URL'lere dönüştür
                                var absoluteUrl = new Uri(new Uri(currentUrl), href).AbsoluteUri;

                                // URL filtreleme kontrolü
                                if ((urlFilter == null || urlFilter(absoluteUrl)) && !visitedUrls.Contains(absoluteUrl))
                                {
                                    urlQueue.Enqueue((absoluteUrl, currentDepth + 1));
                                    visitedUrls.Add(absoluteUrl);
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Sayfa işlenirken hata oluştu: {Url}", currentUrl);
                    }
                    finally
                    {
                        await page.CloseAsync();
                    }
                }
                if (process != null)
                {
                    process.Progress = 80;
                    await UpdateProcess(process);
                }

                _logger.LogInformation("Web sitesi kazıma tamamlandı. Toplam sayfa sayısı: {Count}", results.Count);
                return results;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Web sitesi kazınırken hata oluştu: {Url}", url);
                return new List<WebScrapCreateDto>();
            }
        }

        private static string NormalizeWhitespace(string input)
        {
            if (string.IsNullOrEmpty(input)) return input;

            // Ardışık \n karakterlerini tek \n'e indir
            string cleaned = Regex.Replace(input, @"(\n\s*){2,}", "\n");

            // Ardışık \t karakterlerini tek \t'ye indir
            cleaned = Regex.Replace(cleaned, @"(\t\s*){2,}", "\t");

            return cleaned.Trim(); // Başta veya sonda boşlukları da temizler
        }

        #region CRUD Operations

        /// <summary>
        /// Yeni bir WebScrap kaydı oluşturur
        /// </summary>
        /// <param name="webScrapDto">Oluşturulacak WebScrap verisi</param>
        /// <returns>Oluşturulan WebScrap</returns>
        public async Task<WebScrap> CreateWebScrapAsync(WebScrapCreateDto webScrapDto, int projectId)
        {
            try
            {
                var webScrap = new WebScrap
                {
                    Url = webScrapDto.Url,
                    Content = webScrapDto.Content,
                    CreatedAt = DateTime.UtcNow
                };
                await using var client = _databaseService.GetProjectContext(projectId);
                client.WebScraps.Add(webScrap);
                await client.SaveChangesAsync();

                _logger.LogInformation("WebScrap kaydı oluşturuldu: {Id}", webScrap.Id);
                return webScrap;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "WebScrap oluşturulurken hata oluştu");
                throw;
            }
        }

        /// <summary>
        /// ID'ye göre WebScrap kaydını getirir
        /// </summary>
        /// <param name="id">WebScrap ID'si</param>
        /// <returns>WebScrap kaydı veya null</returns>
        public async Task<WebScrap?> GetWebScrapByIdAsync(long id, int projectId)
        {
            try
            {
                await using var client = _databaseService.GetProjectContext(projectId);
                return await client.WebScraps.FindAsync(id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "WebScrap getirilirken hata oluştu: {Id}", id);
                throw;
            }
        }

        /// <summary>
        /// Tüm WebScrap kayıtlarını getirir
        /// </summary>
        /// <param name="skip">Atlanacak kayıt sayısı</param>
        /// <param name="take">Alınacak kayıt sayısı</param>
        /// <returns>WebScrap kayıtları listesi</returns>
        public async Task<List<WebScrap>> GetAllWebScrapsAsync(int skip = 0, int take = 100, int projectId = 0)
        {
            try
            {
                await using var client = _databaseService.GetProjectContext(projectId);
                return await client.WebScraps
                    .OrderByDescending(w => w.CreatedAt)
                    .Skip(skip)
                    .Take(take)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "WebScrap kayıtları getirilirken hata oluştu");
                throw;
            }
        }

        /// <summary>
        /// URL'ye göre WebScrap kayıtlarını getirir
        /// </summary>
        /// <param name="url">Aranacak URL</param>
        /// <returns>WebScrap kayıtları listesi</returns>
        public async Task<List<WebScrap>> GetWebScrapsByUrlAsync(string url, int projectId)
        {
            try
            {
                await using var client = _databaseService.GetProjectContext(projectId);
                return await client.WebScraps
                    .Where(w => w.Url != null && w.Url.Contains(url))
                    .OrderByDescending(w => w.CreatedAt)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "URL'ye göre WebScrap kayıtları getirilirken hata oluştu: {Url}", url);
                throw;
            }
        }

        /// <summary>
        /// WebScrap kaydını günceller
        /// </summary>
        /// <param name="id">Güncellenecek WebScrap ID'si</param>
        /// <param name="webScrapDto">Güncelleme verisi</param>
        /// <returns>Güncellenmiş WebScrap veya null</returns>
        public async Task<WebScrap?> UpdateWebScrapAsync(long id, WebScrapUpdateDto webScrapDto, int projectId)
        {
            try
            {
                await using var client = _databaseService.GetProjectContext(projectId);
                var webScrap = await client.WebScraps.FindAsync(id);
                if (webScrap == null)
                {
                    return null;
                }

                if (webScrapDto.Url != null)
                    webScrap.Url = webScrapDto.Url;


                if (webScrapDto.Content != null)
                    webScrap.Content = webScrapDto.Content;

                await client.SaveChangesAsync();

                _logger.LogInformation("WebScrap kaydı güncellendi: {Id}", id);
                return webScrap;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "WebScrap güncellenirken hata oluştu: {Id}", id);
                throw;
            }
        }

        /// <summary>
        /// WebScrap kaydını siler
        /// </summary>
        /// <param name="id">Silinecek WebScrap ID'si</param>
        /// <returns>Silme işlemi başarılı ise true</returns>
        public async Task<bool> DeleteWebScrapAsync(long id, int projectId)
        {
            try
            {
                await using var client = _databaseService.GetProjectContext(projectId);
                var webScrap = await client.WebScraps.FindAsync(id);
                if (webScrap == null)
                {
                    return false;
                }

                client.WebScraps.Remove(webScrap);
                await client.SaveChangesAsync();

                _logger.LogInformation("WebScrap kaydı silindi: {Id}", id);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "WebScrap silinirken hata oluştu: {Id}", id);
                throw;
            }
        }

        /// <summary>
        /// WebScrap kayıtlarını toplu olarak ekler
        /// </summary>
        /// <param name="webscraps">eklenecek WebScrap'leri</param>
        /// <returns>Silme işlemi başarılı ise true</returns>
        public async Task<bool> CreateWebScrapsAsync(List<WebScrapCreateDto> webscraps, int projectId)
        {
            try
            {
                await using var client = _databaseService.GetProjectContext(projectId);
                var webScraps = webscraps.Select(x => new WebScrap
                {
                    Url = x.Url,
                    Content = x.Content,
                    CreatedAt = DateTime.UtcNow
                }).ToList();
                client.WebScraps.AddRange(webScraps);
                var result = await client.SaveChangesAsync();
                return result > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "WebScrap toplu olarak eklenirken hata oluştu");
                throw;
            }

        }

        /// <summary>
        /// WebScrap istatistiklerini getirir
        /// </summary>
        /// <returns>WebScrap istatistikleri</returns>
        public async Task<WebScrapStatistics> GetWebScrapStatisticsAsync(int projectId)
        {
            try
            {
                await using var client = _databaseService.GetProjectContext(projectId);
                var totalCount = await client.WebScraps.CountAsync();
                var lastScrapedAt = await client.WebScraps
                    .OrderByDescending(w => w.CreatedAt)
                    .Select(w => w.CreatedAt)
                    .FirstOrDefaultAsync();

                return new WebScrapStatistics
                {
                    TotalCount = totalCount,
                    LastScrapedAt = lastScrapedAt == default ? null : lastScrapedAt
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "WebScrap istatistikleri getirilirken hata oluştu");
                throw;
            }
        }

        /// <summary>
        /// URL'den içerik çıkarır ve veritabanına kaydeder
        /// </summary>
        /// <param name="url">İçerik çıkarılacak URL</param>
        /// <returns>Oluşturulan WebScrap kaydı</returns>
        public async Task<WebScrap> ScrapeAndSaveAsync(string url, int projectId)
        {
            try
            {
                _logger.LogInformation("URL'den içerik çıkarılıyor ve kaydediliyor: {Url}", url);


                var content = await ScrapeContentAsync(url);


                var webScrapDto = new WebScrapCreateDto
                {
                    Url = url,
                    Content = content
                };

                return await CreateWebScrapAsync(webScrapDto, projectId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "URL'den içerik çıkarılıp kaydedilirken hata oluştu: {Url}", url);
                throw;
            }
        }

        #endregion


        // İlerleme güncelleme helper fonksiyonu
        private async Task UpdateProcess(Process existingProcess)
        {
            _logger.LogInformation("İşlem güncelleniyor: {ProcessId}", existingProcess.Id);

            existingProcess.LastPing = DateTime.UtcNow;
            existingProcess.CompletedAt = (int)existingProcess.Status == (int)ProcessStatus.Completed || (int)existingProcess.Status == (int)ProcessStatus.Failed || (int)existingProcess.Status == (int)ProcessStatus.Cancelled
              ? DateTime.UtcNow : existingProcess.CompletedAt;

            await using var client = _databaseService.GetContext();
            existingProcess = client.Processes.Update(existingProcess).Entity;
            var result = await client.SaveChangesAsync();

            var updatedProcess = result > 0 ? existingProcess : null;
            if (updatedProcess != null)
            {
                _logger.LogInformation("İşlem başarıyla güncellendi: {ProcessId}", existingProcess.Id);
            }

        }
    }
}