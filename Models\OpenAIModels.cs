namespace TranslationAgentServer.Models
{
    /// <summary>
    /// OpenAI AI içerik üretim isteği için model.
    /// </summary>
    public class OpenAIContentRequest
    {
        /// <summary>
        /// Sistem talimatı (model davranışını belirler).
        /// </summary>
        public string? SystemInstruction { get; set; }

        /// <summary>
        /// Kullanılacak modelin adı (ör. "gpt-4", "gpt-3.5-turbo").
        /// </summary>
        public string? Model { get; set; }

        /// <summary>
        /// İçerik üretimi için sağlanacak olan prompt metni.
        /// </summary>
        public string Prompt { get; set; } = string.Empty;

        /// <summary>
        /// Üretilecek maksimum token sayısı.
        /// </summary>
        public int? MaxTokens { get; set; }

        /// <summary>
        /// Yaratıcılık seviyesini kontrol eden parametre (0.0 - 2.0).
        /// </summary>
        public float? Temperature { get; set; }
    }

    /// <summary>
    /// OpenAI AI içerik üretim yanıtı için model.
    /// </summary>
    public class OpenAIContentResponse
    {
        /// <summary>
        /// İşlemin başarılı olup olmadığını belirtir.
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Üretilen metin içeriği.
        /// </summary>
        public string Text { get; set; } = string.Empty;

        /// <summary>
        /// İşlem sırasında bir hata oluştuysa hata mesajını içerir.
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// İşlemin ne kadar sürdüğünü milisaniye cinsinden belirtir.
        /// </summary>
        public long ProcessingTimeMs { get; set; }

        /// <summary>
        /// İşlem sırasında kullanılan toplam token sayısı.
        /// </summary>
        public int? TokensUsed { get; set; }
    }
}