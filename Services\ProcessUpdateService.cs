using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using TranslationAgentServer.Data;
using TranslationAgentServer.Interfaces;
using TranslationAgentServer.Models;

namespace TranslationAgentServer.Services;

/// <summary>
/// İşlem güncelleme işlemlerini sağlayan servis
/// </summary>
public class ProcessUpdateService : IProcessUpdateService
{
    private readonly IDatabaseService _databaseService;
    private readonly ILogger<ProcessUpdateService> _logger;

    public ProcessUpdateService(IDatabaseService databaseService, ILogger<ProcessUpdateService> logger)
    {
        _databaseService = databaseService;
        _logger = logger;
    }

    /// <summary>
    /// Belirtilen işlemi günceller
    /// </summary>
    /// <param name="processId">Güncellenecek işlemin kimliği</param>
    /// <param name="updateAction">İşlemi güncelleyecek eylem</param>
    /// <returns>Task</returns>
    public async Task UpdateProcess(Guid processId, Action<Process> updateAction)
    {
        _logger.LogInformation("İşlem güncelleniyor: {ProcessId}", processId);

        await using var client = _databaseService.GetContext();
        var existingProcess = await client.Processes.FindAsync(processId);

        if (existingProcess == null)
        {
            _logger.LogWarning("Güncellenecek işlem bulunamadı: {ProcessId}", processId);
            return;
        }

        // Güncelleme eylemini uygula
        updateAction(existingProcess);

        // Ortak alanları güncelle
        existingProcess.LastPing = DateTime.UtcNow;
        existingProcess.CompletedAt = (int)existingProcess.Status == (int)ProcessStatus.Completed || 
                                      (int)existingProcess.Status == (int)ProcessStatus.Failed || 
                                      (int)existingProcess.Status == (int)ProcessStatus.Cancelled
            ? DateTime.UtcNow : existingProcess.CompletedAt;

        existingProcess = client.Processes.Update(existingProcess).Entity;
        var result = await client.SaveChangesAsync();

        var updatedProcess = result > 0 ? existingProcess : null;
        if (updatedProcess != null)
        {
            _logger.LogInformation("İşlem başarıyla güncellendi: {ProcessId}", processId);
        }
    }
}