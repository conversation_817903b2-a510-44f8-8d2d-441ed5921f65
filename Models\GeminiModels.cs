using System.Text.Json;

namespace TranslationAgentServer.Models;


// Configuration model
public class GeminiOptions
{
    public string ApiKey { get; set; } = string.Empty;
    public string BaseUrl { get; set; } = "https://generativelanguage.googleapis.com/v1beta";
    public int TimeoutSeconds { get; set; } = 300;
}

// Request/Response models
public class BatchEmbedRequest
{
    public List<EmbedContentRequest> requests { get; set; }
}

public class EmbedContentRequest
{
    public string model { get; set; }
    public Content content { get; set; }
    public int? outputDimensionality { get; set; }
    public string? taskType { get; set; }
}

public class Content
{
    public List<Part> parts { get; set; }
}

public class Part
{
    public string text { get; set; }
}

public class BatchEmbedResponse
{
    public List<EmbeddingResult> embeddings { get; set; }
}

public class EmbeddingResult
{
    public List<float> values { get; set; }
}

public class SingleEmbedResponse
{
    public EmbeddingResult embedding { get; set; }
}


/// <summary>
/// Gemini AI içerik üretim isteği
/// </summary>
public class GeminiContentRequest
{
    /// <summary>
    /// Üretilecek içerik için prompt/mesaj
    /// </summary>
    public string Prompt { get; set; } = string.Empty;

    /// <summary>
    /// Kullanılacak model adı (varsayılan: gemini-1.5-pro)
    /// </summary>
    public string? Model { get; set; }

    /// <summary>
    /// Maksimum token sayısı
    /// </summary>
    public int? MaxTokens { get; set; }

    /// <summary>
    /// Yaratıcılık seviyesi (0.0 - 2.0)
    /// </summary>
    public float? Temperature { get; set; }

    /// <summary>
    /// Top-p sampling parametresi
    /// </summary>
    public float? TopP { get; set; }

    /// <summary>
    /// Top-k sampling parametresi
    /// </summary>
    public int? TopK { get; set; }

    /// <summary>
    /// Sistem talimatı (model davranışını belirler)
    /// </summary>
    public string? SystemInstruction { get; set; }

    /// <summary>
    /// JSON çıktı formatı istenip istenmediği
    /// </summary>
    public bool RequireJsonOutput { get; set; } = false;

    /// <summary>
    /// Google Search grounding kullanılıp kullanılmayacağı
    /// </summary>
    public bool UseGrounding { get; set; } = false;

    /// <summary>
    /// Düşünce token bütçesi
    /// </summary>
    public int? ThinkingBudget { get; set; }
}

/// <summary>
/// Gemini AI içerik üretim yanıtı
/// </summary>
public class GeminiContentResponse
{
    /// <summary>
    /// Üretilen metin içeriği
    /// </summary>
    public string Text { get; set; } = string.Empty;

    /// <summary>
    /// JSON çıktısı (eğer istenmişse)
    /// </summary>
    public string? JsonOutput { get; set; }

    /// <summary>
    /// Kullanılan model adı
    /// </summary>
    public string Model { get; set; } = string.Empty;

    /// <summary>
    /// İşlem başarılı mı
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Hata mesajı (varsa)
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Kullanılan token sayısı
    /// </summary>
    public int? TokensUsed { get; set; }

    /// <summary>
    /// İşlem süresi (milisaniye)
    /// </summary>
    public long ProcessingTimeMs { get; set; }
}

/// <summary>
/// Gemini AI tekli embedding yanıtı
/// </summary>
public class GeminiEmbeddingResponse
{
    /// <summary>
    /// Embedding vektörü
    /// </summary>
    public float[] Embedding { get; set; } = Array.Empty<float>();

    /// <summary>
    /// Kullanılan model adı
    /// </summary>
    public string Model { get; set; } = string.Empty;

    /// <summary>
    /// İşlem başarılı mı
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Hata mesajı (varsa)
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Embedding boyutu
    /// </summary>
    public int Dimensions { get; set; }

    /// <summary>
    /// İşlem süresi (milisaniye)
    /// </summary>
    public long ProcessingTimeMs { get; set; }
}

/// <summary>
/// Gemini AI çoklu embedding yanıtı
/// </summary>
public class GeminiMultipleEmbeddingResponse
{
    /// <summary>
    /// Embedding vektörleri listesi
    /// </summary>
    public List<float[]> Embeddings { get; set; } = new();

    /// <summary>
    /// Kullanılan model adı
    /// </summary>
    public string Model { get; set; } = string.Empty;

    /// <summary>
    /// İşlem başarılı mı
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Hata mesajı (varsa)
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Embedding boyutu
    /// </summary>
    public int Dimensions { get; set; }

    /// <summary>
    /// İşlenen metin sayısı
    /// </summary>
    public int TextCount { get; set; }

    /// <summary>
    /// İşlem süresi (milisaniye)
    /// </summary>
    public long ProcessingTimeMs { get; set; }
}

/// <summary>
/// Gemini AI model bilgisi
/// </summary>
public class GeminiModel
{
    /// <summary>
    /// Model adı
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Model görünen adı
    /// </summary>
    public string DisplayName { get; set; } = string.Empty;

    /// <summary>
    /// Model açıklaması
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// Desteklenen giriş türleri
    /// </summary>
    public List<string> SupportedInputTypes { get; set; } = new();

    /// <summary>
    /// Desteklenen çıktı türleri
    /// </summary>
    public List<string> SupportedOutputTypes { get; set; } = new();

    /// <summary>
    /// Maksimum token sayısı
    /// </summary>
    public int? MaxTokens { get; set; }

    /// <summary>
    /// Model versiyonu
    /// </summary>
    public string Version { get; set; } = string.Empty;
}