
// using TranslationAgentServer.Models.Filtering;
// using System.Text.Json;
// using TranslationAgentServer.Models;

// /// <summary>
// /// Filtering örneklerini test eden metod
// /// </summary>
// public static void TestFilteringExamples()
// {
//     Console.WriteLine("=== FILTERING ÖRNEKLERI ===");

//     // TextFilterQuery örneği
//     var textFilter = new TextFilterQuery
//     {
//         Namespace = "ui",
//         Key = "button",
//         En = "Save",
//         Status = TextStatus.EN,
//         Page = 1,
//         PageSize = 10,
//         OrderBy = "CreatedAt",
//         SortDirection = SortDirection.Descending,
//         Criteria = new List<FilterCriteria>
//         {
//             new FilterCriteria
//             {
//                 Field = "En",
//                 Operator = FilterOperator.Contains,
//                 Value = "save",
//                 CaseSensitive = false
//             },
//             new FilterCriteria
//             {
//                 Field = "CreatedAt",
//                 Operator = FilterOperator.Between,
//                 Value = DateTime.Now.AddDays(-30),
//                 SecondValue = DateTime.Now
//             }
//         },
//         LogicalOperator = LogicalOperator.And
//     };

//     Console.WriteLine("TextFilterQuery JSON:");
//     Console.WriteLine(JsonSerializer.Serialize(textFilter, new JsonSerializerOptions { WriteIndented = true }));
//     Console.WriteLine();

//     // TermFilterQuery örneği
//     var termFilter = new TermFilterQuery
//     {
//         En = "user",
//         Tr = "kullanıcı",
//         Category = "technical",
//         Page = 1,
//         PageSize = 20,
//         Criteria = new List<FilterCriteria>
//         {
//             new FilterCriteria
//             {
//                 Field = "En",
//                 Operator = FilterOperator.StartsWith,
//                 Value = "user",
//                 CaseSensitive = false
//             }
//         }
//     };

//     Console.WriteLine("TermFilterQuery JSON:");
//     Console.WriteLine(JsonSerializer.Serialize(termFilter, new JsonSerializerOptions { WriteIndented = true }));
//     Console.WriteLine();

//     // ContextFilterQuery örneği
//     var contextFilter = new ContextFilterQuery
//     {
//         Category = "help",
//         Title = "Getting Started",
//         Page = 1,
//         PageSize = 15,
//         OrderBy = "Title",
//         SortDirection = SortDirection.Ascending
//     };

//     Console.WriteLine("ContextFilterQuery JSON:");
//     Console.WriteLine(JsonSerializer.Serialize(contextFilter, new JsonSerializerOptions { WriteIndented = true }));
//     Console.WriteLine();

//     // ProjectFilterQuery örneği
//     var projectFilter = new ProjectFilterQuery
//     {
//         Name = "Mobile App",
//         SpreadsheetId = "1ABC123",
//         Page = 1,
//         PageSize = 5,
//         Criteria = new List<FilterCriteria>
//         {
//             new FilterCriteria
//             {
//                 Field = "Name",
//                 Operator = FilterOperator.Contains,
//                 Value = "app",
//                 CaseSensitive = false
//             }
//         }
//     };

//     Console.WriteLine("ProjectFilterQuery JSON:");
//     Console.WriteLine(JsonSerializer.Serialize(projectFilter, new JsonSerializerOptions { WriteIndented = true }));
//     Console.WriteLine();

//     // ProcessFilterQuery örneği
//     var processFilter = new ProcessFilterQuery
//     {
//         ProjectId = 1,
//         Status = ProcessStatus.InProgress,
//         TaskType = ProcessTaskType.TextTranslation,
//         ProgressMin = 0,
//         ProgressMax = 50,
//         Page = 1,
//         PageSize = 10,
//         Criteria = new List<FilterCriteria>
//         {
//             new FilterCriteria
//             {
//                 Field = "Progress",
//                 Operator = FilterOperator.GreaterThan,
//                 Value = 25
//             }
//         }
//     };

//     Console.WriteLine("ProcessFilterQuery JSON:");
//     Console.WriteLine(JsonSerializer.Serialize(processFilter, new JsonSerializerOptions { WriteIndented = true }));
//     Console.WriteLine();

//     // Genel FilterQuery örneği (advanced filtering)
//     var generalFilter = new FilterQuery
//     {
//         Criteria = new List<FilterCriteria>
//         {
//             new FilterCriteria
//             {
//                 Field = "Name",
//                 Operator = FilterOperator.Equals,
//                 Value = "example"
//             },
//             new FilterCriteria
//             {
//                 Field = "CreatedAt",
//                 Operator = FilterOperator.GreaterThanOrEqual,
//                 Value = DateTime.Now.AddDays(-7)
//             }
//         },
//         LogicalOperator = LogicalOperator.Or,
//         Page = 1,
//         PageSize = 25,
//         OrderBy = "CreatedAt",
//         SortDirection = SortDirection.Descending
//     };

//     Console.WriteLine("Genel FilterQuery JSON:");
//     Console.WriteLine(JsonSerializer.Serialize(generalFilter, new JsonSerializerOptions { WriteIndented = true }));
//     Console.WriteLine();

//     Console.WriteLine("=== FILTERING ÖRNEKLERİ TAMAMLANDI ===");
// }
