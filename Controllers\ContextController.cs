using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using TranslationAgentServer.Interfaces;
using TranslationAgentServer.Models;
using TranslationAgentServer.Models.Filtering;
using System.Text.Json;

namespace TranslationAgentServer.Controllers;

/// <summary>
/// Context yönetimi için API controller'ı
/// Context verilerinin CRUD işlemlerini ve özel sorguları sağlar
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class ContextController : ControllerBase
{
    private readonly IContextService _contextService;
    private readonly ILogger<ContextController> _logger;

    public ContextController(IContextService contextService, ILogger<ContextController> logger)
    {
        _contextService = contextService;
        _logger = logger;
    }

    /// <summary>
    /// Tüm context'leri getirir
    /// </summary>
    /// <param name="projectId"><PERSON><PERSON> k<PERSON></param>
    /// <returns>Context listesi</returns>
    [HttpGet("{projectId}")]
    public async Task<ActionResult<List<Context>>> GetAllContexts(
        int projectId)
    {
        try
        {


            var contexts = await _contextService.GetAllContextsAsync(projectId);
            return Ok(contexts);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Context'ler getirilirken hata oluştu");
            return StatusCode(500, "Context'ler getirilirken bir hata oluştu.");
        }
    }

    /// <summary>
    /// Gelişmiş filtreleme ile context'leri getirir
    /// </summary>
    /// <param name="projectId">Proje kimliği</param>
    /// <param name="filterQuery">Filtreleme sorgusu</param>
    /// <returns>Filtrelenmiş context listesi</returns>
    [HttpPost("{projectId}/filter")]
    public async Task<ActionResult<List<Context>>> GetContextsWithFilter(int projectId, [FromBody] ContextFilterQuery filterQuery)
    {
        try
        {
            var contexts = await _contextService.GetAllContextsAsync(projectId, filterQuery);
            return Ok(contexts);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Filtreleme ile context'ler getirilirken hata oluştu. ProjectId: {ProjectId}", projectId);
            return StatusCode(500, "Filtreleme ile context'ler getirilirken bir hata oluştu.");
        }
    }

    /// <summary>
    /// ID'ye göre context getirir
    /// </summary>
    /// <param name="projectId">Proje kimliği</param>
    /// <param name="id">Context ID'si</param>
    /// <returns>Context</returns>
    [HttpGet("{projectId}/{id}")]
    public async Task<ActionResult<Context>> GetContextById(
        int projectId,
        int id)
    {
        try
        {
            var context = await _contextService.GetContextByIdAsync(id, projectId);

            if (context == null)
            {
                return NotFound($"ID'si {id} olan context bulunamadı.");
            }

            return Ok(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Context getirilirken hata oluştu. ID: {Id}", id);
            return StatusCode(500, "Context getirilirken bir hata oluştu.");
        }
    }

    /// <summary>
    /// Embedding vektörüne göre benzer context'leri bulur
    /// </summary>
    /// <param name="projectId">Proje kimliği</param>
    /// <param name="embedding">Embedding vektörü</param>
    /// <param name="limit">Sonuç limiti (varsayılan: 10, maksimum: 50)</param>
    /// <returns>Benzer context listesi</returns>
    [HttpPost("{projectId}/similar")]
    public async Task<ActionResult<List<Context>>> FindSimilarContexts(
        int projectId,
        [FromBody] float[] embedding,
        [FromQuery] int limit = 10)
    {
        try
        {
            // Parametre doğrulama
            if (embedding == null || embedding.Length == 0)
            {
                return BadRequest("Embedding vektörü boş olamaz.");
            }

            if (limit < 1 || limit > 50)
            {
                return BadRequest("Limit 1-50 arasında olmalıdır.");
            }

            var contexts = await _contextService.FindSimilarContextsAsync(embedding, limit, projectId);
            return Ok(contexts);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Benzer context'ler bulunurken hata oluştu");
            return StatusCode(500, "Benzer context'ler bulunurken bir hata oluştu.");
        }
    }

    /// <summary>
    /// Yeni context oluşturur
    /// </summary>
    /// <param name="projectId">Proje kimliği</param>
    /// <param name="contextCreateDto">Context oluşturma DTO'su</param>
    /// <returns>Oluşturulan context</returns>
    [HttpPost("{projectId}")]
    public async Task<ActionResult<Context>> CreateContext(
        int projectId,
        [FromBody] ContextCreateDto contextCreateDto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var context = await _contextService.CreateContextAsync(contextCreateDto, projectId);
            return CreatedAtAction(nameof(GetContextById), new { projectId, id = context.Id }, context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Context oluşturulurken hata oluştu");
            return StatusCode(500, "Context oluşturulurken bir hata oluştu.");
        }
    }

    /// <summary>
    /// Context günceller
    /// </summary>
    /// <param name="projectId">Proje kimliği</param>
    /// <param name="id">Context ID'si</param>
    /// <param name="contextUpdateDto">Context güncelleme DTO'su</param>
    /// <returns>Güncellenmiş context</returns>
    [HttpPut("{projectId}/{id}")]
    public async Task<ActionResult<Context>> UpdateContext(
        int projectId,
        int id,
        [FromBody] ContextUpdateDto contextUpdateDto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var context = await _contextService.UpdateContextAsync(id, contextUpdateDto, projectId);

            if (context == null)
            {
                return NotFound($"ID'si {id} olan context bulunamadı.");
            }

            return Ok(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Context güncellenirken hata oluştu. ID: {Id}", id);
            return StatusCode(500, "Context güncellenirken bir hata oluştu.");
        }
    }

    /// <summary>
    /// Context siler
    /// </summary>
    /// <param name="projectId">Proje kimliği</param>
    /// <param name="id">Context ID'si</param>
    /// <returns>Silme işlemi sonucu</returns>
    [HttpDelete("{projectId}/{id}")]
    public async Task<ActionResult> DeleteContext(
        int projectId,
        int id)
    {
        try
        {
            var result = await _contextService.DeleteContextAsync(id, projectId);

            if (!result)
            {
                return NotFound($"ID'si {id} olan context bulunamadı veya silinemedi.");
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Context silinirken hata oluştu. ID: {Id}", id);
            return StatusCode(500, "Context silinirken bir hata oluştu.");
        }
    }

    /// <summary>
    /// Context'leri toplu olarak oluşturur
    /// </summary>
    /// <param name="projectId">Proje kimliği</param>
    /// <param name="contextCreateDtos">Context oluşturma DTO listesi</param>
    /// <returns>Oluşturulan context listesi</returns>
    [HttpPost("{projectId}/bulk")]
    public async Task<ActionResult<List<Context>>> CreateContexts(
        int projectId,
        [FromBody] List<ContextCreateDto> contextCreateDtos)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            if (contextCreateDtos == null || contextCreateDtos.Count == 0)
            {
                return BadRequest("Context listesi boş olamaz.");
            }

            if (contextCreateDtos.Count > 100)
            {
                return BadRequest("Tek seferde en fazla 100 context oluşturulabilir.");
            }

            var contexts = await _contextService.CreateContextsAsync(contextCreateDtos, projectId, CancellationToken.None);
            return Ok(contexts);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Context'ler toplu olarak oluşturulurken hata oluştu");
            return StatusCode(500, "Context'ler oluşturulurken bir hata oluştu.");
        }
    }

    /// <summary>
    /// Context'leri toplu olarak günceller
    /// </summary>
    /// <param name="projectId">Proje kimliği</param>
    /// <param name="contextUpdates">Güncellenecek context listesi</param>
    /// <returns>Güncellenmiş context listesi</returns>
    [HttpPut("{projectId}/bulk")]
    public async Task<ActionResult<List<ContextUpdateDto>>> UpdateContexts(
        int projectId,
        [FromBody] List<ContextUpdateRequest> contextUpdates)
    {
        try
        {
            if (contextUpdates == null || !contextUpdates.Any())
            {
                return BadRequest("Güncelleme listesi boş olamaz.");
            }

            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            if (contextUpdates.Count > 100)
            {
                return BadRequest("Tek seferde en fazla 100 context güncellenebilir.");
            }
            var updates = contextUpdates.Select(u => (u.Id, u.UpdateDto)).ToList();
            var updatedContexts = await _contextService.UpdateContextsAsync(updates, projectId, CancellationToken.None);
            return Ok(updatedContexts);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Toplu context güncellenirken hata oluştu. Sayı: {Count}, Proje: {ProjectId}", contextUpdates?.Count ?? 0, projectId);
            return StatusCode(500, "Context'ler güncellenirken bir hata oluştu.");
        }
    }

    /// <summary>
    /// Context'leri toplu olarak siler
    /// </summary>
    /// <param name="projectId">Proje kimliği</param>
    /// <param name="ids">Silinecek context ID'leri</param>
    /// <returns>Silme işlemi sonucu</returns>
    [HttpDelete("{projectId}/bulk")]
    public async Task<ActionResult> DeleteContexts(
        int projectId,
        [FromBody] List<long> ids)
    {
        try
        {
            if (ids == null || ids.Count == 0)
            {
                return BadRequest("ID listesi boş olamaz.");
            }

            if (ids.Count > 100)
            {
                return BadRequest("Tek seferde en fazla 100 context silinebilir.");
            }

            var result = await _contextService.DeleteContextsAsync(ids, projectId);

            if (!result)
            {
                return BadRequest("Context'ler silinemedi.");
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Context'ler toplu olarak silinirken hata oluştu");
            return StatusCode(500, "Context'ler silinirken bir hata oluştu.");
        }
    }

    /// <summary>
    /// Context istatistiklerini getirir
    /// </summary>
    /// <param name="projectId">Proje kimliği</param>
    /// <returns>Context istatistikleri</returns>
    [HttpGet("{projectId}/statistics")]
    public async Task<ActionResult<ContextStatistics>> GetContextStatistics(
        int projectId)
    {
        try
        {
            var statistics = await _contextService.GetContextStatisticsAsync(projectId);
            return Ok(statistics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Context istatistikleri getirilirken hata oluştu");
            return StatusCode(500, "Context istatistikleri getirilirken bir hata oluştu.");
        }
    }
    public class ContextUpdateRequest
    {
        /// <summary>
        /// Güncellenecek context ID'si
        /// </summary>
        [Required]
        public int Id { get; set; }
        /// <summary>
        /// Düzenlenen context içeriği
        /// </summary>
        public ContextUpdateDto UpdateDto { get; set; } = null!;
    }
}