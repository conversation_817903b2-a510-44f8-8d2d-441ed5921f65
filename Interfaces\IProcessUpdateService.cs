using TranslationAgentServer.Models;

namespace TranslationAgentServer.Interfaces;

/// <summary>
/// İşlem güncelleme işlemlerini sağlayan servis arayüzü
/// </summary>
public interface IProcessUpdateService
{
    /// <summary>
    /// Belirtilen işlemi günceller
    /// </summary>
    /// <param name="processId">Güncellenecek işlemin kimliği</param>
    /// <param name="updateAction">İşlemi güncelleyecek eylem</param>
    /// <returns>Task</returns>
    Task UpdateProcess(Guid processId, Action<Process> updateAction);
}