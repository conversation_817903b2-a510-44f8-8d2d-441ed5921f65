using TranslationAgentServer.Interfaces;
using Microsoft.Extensions.Logging;
using TranslationAgentServer.Models;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using Microsoft.EntityFrameworkCore;

namespace TranslationAgentServer.Services
{
    /// <summary>
    /// Placeholder'ları tespit etme, işleme ve yönetme işlemlerini yönetir.
    /// </summary>
    public class PlaceholderService : IPlaceholderService
    {
        private readonly IDatabaseService _databaseService;
        private readonly IGoogleSheetsService _googleSheetsService;
        private readonly IGeminiService _geminiService;
        private readonly ILogger<PlaceholderService> _logger;

        /// <summary>
        /// PlaceholderService sınıfını başlatır.
        /// </summary>
        /// <param name="databaseService">Veritabanı işlemleri için servis.</param>
        /// <param name="googleSheetsService">Google Sheets işlemleri için servis.</param>
        /// <param name="geminiService">Gemini AI işlemleri için servis.</param>
        /// <param name="logger">Loglama servisi.</param>
        public PlaceholderService(
            IDatabaseService databaseService,
            IGoogleSheetsService googleSheetsService,
            IGeminiService geminiService,
            ILogger<PlaceholderService> logger)
        {
            _databaseService = databaseService;
            _googleSheetsService = googleSheetsService;
            _geminiService = geminiService;
            _logger = logger;
        }

        /// <inheritdoc />
        public async Task<Dictionary<string, List<string>>> DetectPlaceholdersAsync(Project project, List<CustomPatternDto>? customPatterns = null)
        {
            _logger.LogInformation("'{ProjectName}' projesi için placeholder tespiti süreci başlatıldı.", project.Name);

            if (string.IsNullOrEmpty(project.SpreadsheetId) || string.IsNullOrEmpty(project.TextsTable))
            {
                _logger.LogWarning("'{ProjectName}' projesi için Google Sheets ID veya sayfa adı eksik.", project.Name);
                return new Dictionary<string, List<string>>();
            }

            // RowMapper fonksiyonu: Sheet satırı + başlık -> TextCreateDto
            TextCreateDto RowMapper(List<string> row, List<string> headers)
            {
                var status = TextStatus.EN;
                var textStatus = _googleSheetsService.GetColumnValue(row, headers, "status");
                if (textStatus?.Equals("TR", StringComparison.OrdinalIgnoreCase) == true) status = TextStatus.TR;
                else if (textStatus?.Equals("DUPE", StringComparison.OrdinalIgnoreCase) == true) status = TextStatus.DUPE;
                else if (textStatus?.Equals("NULL", StringComparison.OrdinalIgnoreCase) == true) status = TextStatus.NULL;

                return new TextCreateDto
                {
                    RowID = int.TryParse(_googleSheetsService.GetColumnValue(row, headers, "row_id"), out var id) ? id : 0,
                    Namespace = _googleSheetsService.GetColumnValue(row, headers, "namespace"),
                    Key = _googleSheetsService.GetColumnValue(row, headers, "key"),
                    En = _googleSheetsService.GetColumnValue(row, headers, "en"),
                    Tr = _googleSheetsService.GetColumnValue(row, headers, "tr"),
                    Status = status
                };
            }

            await using var mainclient = _databaseService.GetContext();
            var textsColumns = await mainclient.MainData.SingleAsync(x => x.Name == "texts_columns");

            var options = new SheetProcessingOptions
            {
                HeaderTemplate = textsColumns.Value,
                SkipHeader = true
            };

            var processingResult = await _googleSheetsService.ProcessSheetDataAsync<TextCreateDto>(
                project.SpreadsheetId,
                project.TextsTable,
                options,
                RowMapper
            );

            if (!processingResult.Success || processingResult.ProcessedItems == null || !processingResult.ProcessedItems.Any())
            {
                _logger.LogWarning("'{ProjectName}' projesi için Google Sheets'ten hiçbir metin işlenemedi. Hata: {Error}", project.Name, processingResult.ErrorMessage);
                return new Dictionary<string, List<string>>();
            }

            _logger.LogInformation("Google Sheets'ten {Count} adet metin başarıyla çekildi ve işlendi.", processingResult.ProcessedItems.Count);

            var groupedResults = GroupPlaceholders(processingResult.ProcessedItems, customPatterns);
            _logger.LogInformation("{Count} farklı türde placeholder/tag grubu tespit edildi.", groupedResults.Count);

            return groupedResults;
        }

        /// <inheritdoc />
        public async Task ProcessPlaceholdersAsync(int projectId, Dictionary<string, List<string>> approvedGroups)
        {
            _logger.LogInformation("'{ProjectId}' projesi için placeholder işleme süreci başlatıldı.", projectId);

            var generatedRegexesDict = new Dictionary<string, string>();
            foreach (var group in approvedGroups)
            {
                var regex = await GenerateRegexWithAIAsync(group.Key, group.Value);
                generatedRegexesDict[group.Key] = regex;
                _logger.LogInformation("'{GroupKey}' grubu için Regex üretildi: {Regex}", group.Key, regex);
            }

            await SavePlaceholdersAsync(projectId, generatedRegexesDict, approvedGroups);
            _logger.LogInformation("'{ProjectId}' projesi için placeholder'lar başarıyla işlendi ve kaydedildi.", projectId);
        }

        /// <summary>
        /// Üretilen Regex'leri ve örnekleri veritabanına kaydeder.
        /// </summary>
        /// <param name="projectId">Proje ID'si.</param>
        /// <param name="generatedRegexes">Üretilen regex'lerin sözlüğü.</param>
        /// <param name="detectedGroups">Tespit edilen grupların ve örneklerin sözlüğü.</param>
        private async Task SavePlaceholdersAsync(int projectId, Dictionary<string, string> generatedRegexes, Dictionary<string, List<string>> detectedGroups)
        {
            var placeholdersToSave = new List<Placeholder>();

            foreach (var item in generatedRegexes)
            {
                if (detectedGroups.TryGetValue(item.Key, out var examples))
                {
                    var placeholder = new Placeholder
                    {
                        Type = item.Key,
                        Regex = item.Value,
                        Examples = examples
                    };
                    placeholdersToSave.Add(placeholder);
                }
            }

            if (placeholdersToSave.Any())
            {
                await using var dbContext = _databaseService.GetProjectContext(projectId);
                await dbContext.Placeholders.AddRangeAsync(placeholdersToSave);
                await dbContext.SaveChangesAsync();
                _logger.LogInformation("{Count} adet placeholder veritabanına başarıyla kaydedildi.", placeholdersToSave.Count);
            }
        }

        /// <summary>
        /// Verilen metinlerdeki placeholder'ları ve HTML/XML tag'lerini regex kullanarak tespit eder ve gruplar.
        /// </summary>
        /// <param name="texts">İçinde arama yapılacak metinlerin listesi.</param>
        /// <param name="customPatterns">Başlangıç ve bitiş ayıraçlarını içeren özel kalıplar.</param>
        /// <returns>Tespit edilen placeholder ve tag'lerin gruplanmış bir sözlüğü.</returns>
        private Dictionary<string, List<string>> GroupPlaceholders(List<TextCreateDto> texts, List<CustomPatternDto>? customPatterns)
        {
            var groupedPlaceholders = new Dictionary<string, List<string>>();

            // Desenler, örtüşen eşleşmeleri önlemek için özgüllüğe göre sıralanır.
            // Daha spesifik desenler önce kontrol edilir.
            var orderedPatterns = new List<KeyValuePair<string, string>>
            {
                new KeyValuePair<string, string>("<tag>...</tag>", @"<(\w+)[^>]*>.*?<\/\1>"), // Kapanışlı tag'ler (non-greedy içerik)
                new KeyValuePair<string, string>("<tag/>", @"<(\w+)[^>]*\/>"), // Kendi kendine kapanan tag'ler
                new KeyValuePair<string, string>("<>", @"<[^>]+>"), // Basit, potansiyel olarak kapanışı olmayan tag'ler
                new KeyValuePair<string, string>("[]", @"\[([^\]]+)\]"),
                new KeyValuePair<string, string>("()", @"\(([^)]+)\)"),
                new KeyValuePair<string, string>("{}", @"\{([^{}]+)\}")
            };

            if (customPatterns != null)
            {
                foreach (var pattern in customPatterns)
                {
                    if (!string.IsNullOrEmpty(pattern.StartDelimiter) && !string.IsNullOrEmpty(pattern.EndDelimiter))
                    {
                        var open = Regex.Escape(pattern.StartDelimiter);
                        var close = Regex.Escape(pattern.EndDelimiter);
                        orderedPatterns.Add(new KeyValuePair<string, string>(
                            $"{pattern.StartDelimiter}...{pattern.EndDelimiter}",
                            $@"{open}(.*?){close}"
                        ));
                    }
                }
            }

            foreach (var text in texts.Select(t => t.En).Where(t => !string.IsNullOrEmpty(t)))
            {
                var remainingText = text;

                foreach (var pattern in orderedPatterns)
                {
                    var matches = Regex.Matches(remainingText, pattern.Value);
                    if (matches.Count > 0)
                    {
                        if (!groupedPlaceholders.ContainsKey(pattern.Key))
                        {
                            groupedPlaceholders[pattern.Key] = new List<string>();
                        }

                        foreach (Match match in matches)
                        {
                            var value = match.Value; // Örnek olarak tam eşleşmeyi kullan
                            if (!groupedPlaceholders[pattern.Key].Contains(value))
                            {
                                groupedPlaceholders[pattern.Key].Add(value);
                            }
                        }
                
                        // Eşleşen kısımları metinden çıkararak daha az spesifik desenler tarafından tekrar eşleştirilmesini önle.
                        remainingText = Regex.Replace(remainingText, pattern.Value, string.Empty);
                    }
                }
            }

            return groupedPlaceholders;
        }

        /// <summary>
        /// Verilen örnek placeholder'lardan C# için genel bir Regex deseni üretmek üzere AI modelini kullanır.
        /// </summary>
        /// <param name="groupType">Placeholder grubunun türü (örn: "[]", "{}").</param>
        /// <param name="examples">Bu gruba ait örnek placeholder'ların listesi.</param>
        /// <returns>AI tarafından üretilen temizlenmiş Regex deseni.</returns>
        private async Task<string> GenerateRegexWithAIAsync(string groupType, List<string> examples)
        {
            var prompt = $@"
                Regex grup türü: '{groupType}', aşağıdaki örnekleri yakalayabilen tek bir genel Regex kalıbı oluştur: 
                - {string.Join("\n- ", examples.Select(e => $"'{e}'"))}

                Regex, benzer kalıpları yakalayacak kadar geniş, ancak yanlış pozitifleri önleyecek kadar da spesifik olmalıdır.
                Herhangi bir açıklama, yorum veya kod bloğu eklemeyin. Yalnızca ham regex dizesini sağlayın.
            ";

            var request = new GeminiContentRequest { Prompt = prompt.Trim(), Model = "gemini-2.5-flash", ThinkingBudget = 0 };
            var aiResponse = await _geminiService.GenerateContentAsync(request, CancellationToken.None);

            if (!aiResponse.Success || string.IsNullOrEmpty(aiResponse.Text))
            {
                _logger.LogWarning("AI'dan Regex üretilemedi. Hata: {ErrorMessage}", aiResponse.ErrorMessage);
                return string.Empty; // veya bir varsayılan desen
            }

            // AI'dan gelen yanıtı temizle (kod blokları, tırnak işaretleri vb.)
            var regexPattern = aiResponse.Text.Trim('`', '\n', '\r', ' ');
            if (regexPattern.StartsWith("```"))
            {
                regexPattern = regexPattern.Substring(3).Trim();
            }
            if (regexPattern.StartsWith("csharp"))
            {
                regexPattern = regexPattern.Substring(6).Trim();
            }
            if (regexPattern.StartsWith("regex"))
            {
                regexPattern = regexPattern.Substring(5).Trim();
            }
            if (regexPattern.EndsWith("```"))
            {
                regexPattern = regexPattern.Substring(0, regexPattern.Length - 3).Trim();
            }


            return regexPattern;
        }
    }
}