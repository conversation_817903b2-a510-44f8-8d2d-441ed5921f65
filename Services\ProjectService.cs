using System.Text.Json;
using Microsoft.EntityFrameworkCore;
using TranslationAgentServer.Interfaces;
using TranslationAgentServer.Models;
using TranslationAgentServer.Models.Filtering;

namespace TranslationAgentServer.Services;

/// <summary>
/// Proje yönetimi servisinin implementasyonu
/// Supabase ile proje CRUD operasyonlarını gerçekleştirir
/// </summary>
public class ProjectService : IProjectService
{
    private readonly IDatabaseService _databaseService;
    private readonly ILogger<ProjectService> _logger;

    public ProjectService(IDatabaseService databaseService, ILogger<ProjectService> logger)
    {
        _databaseService = databaseService;
        _logger = logger;
    }

    /// <summary>
    /// Tüm projeleri listeler
    /// </summary>
    /// <returns>Proje listesi</returns>
    public async Task<IEnumerable<Project>> GetAllProjectsAsync()
    {
        try
        {
            await using var client = _databaseService.GetContext();
            var response = await client.Projects.ToListAsync();
            return response ?? new List<Project>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Projeler getirilirken hata oluştu");
            throw;
        }
    }

    public async Task<IEnumerable<Project>> GetAllProjectsAsync(ProjectFilterQuery filterQuery)
    {
        try
        {
            await using var client = _databaseService.GetContext();
            var query = client.Projects.AsQueryable();

            var filteredQuery = FilteringService.ApplyFilters(query, filterQuery);

            return await filteredQuery.ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Gelişmiş filtreleme ile projeler getirilirken hata oluştu");
            throw;
        }
    }

    public async Task<IEnumerable<Project>> GetAllProjectsAsync(JsonElement filterElement)
    {
        try
        {
            var filterQuery = ProjectFilterQuery.FromJsonElement(filterElement);
            return await GetAllProjectsAsync(filterQuery);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "JsonElement filtreleme ile projeler getirilirken hata oluştu");
            throw;
        }
    }

    /// <summary>
    /// Belirtilen ID'ye sahip projeyi getirir
    /// </summary>
    /// <param name="id">Proje kimliği</param>
    /// <returns>Proje bilgileri veya null</returns>
    public async Task<Project?> GetProjectByIdAsync(int id)
    {
        try
        {
            _logger.LogInformation("Proje getiriliyor: {ProjectId}", id);
            await using var client = _databaseService.GetContext();
            var response = await client.Projects
                .Where(x => x.Id == id)
                .SingleOrDefaultAsync();

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Proje getirilirken hata oluştu: {ProjectId}", id);
            return null;
        }
    }

    /// <summary>
    /// Yeni proje oluşturur
    /// </summary>
    /// <param name="projectDto">Proje bilgileri</param>
    /// <returns>Oluşturulan proje</returns>
    public async Task<Project> CreateProjectAsync(ProjectDto projectDto)
    {
        try
        {
            _logger.LogInformation("Yeni proje oluşturuluyor: {ProjectName}", projectDto.Name);
            await using var client = _databaseService.GetContext();
            var project = new Project
            {
                Name = projectDto.Name,
                SpreadsheetId = projectDto.SpreadsheetId,
                TextsTable = projectDto.TextsTable,
                TermsTable = projectDto.TermsTable,
                MainContext = projectDto.MainContext,
                CreatedAt = DateTime.UtcNow
            };

            if (projectDto.Settings != null)
            {
                project.Settings = projectDto.Settings;
            }
            else
            {
                var defaultSettings = await client.MainData.SingleOrDefaultAsync(x => x.Name == "default_project_settings");
                project.Settings = defaultSettings?.Value != null
                    ? JsonDocument.Parse(defaultSettings.Value)
                    : JsonDocument.Parse("{}");
            }


            await client.Projects.AddAsync(project);
            await client.SaveChangesAsync(); // ID burada atanır

            // Şimdi ID'ye göre schema oluştur
            var schemaName = $"project_{project.Id}";
            var schemaCreationResult = await _databaseService.AddSchemaAsync(schemaName);

            if (!schemaCreationResult)
            {
                // Schema oluşturulamazsa projeyi geri al
                client.Projects.Remove(project);
                await client.SaveChangesAsync();
                throw new InvalidOperationException("Proje şeması oluşturulurken hata oluştu");
            }

            // Schema adını güncelle
            project.SchemaName = schemaName;
            await client.SaveChangesAsync();

            _logger.LogInformation("Proje başarıyla oluşturuldu: {ProjectId}", project.Id);
            return project;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Proje oluşturulurken hata oluştu: {ProjectName}", projectDto.Name);
            throw;
        }
    }

    /// <summary>
    /// Mevcut projeyi günceller
    /// </summary>
    /// <param name="id">Proje kimliği</param>
    /// <param name="projectUpdateDto">Güncellenecek proje bilgileri</param>
    /// <returns>Güncellenmiş proje veya null</returns>
    public async Task<Project?> UpdateProjectAsync(int id, ProjectUpdateDto projectUpdateDto)
    {
        try
        {
            _logger.LogInformation("Proje güncelleniyor: {ProjectId}", id);

            // Önce projenin var olup olmadığını kontrol et
            var existingProject = await GetProjectByIdAsync(id);
            if (existingProject == null)
            {
                _logger.LogWarning("Güncellenecek proje bulunamadı: {ProjectId}", id);
                return null;
            }

            existingProject.Name = projectUpdateDto.Name ?? existingProject.Name;
            existingProject.SpreadsheetId = projectUpdateDto.SpreadsheetId ?? existingProject.SpreadsheetId;
            existingProject.TextsTable = projectUpdateDto.TextsTable ?? existingProject.TextsTable;
            existingProject.TermsTable = projectUpdateDto.TermsTable ?? existingProject.TermsTable;
            existingProject.MainContext = projectUpdateDto.MainContext ?? existingProject.MainContext;
            existingProject.Settings = projectUpdateDto.Settings ?? existingProject.Settings;
            existingProject.CreatedAt = existingProject.CreatedAt;

            await using var client = _databaseService.GetContext();
            existingProject = client.Projects.Update(existingProject).Entity;
            var response = await client.SaveChangesAsync();

            var updatedProject = response > 0 ? existingProject : null;
            if (updatedProject != null)
            {
                _logger.LogInformation("Proje başarıyla güncellendi: {ProjectId}", id);
            }

            return updatedProject;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Proje güncellenirken hata oluştu: {ProjectId}", id);
            throw;
        }
    }

    /// <summary>
    /// Projeyi siler
    /// </summary>
    /// <param name="id">Proje kimliği</param>
    /// <returns>Silme işleminin başarılı olup olmadığı</returns>
    public async Task<bool> DeleteProjectAsync(int id)
    {
        try
        {
            _logger.LogInformation("Proje siliniyor: {ProjectId}", id);

            // Önce projenin var olup olmadığını kontrol et
            var existingProject = await GetProjectByIdAsync(id);
            if (existingProject == null)
            {
                _logger.LogWarning("Silinecek proje bulunamadı: {ProjectId}", id);
                return false;
            }

            await using var client = _databaseService.GetContext();
            client.Projects.Remove(existingProject);
            var response = await client.SaveChangesAsync();
            _logger.LogInformation("Proje başarıyla silindi: {ProjectId}", id);
            return response > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Proje silinirken hata oluştu: {ProjectId}", id);
            throw;
        }
    }

    /// <summary>
    /// Proje adına göre arama yapar
    /// </summary>
    /// <param name="name">Aranacak proje adı</param>
    /// <returns>Eşleşen projeler</returns>
    public async Task<IEnumerable<Project>> SearchProjectsByNameAsync(string name)
    {
        try
        {
            _logger.LogInformation("Proje aranıyor: {SearchTerm}", name);

            if (string.IsNullOrWhiteSpace(name))
            {
                return await GetAllProjectsAsync();
            }

            await using var client = _databaseService.GetContext();
            var response = await client.Projects
                .Where(x => x.Name!.Contains(name)).OrderByDescending(x => x.CreatedAt)
                .ToListAsync();

            return response ?? new List<Project>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Proje aranırken hata oluştu: {SearchTerm}", name);
            throw;
        }
    }

    /// <summary>
    /// Belirtilen Google Sheets ID'sine sahip projeyi bulur
    /// </summary>
    /// <param name="SpreadsheetId">Google Sheets ID</param>
    /// <returns>Proje bilgileri veya null</returns>
    public async Task<Project?> GetProjectBySpreadsheetIdAsync(string spreadsheetId)
    {
        try
        {
            _logger.LogInformation("Google Sheets ID ile proje aranıyor: {SpreadsheetId}", spreadsheetId);

            await using var client = _databaseService.GetContext();
            var response = await client.Projects
                .Where(x => x.SpreadsheetId == spreadsheetId).SingleOrDefaultAsync();

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Google Sheets ID ile proje aranırken hata oluştu: {SpreadsheetId}", spreadsheetId);
            return null;
        }
    }


}