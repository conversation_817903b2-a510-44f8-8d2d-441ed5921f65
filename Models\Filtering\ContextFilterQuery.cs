using System.Text.Json;

namespace TranslationAgentServer.Models.Filtering;

/// <summary>
/// Context modeli için ö<PERSON>ştirilmiş filtreleme sorgusu
/// </summary>
public class ContextFilterQuery : FilterQuery
{
    /// <summary>
    /// Kategori filtresi
    /// </summary>
    public string? Category { get; set; }
    
    /// <summary>
    /// Başlık filtresi
    /// </summary>
    public string? Title { get; set; }
    
    /// <summary>
    /// İçerik filtresi
    /// </summary>
    public string? Content { get; set; }
    
    /// <summary>
    /// Birleştirilmiş metin filtresi
    /// </summary>
    public string? CombinedText { get; set; }
    
    /// <summary>
    /// CreatedAt başlangıç tarihi
    /// </summary>
    public DateTime? CreatedAtFrom { get; set; }
    
    /// <summary>
    /// CreatedAt bitiş tarihi
    /// </summary>
    public DateTime? CreatedAtTo { get; set; }
    
    /// <summary>
    /// UpdatedAt başlangıç tarihi
    /// </summary>
    public DateTime? UpdatedAtFrom { get; set; }
    
    /// <summary>
    /// UpdatedAt bitiş tarihi
    /// </summary>
    public DateTime? UpdatedAtTo { get; set; }
    
    /// <summary>
    /// JSON string'den ContextFilterQuery oluşturur
    /// </summary>
    public static new ContextFilterQuery FromJson(string json)
    {
        if (string.IsNullOrEmpty(json))
            return new ContextFilterQuery();
            
        try
        {
            return JsonSerializer.Deserialize<ContextFilterQuery>(json, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            }) ?? new ContextFilterQuery();
        }
        catch
        {
            return new ContextFilterQuery();
        }
    }
    
    /// <summary>
    /// JsonElement'den ContextFilterQuery oluşturur
    /// </summary>
    public static new ContextFilterQuery FromJsonElement(JsonElement element)
    {
        try
        {
            return JsonSerializer.Deserialize<ContextFilterQuery>(element.GetRawText(), new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            }) ?? new ContextFilterQuery();
        }
        catch
        {
            return new ContextFilterQuery();
        }
    }
}