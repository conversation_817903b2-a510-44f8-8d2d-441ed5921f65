using TranslationAgentServer.Models;
using TranslationAgentServer.Models.Filtering;
using System.Text.Json;
using TranslationAgentServer.Data;

namespace TranslationAgentServer.Interfaces
{
    /// <summary>
    /// Terim yönetimi için servis arayüzü.
    /// CRUD işlemleri, arama ve özel terim işlevlerini tanımlar.
    /// </summary>
    public interface ITermService
    {
        /// <summary>
        /// Tüm terimleri getirir
        /// </summary>
        /// <param name="ProjectId">Proje kimliği</param>
        /// <returns>Terim listesi</returns>
        Task<List<Term>> GetAllTermsAsync(int ProjectId);

        /// <summary>
        /// Gelişmiş filtreleme ile terimleri getirir
        /// </summary>
        /// <param name="ProjectId">Proje kimliği</param>
        /// <param name="filterQuery">Filtreleme sorgusu</param>
        /// <returns>Filtrelenmiş terim listesi</returns>
        Task<List<Term>> GetAllTermsAsync(int ProjectId, TermFilterQuery filterQuery);


        /// <summary>
        /// JsonElement ile filtreleme (backend kullanımı için)
        /// </summary>
        /// <param name="ProjectId">Proje kimliği</param>
        /// <param name="filterElement">JsonElement formatında filtreleme sorgusu</param>
        /// <returns>Filtrelenmiş terim listesi</returns>
        Task<List<Term>> GetAllTermsAsync(int ProjectId, JsonElement filterElement);

        /// <summary>
        /// ID'ye göre terim getirir
        /// </summary>
        /// <param name="id">Terim ID'si</param>
        /// <param name="ProjectId">Proje kimliği</param>
        /// <returns>Bulunan terim veya null</returns>
        Task<Term?> GetTermByIdAsync(int id, int ProjectId);

        /// <summary>
        /// ID'ye göre terim ve çeviri geçmişini getirir
        /// </summary>
        /// <param name="id">Terim ID'si</param>
        /// <param name="ProjectId">Proje kimliği</param>
        /// <returns>Terim ve çeviri geçmişi</returns>
        Task<TermTranslation?> GetTermTranslationsAsync(int id, int ProjectId);


        /// <summary>
        /// Terim istatistiklerini getirir
        /// </summary>
        /// <param name="ProjectId">Proje kimliği</param>
        /// <returns>İstatistik bilgileri</returns>
        Task<TermStatistics> GetTermStatisticsAsync(int ProjectId);

        /// <summary>
        /// Terim oluşturur
        /// </summary>
        /// <param name="termCreateDto">Oluşturulacak terim bilgileri</param>
        /// <param name="ProjectId">Proje kimliği</param>
        /// <returns>Oluşturulan terim</returns>
        Task<Term?> CreateTermAsync(TermCreateDto termCreateDto, int ProjectId);

        /// <summary>
        /// Yeni terim oluşturur
        /// </summary>
        /// <param name="termCreateDto">Oluşturulacak terim bilgileri</param>
        /// <param name="ProjectId">Proje kimliği</param>
        /// <returns>Oluşturulan terim</returns>
        Task<Term?> AddTermAsync(Term termCreateDto, int ProjectId);

        /// <summary>
        /// Çoklu terim oluşturur
        /// </summary>
        /// <param name="termCreateDtos">Oluşturulacak terim listesi</param>
        /// <param name="ProjectId">Proje kimliği</param>
        /// <returns>Oluşturulan terimler</returns>
        Task<List<Term>> CreateTermsAsync(List<TermCreateDto> termCreateDtos, int ProjectId, CancellationToken cancellationToken);

        /// <summary>
        /// Çoklu terim ekler (embedding üretmeden)
        /// </summary>
        /// <param name="termCreateDtos">Eklenen terim listesi</param>
        /// <param name="ProjectId">Proje kimliği</param>
        /// <returns>Eklenen terimler</returns>
        Task<int> AddTermsAsync(List<Term> termCreateDtos, int ProjectId, CancellationToken cancellationToken, DynamicSchemaDbContext useClient = null);

        /// <summary>
        /// Terimi günceller
        /// </summary>
        /// <param name="id">Güncellenecek terim ID'si</param>
        /// <param name="termUpdateDto">Güncelleme bilgileri</param>
        /// <param name="ProjectId">Proje kimliği</param>
        /// <returns>Güncellenmiş terim veya null</returns>
        Task<Term?> UpdateTermAsync(int id, TermUpdateDto termUpdateDto, int ProjectId);

        /// <summary>
        /// Çoklu terim günceller
        /// </summary>
        /// <param name="termUpdates">Güncellenecek terim listesi (ID ve DTO çiftleri)</param>
        /// <param name="ProjectId">Proje kimliği</param>
        /// <returns>Güncellenmiş terim sayısı</returns>
        Task<int> UpdateTermsAsync(List<(int Id, TermUpdateDto UpdateDto)> termUpdates, int ProjectId, CancellationToken cancellationToken);

        /// <summary>
        /// Terimi siler
        /// </summary>
        /// <param name="id">Silinecek terim ID'si</param>
        /// <param name="ProjectId">Proje kimliği</param>
        /// <returns>Silme işlemi başarılı mı?</returns>
        Task<bool> DeleteTermAsync(int id, int ProjectId);

        /// <summary>
        /// Çoklu terim siler
        /// </summary>
        /// <param name="ids">Silinecek terim ID'leri</param>
        /// <param name="ProjectId">Proje kimliği</param>
        /// <returns>Silinen terim sayısı</returns>
        Task<int> DeleteTermsAsync(List<long> ids, int ProjectId);
    }

    /// <summary>
    /// Terim istatistikleri için model
    /// </summary>
    public class TermStatistics
    {
        /// <summary>
        /// Toplam terim sayısı
        /// </summary>
        public int TotalTerms { get; set; }

        /// <summary>
        /// Çevrilmiş terim sayısı
        /// </summary>
        public int TranslatedTerms { get; set; }

        /// <summary>
        /// Çevrilmemiş terim sayısı
        /// </summary>
        public int UntranslatedTerms { get; set; }

        /// <summary>
        /// İngilizce terim sayısı
        /// </summary>
        public int EnglishTerms { get; set; }

        /// <summary>
        /// Türkçe terim sayısı
        /// </summary>
        public int TurkishTerms { get; set; }

        /// <summary>
        /// Embedding'i olan terim sayısı
        /// </summary>
        public int TermsWithEmbedding { get; set; }

        /// <summary>
        /// Embedding'i olmayan terim sayısı
        /// </summary>
        public int TermsWithoutEmbedding { get; set; }

        /// <summary>
        /// Çeviri tamamlanma yüzdesi
        /// </summary>
        public double TranslationCompletionPercentage => TotalTerms > 0 ? (double)TranslatedTerms / TotalTerms * 100 : 0;

        /// <summary>
        /// Embedding tamamlanma yüzdesi
        /// </summary>
        public double EmbeddingCompletionPercentage => TotalTerms > 0 ? (double)TermsWithEmbedding / TotalTerms * 100 : 0;
    }
}