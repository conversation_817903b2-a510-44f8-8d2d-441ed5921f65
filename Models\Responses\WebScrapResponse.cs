// <summary>
// Web scraping işlemi sonucunda elde edilen veriyi temsil eder. Sadece WebScraping işlemi için kullanılır.
// </summary>
namespace TranslationAgentServer.Models.Responses
{
    public class WebScrapResponse
    {
        /// <summary>
        /// Kaynağın URL adresi.
        /// </summary>
        public string? Url { get; set; }

        /// <summary>
        /// Sayfa başlığı (varsa).
        /// </summary>
        public string? Title { get; set; }

        /// <summary>
        /// Çekilen içerik.
        /// </summary>
        public string? Content { get; set; }
    }
}