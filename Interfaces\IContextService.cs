using TranslationAgentServer.Models;
using TranslationAgentServer.Models.Filtering;
using System.Text.Json;

namespace TranslationAgentServer.Interfaces;

/// <summary>
/// Context servisi arayüzü
/// Context verilerinin CRUD işlemlerini ve özel sorguları tanımlar
/// </summary>
public interface IContextService
{
    /// <summary>
    /// Tüm context'leri getirir
    /// </summary>
    /// <param name="projectId"><PERSON>je kimliği</param>

    /// <returns>Context listesi</returns>
    Task<List<Context>> GetAllContextsAsync(int projectId);

    /// <summary>
    /// Gelişmiş filtreleme ile context'leri getirir
    /// </summary>
    /// <param name="projectId">Proje kimliği</param>
    /// <param name="filterQuery">Filtreleme sorgusu</param>
    /// <returns>Filtrelenmiş context listesi</returns>
    Task<List<Context>> GetAllContextsAsync(int projectId, ContextFilterQuery filterQuery);

    /// <summary>
    /// JsonElement ile filtreleme (backend kullanımı için)
    /// </summary>
    /// <param name="projectId">Proje kimliği</param>
    /// <param name="filterElement">JsonElement formatında filtreleme sorgusu</param>
    /// <returns>Filtrelenmiş context listesi</returns>
    Task<List<Context>> GetAllContextsAsync(int projectId, JsonElement filterElement);

    /// <summary>
    /// ID'ye göre context getirir
    /// </summary>
    /// <param name="id">Context ID'si</param>
    /// <param name="projectId">Proje kimliği</param>
    /// <returns>Context</returns>
    Task<Context?> GetContextByIdAsync(int id, int projectId);
    /// <summary>
    /// Embedding vektörüne göre benzer context'leri bulur
    /// </summary>
    /// <param name="embedding">Embedding vektörü</param>
    /// <param name="limit">Sonuç limiti</param>
    /// <param name="projectId">Proje kimliği</param>
    /// <returns>Benzer context listesi</returns>
    Task<List<Context>> FindSimilarContextsAsync(float[] embedding, int limit, int projectId);

    /// <summary>
    /// Yeni context oluşturur
    /// </summary>
    /// <param name="contextCreateDto">Context oluşturma DTO'su</param>
    /// <param name="projectId">Proje kimliği</param>
    /// <returns>Oluşturulan context</returns>
    Task<Context> CreateContextAsync(ContextCreateDto contextCreateDto, int projectId);

    /// <summary>
    /// Context günceller
    /// </summary>
    /// <param name="id">Context ID'si</param>
    /// <param name="contextUpdateDto">Context güncelleme DTO'su</param>
    /// <param name="projectId">Proje kimliği</param>
    /// <returns>Güncellenmiş context</returns>
    Task<ContextUpdateDto?> UpdateContextAsync(int id, ContextUpdateDto contextUpdateDto, int projectId);

    /// <summary>
    /// Context siler
    /// </summary>
    /// <param name="id">Context ID'si</param>
    /// <param name="projectId">Proje kimliği</param>
    /// <returns>Silme işlemi başarılı mı</returns>
    Task<bool> DeleteContextAsync(int id, int projectId);

    /// <summary>
    /// Context'leri toplu olarak oluşturur
    /// </summary>
    /// <param name="contextCreateDtos">Context oluşturma DTO listesi</param>
    /// <param name="projectId">Proje kimliği</param>
    /// <returns>Oluşturulan context listesi</returns>
    Task<List<Context>> CreateContextsAsync(List<ContextCreateDto> contextCreateDtos, int projectId, CancellationToken cancellationToken);

    /// <summary>
    /// Context'leri toplu olarak günceller
    /// </summary>
    /// <param name="contextUpdates">Context güncelleme listesi (ID ve DTO çiftleri)</param>
    /// <param name="projectId">Proje kimliği</param>
    /// <returns>Güncellenmiş context listesi</returns>
    Task<List<Context>> UpdateContextsAsync(List<(int Id, ContextUpdateDto UpdateDto)> contextUpdates, int projectId, CancellationToken cancellationToken);

    /// <summary>
    /// Context'leri toplu olarak siler
    /// </summary>
    /// <param name="ids">Silinecek context ID'leri</param>
    /// <param name="projectId">Proje kimliği</param>
    /// <returns>Silme işlemi başarılı mı</returns>
    Task<bool> DeleteContextsAsync(List<long> ids, int projectId);

    /// <summary>
    /// Context istatistiklerini getirir
    /// </summary>
    /// <param name="projectId">Proje kimliği</param>
    /// <returns>Context istatistikleri</returns>
    Task<ContextStatistics> GetContextStatisticsAsync(int projectId);
}