namespace TranslationAgentServer.Models.Filtering;

/// <summary>
/// Filtreleme operatörleri
/// </summary>
public enum FilterOperator
{
    // String operatörleri
    Equals,
    NotEquals,
    Contains,
    NotContains,
    StartsWith,
    EndsWith,
    Reg<PERSON>,
    
    // Numeric operatörleri
    <PERSON>han,
    GreaterThan<PERSON>r<PERSON>,
    LessThan,
    LessThanOrEqual,
    
    // DateTime operatörleri
    Before,
    After,
    Between,
    
    // Null operatörleri
    IsNull,
    IsNotNull,
    
    // Array/List operatörleri
    In,
    NotIn
}