// <summary>
// AI'dan gelen terim onaylama yanıtını temsil eder.
// </summary>
namespace TranslationAgentServer.Models.Responses
{
    /// <summary>
    /// AI tarafından dönen "confirm_term" ve opsiyonel "term" alanlarını karşılayan model.
    /// </summary>
    public class ConfirmTermResponse
    {
        /// <summary>
        /// AI'nın terimi onaylayıp onaylamadığını belirten alan.
        /// </summary>
        public bool ConfirmTerm { get; set; }

        /// <summary>
        /// Onaylanan veya düzeltilen terim (varsa).
        /// </summary>
        public string? Term { get; set; }
    }
}