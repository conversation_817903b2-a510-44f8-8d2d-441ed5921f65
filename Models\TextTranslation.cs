using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TranslationAgentServer.Models;

/// <summary>
/// Metin çevirilerinin AI model sonuçlarını saklayan model sınıfı
/// Read-only erişim için tasarlanmıştır
/// </summary>
public class TextTranslation
{
    /// <summary>
    /// Benzersiz kimlik
    /// </summary>
    [Key]
    public long Id { get; set; }

    /// <summary>
    /// texts tablosundaki id ile ilişkili
    /// </summary>
    public int TextId { get; set; }

    [ForeignKey("TextId")]
    public Text? Text { get; set; }

    /// <summary>
    /// Kullanılan AI model adı
    /// </summary>
    public string? AiModel { get; set; }

    /// <summary>
    /// AI modeline gönderilen prompt
    /// </summary>
    public string? Prompt { get; set; }

    /// <summary>
    /// Çeviri muhakemesi
    /// </summary>
    public string? Reasoning { get; set; }

    /// <summary>
    /// AI modelinden dönen sonuç
    /// </summary>
    public string? Result { get; set; }


    /// <summary>
    /// Kullanılan token sayısı
    /// </summary>
    [Required]
    public int TokenCount { get; set; } = 0;

    /// <summary>
    /// İşlenme tarihi
    /// </summary>
    [Required]
    public DateTime ProcessedAt { get; set; } = DateTime.UtcNow;

    public override bool Equals(object? obj)
    {
        return obj is TextTranslation other && Id == other.Id;
    }

    public override int GetHashCode()
    {
        return Id.GetHashCode();
    }
}