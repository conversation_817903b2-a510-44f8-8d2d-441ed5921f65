// <summary>
// AI tarafından döndürülen context oluşturma yanıtını temsil eder. Sadece ContextCreation işlemi için kullan<PERSON>lır.
// </summary>
namespace TranslationAgentServer.Models.Responses
{
    public class ContextCreationResponse
    {
        /// <summary>
        /// Oluşturulan context listesidir.
        /// </summary>
        public List<ContextItem> Contexts { get; set; } = new();

        // <summary>
        // Tek bir context nesnesini temsil eder.
        // </summary>
        public class ContextItem
        {
            /// <summary>
            /// Context içeriği.
            /// </summary>
            public string? Content { get; set; }

            /// <summary>
            /// Context kategorisi (varsa).
            /// </summary>
            public string? Category { get; set; }

            /// <summary>
            /// Context başlığı (varsa).
            /// </summary>
            public string? Title { get; set; }
        }
    }
}