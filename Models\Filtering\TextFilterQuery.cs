using System.Text.Json;

namespace TranslationAgentServer.Models.Filtering;

/// <summary>
/// Text modeli için özel<PERSON>ştirilmiş filtreleme sorgusu
/// </summary>
public class TextFilterQuery : FilterQuery
{
    /// <summary>
    /// Namespace filtresi
    /// </summary>
    public string? Namespace { get; set; }
    
    /// <summary>
    /// Key filtresi
    /// </summary>
    public string? Key { get; set; }
    
    /// <summary>
    /// İngilizce metin filtresi
    /// </summary>
    public string? En { get; set; }
    
    /// <summary>
    /// Türkçe metin filtresi
    /// </summary>
    public string? Tr { get; set; }
    
    /// <summary>
    /// Status filtresi
    /// </summary>
    public TextStatus? Status { get; set; }
    
    /// <summary>
    /// RowID filtresi
    /// </summary>
    public int? RowId { get; set; }
    
    /// <summary>
    /// CreatedAt başlangıç tarihi
    /// </summary>
    public DateTime? CreatedAtFrom { get; set; }
    
    /// <summary>
    /// CreatedAt bitiş tarihi
    /// </summary>
    public DateTime? CreatedAtTo { get; set; }
    
    /// <summary>
    /// UpdatedAt başlangıç tarihi
    /// </summary>
    public DateTime? UpdatedAtFrom { get; set; }
    
    /// <summary>
    /// UpdatedAt bitiş tarihi
    /// </summary>
    public DateTime? UpdatedAtTo { get; set; }
    
    /// <summary>
    /// JSON string'den TextFilterQuery oluşturur
    /// </summary>
    public static new TextFilterQuery FromJson(string json)
    {
        if (string.IsNullOrEmpty(json))
            return new TextFilterQuery();
            
        try
        {
            return JsonSerializer.Deserialize<TextFilterQuery>(json, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            }) ?? new TextFilterQuery();
        }
        catch
        {
            return new TextFilterQuery();
        }
    }
    
    /// <summary>
    /// JsonElement'den TextFilterQuery oluşturur
    /// </summary>
    public static new TextFilterQuery FromJsonElement(JsonElement element)
    {
        try
        {
            return JsonSerializer.Deserialize<TextFilterQuery>(element.GetRawText(), new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            }) ?? new TextFilterQuery();
        }
        catch
        {
            return new TextFilterQuery();
        }
    }
}