# Teknoloji Yığını: TranslationAgentServer

<PERSON><PERSON>, `TranslationAgentServer` projesinde kullanılan temel teknolojileri, kütüphaneleri ve araçları detaylandırmaktadır.

## Backend

*   **Ana Çatı (Framework):** .NET 9.0
*   **Programlama Dili:** C#
*   **API:** ASP.NET Core Web API

## Veritabanı

*   **Veritabanı Sistemi:** PostgreSQL
*   **ORM:** Entity Framework Core
*   **Sürücü:** Npgsql
*   **Eklentiler:** Pgvector (Vektör veritabanı ve anlamsal arama yetenekleri için)
*   **Çok Kiracılı Mimari:** Her proje için ayrı bir şema (schema) ile veri izolasyonu

## Yapay Zeka & NLP

*   **Google Gemini Entegrasyonu:** `Mscc.GenerativeAI` kütüphanesi kullanılarak sağlanır.
*   **OpenAI Entegrasyonu:** `OpenAI-DotNet` kütüphanesi ile gerçekleştirilir.
*   **Doğal Dil İşleme (NLP):** Metin analizi ve işleme için `Catalyst` kütüphanesi kullanılır.

## Veri Entegrasyonları & Otomasyon

*   **Google Sheets:** `Google.Apis.Sheets.v4` kütüphanesi aracılığıyla Google E-Tablolar ile veri senkronizasyonu yapılır.
*   **Web Scraping:** Dinamik web sitelerinden içerik çekmek için `Playwright` kullanılır.

## API Dokümantasyonu

*   **OpenAPI/Swagger:** API endpoint'lerinin dokümantasyonu ve test edilebilir bir arayüz sağlamak için `Swashbuckle.AspNetCore` paketi kullanılır.

## Diğer Kütüphaneler

*   **Newtonsoft.Json:** JSON işlemleri için.
*   **System.Text.Json:** Alternatif JSON işlemleri için.
*   **Microsoft.AspNetCore.OpenApi:** OpenAPI desteği için.

## Geliştirme ve Dağıtım

*   **Render.com:** Bulut tabanlı dağıtım ve izleme.
*   **VSCode:** Geliştirme ortamı.