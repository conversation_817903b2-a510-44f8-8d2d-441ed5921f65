using Microsoft.AspNetCore.Mvc;
using TranslationAgentServer.Interfaces;
using TranslationAgentServer.Models;
using TranslationAgentServer.Models.Filtering;
using System.Text.Json;

namespace TranslationAgentServer.Controllers;

/// <summary>
/// Text verileri için API controller
/// Metin verilerinin CRUD işlemlerini ve özel sorguları yönetir
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class TextController : ControllerBase
{
    private readonly ITextService _textService;
    private readonly ILogger<TextController> _logger;

    public TextController(ITextService textService, ILogger<TextController> logger)
    {
        _textService = textService;
        _logger = logger;
    }

    /// <summary>
    /// Belirtilen projedeki tüm metinleri getirir (filtreleme seçenekleri ile)
    /// </summary>
    /// <param name="ProjectId">Proje ID'si</param>
    /// <returns>Metin listesi</returns>
    [HttpGet("{ProjectId}")]
    public async Task<ActionResult<List<Text>>> GetAllTexts(int ProjectId)
    {
        try
        {
            var texts = await _textService.GetAllTextsAsync(ProjectId);
            return Ok(texts);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Metinler getirilirken hata oluştu. Proje: {ProjectId}", ProjectId);
            return StatusCode(500, "Metinler getirilirken bir hata oluştu.");
        }
    }

    /// <summary>
    /// Gelişmiş filtreleme ile metinleri getirir
    /// </summary>
    /// <param name="ProjectId">Proje ID'si</param>
    /// <param name="filterQuery">Filtreleme sorgusu</param>
    /// <returns>Filtrelenmiş metin listesi</returns>
    [HttpPost("{ProjectId}/filter")]
    public async Task<ActionResult<List<Text>>> GetTextsWithFilter(int ProjectId, [FromBody] TextFilterQuery filterQuery)
    {
        try
        {
            var texts = await _textService.GetAllTextsAsync(ProjectId, filterQuery);
            return Ok(texts);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Filtreleme ile metinler getirilirken hata oluştu. ProjectId: {ProjectId}", ProjectId);
            return StatusCode(500, "Filtreleme ile metinler getirilirken bir hata oluştu.");
        }
    }

    /// <summary>
    /// ID'ye göre metin ve çeviri geçmişini getirir
    /// </summary>
    /// <param name="ProjectId">Proje ID'si</param>
    /// <param name="id">Metin ID</param>
    /// <returns>Metin ve çeviri geçmişi</returns>
    [HttpGet("{ProjectId}/{id}")]
    public async Task<ActionResult<TextTranslation>> GetTextById(int ProjectId, int id)
    {
        try
        {
            var textWithTranslation = await _textService.GetTextTranslationsAsync(id, ProjectId);
            if (textWithTranslation == null)
            {
                return NotFound($"ID {id} ile metin bulunamadı.");
            }
            return Ok(textWithTranslation);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Metin ve çeviri geçmişi getirilirken hata oluştu. ID: {Id}, Şema: {ProjectId}", id, ProjectId);
            return StatusCode(500, "Metin getirilirken bir hata oluştu.");
        }
    }

    /// <summary>
    /// Embedding vektörüne göre benzer metinleri bulur
    /// </summary>
    /// <param name="ProjectId">Proje ID'si</param>
    /// <param name="embedding">Embedding vektörü</param>
    /// <param name="limit">Sonuç limiti</param>
    /// <returns>Benzer metin listesi</returns>
    [HttpPost("{ProjectId}/similar")]
    public async Task<ActionResult<List<Text>>> FindSimilarTexts(int ProjectId, [FromBody] float[] embedding, [FromQuery] int limit = 10)
    {
        try
        {
            if (embedding == null || embedding.Length == 0)
            {
                return BadRequest("Embedding vektörü boş olamaz.");
            }

            var texts = await _textService.FindSimilarTextsAsync(embedding, limit, ProjectId);
            return Ok(texts);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Benzer metin arama işleminde hata oluştu. Şema: {ProjectId}", ProjectId);
            return StatusCode(500, "Benzer metin arama işleminde bir hata oluştu.");
        }
    }

    /// <summary>
    /// Yeni metin oluşturur
    /// </summary>
    /// <param name="ProjectId">Proje ID'si</param>
    /// <param name="textCreateDto">Metin oluşturma DTO</param>
    /// <returns>Oluşturulan metin</returns>
    [HttpPost("{ProjectId}")]
    public async Task<ActionResult<Text>> CreateText(int ProjectId, [FromBody] TextCreateDto textCreateDto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var text = await _textService.CreateTextAsync(textCreateDto, ProjectId);
            return CreatedAtAction(nameof(GetTextById), new { ProjectId, id = text.Id }, text);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Metin oluşturulurken hata oluştu. Şema: {ProjectId}", ProjectId);
            return StatusCode(500, "Metin oluşturulurken bir hata oluştu.");
        }
    }

    /// <summary>
    /// Metinleri toplu olarak oluşturur
    /// </summary>
    /// <param name="ProjectId">Proje ID'si</param>
    /// <param name="textCreateDtos">Metin oluşturma DTO listesi</param>
    /// <param name="cancellationToken">İptal token'ı</param>
    /// <returns>Oluşturma sonucu</returns>
    [HttpPost("{ProjectId}/bulk")]
    public async Task<ActionResult> CreateTexts(int ProjectId, [FromBody] List<TextCreateDto> textCreateDtos, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            if (textCreateDtos == null || textCreateDtos.Count == 0)
            {
                return BadRequest("Metin listesi boş olamaz.");
            }

            var result = await _textService.CreateTextsAsync(textCreateDtos, ProjectId, cancellationToken);
            if (result.Count != textCreateDtos.Count)
            {
                return BadRequest("Metinler oluşturulurken bir hata oluştu.");
            }

            return Ok(new { message = $"{textCreateDtos.Count} metin başarıyla oluşturuldu.", texts = result });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Toplu metin oluşturma işleminde hata oluştu. Proje: {ProjectId}", ProjectId);
            return StatusCode(500, "Toplu metin oluşturma işleminde bir hata oluştu.");
        }
    }

    /// <summary>
    /// Metni günceller
    /// </summary>
    /// <param name="ProjectId">Proje ID'si</param>
    /// <param name="id">Metin ID</param>
    /// <param name="textUpdateDto">Metin güncelleme DTO</param>
    /// <returns>Güncellenmiş metin</returns>
    [HttpPut("{ProjectId}/{id}")]
    public async Task<ActionResult<Text>> UpdateText(int ProjectId, int id, [FromBody] TextUpdateDto textUpdateDto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var text = await _textService.UpdateTextAsync(id, textUpdateDto, ProjectId);
            if (text == null)
            {
                return NotFound($"ID {id} ile metin bulunamadı.");
            }

            return Ok(text);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Metin güncellenirken hata oluştu. ID: {Id}, Şema: {ProjectId}", id, ProjectId);
            return StatusCode(500, "Metin güncellenirken bir hata oluştu.");
        }
    }

    /// <summary>
    /// Metni siler
    /// </summary>
    /// <param name="ProjectId">Proje ID'si</param>
    /// <param name="id">Metin ID</param>
    /// <returns>Silme sonucu</returns>
    [HttpDelete("{ProjectId}/{id}")]
    public async Task<ActionResult> DeleteText(int ProjectId, int id)
    {
        try
        {
            var result = await _textService.DeleteTextAsync(id, ProjectId);
            if (!result)
            {
                return NotFound($"ID {id} ile metin bulunamadı.");
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Metin silinirken hata oluştu. ID: {Id}, Şema: {ProjectId}", id, ProjectId);
            return StatusCode(500, "Metin silinirken bir hata oluştu.");
        }
    }


    /// <summary>
    /// Toplu metin günceller
    /// </summary>
    /// <param name="ProjectId">Proje ID'si</param>
    /// <param name="textUpdates">Metin güncelleme listesi</param>
    /// <returns>Güncellenmiş metin listesi</returns>
    [HttpPut("{ProjectId}/bulk")]
    public async Task<ActionResult<List<Text>>> UpdateTexts(int ProjectId, [FromBody] List<TextUpdateRequest> textUpdates)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var updates = textUpdates.Select(u => (u.Id, u.UpdateDto)).ToList();
            var texts = await _textService.UpdateTextsAsync(updates, ProjectId, CancellationToken.None);
            return Ok(texts);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Toplu metin güncelleme işleminde hata oluştu. Şema: {ProjectId}", ProjectId);
            return StatusCode(500, "Toplu metin güncelleme işleminde bir hata oluştu.");
        }
    }

    /// <summary>
    /// Toplu metin siler
    /// </summary>
    /// <param name="ProjectId">Proje ID'si</param>
    /// <param name="ids">Silinecek metin ID listesi</param>
    /// <returns>Silme sonucu</returns>
    [HttpDelete("{ProjectId}/bulk")]
    public async Task<ActionResult> DeleteTexts(int ProjectId, [FromBody] List<int> ids)
    {
        try
        {
            var result = await _textService.DeleteTextsAsync(ids, ProjectId);
            if (result != ids.Count)
            {
                return BadRequest("Metinler silinirken bir hata oluştu.");
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Toplu metin silme işleminde hata oluştu. Şema: {ProjectId}", ProjectId);
            return StatusCode(500, "Toplu metin silme işleminde bir hata oluştu.");
        }
    }

    /// <summary>
    /// Metin istatistiklerini getirir
    /// </summary>
    /// <param name="ProjectId">Proje ID'si</param>
    /// <returns>Metin istatistikleri</returns>
    [HttpGet("{ProjectId}/statistics")]
    public async Task<ActionResult<TextStatistics>> GetTextStatistics(int ProjectId)
    {
        try
        {
            var statistics = await _textService.GetTextStatisticsAsync(ProjectId);
            return Ok(statistics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Metin istatistikleri getirilirken hata oluştu. Şema: {ProjectId}", ProjectId);
            return StatusCode(500, "Metin istatistikleri getirilirken bir hata oluştu.");
        }
    }
}

/// <summary>
/// Toplu güncelleme için yardımcı sınıf
/// </summary>
public class TextUpdateRequest
{
    public int Id { get; set; }
    public TextUpdateDto UpdateDto { get; set; } = null!;
}