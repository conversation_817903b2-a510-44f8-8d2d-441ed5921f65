using System;
using System.Net;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace TranslationAgentServer.Helpers;

/// <summary>
/// HTTP istekleri için retry mekanizması sağlayan yardımcı sınıf
/// </summary>
public static class RetryHelper
{
    /// <summary>
    /// Belirtilen işlemi retry mekanizması ile çalıştırır
    /// </summary>
    /// <typeparam name="T">Dönüş tipi</typeparam>
    /// <param name="operation">Çalıştırılacak işlem</param>
    /// <param name="maxRetries">Maksimum retry sayısı</param>
    /// <param name="baseDelayMs">Temel bekleme süresi (milisaniye)</param>
    /// <param name="logger">Logger instance</param>
    /// <param name="operationName"><PERSON><PERSON><PERSON> adı (loglama için)</param>
    /// <returns><PERSON><PERSON><PERSON> sonucu</returns>
    public static async Task<T> ExecuteWithRetryAsync<T>(
        Func<Task<T>> operation,
        int maxRetries = 3,
        int baseDelayMs = 1000,
        ILogger? logger = null,
        string operationName = "Operation")
    {
        var attempt = 0;
        Exception? lastException = null;

        while (attempt <= maxRetries)
        {
            try
            {
                if (attempt > 0)
                {
                    logger?.LogInformation("{OperationName} - Deneme {Attempt}/{MaxRetries}",
                        operationName, attempt + 1, maxRetries);
                }

                return await operation();
            }
            catch (HttpRequestException httpEx) when (IsRetryableHttpError(httpEx))
            {
                lastException = httpEx;
                attempt++;

                if (attempt <= maxRetries)
                {
                    var delay = CalculateExponentialBackoff(attempt, baseDelayMs);
                    logger?.LogWarning("{OperationName} - HTTP hatası (deneme {Attempt}/{MaxRetries}): {Error}. {Delay}ms sonra tekrar denenecek.",
                        operationName, attempt, maxRetries + 1, httpEx.Message, delay);

                    await Task.Delay(delay);
                }
            }
            catch (TaskCanceledException tcEx) when (tcEx.InnerException is TimeoutException)
            {
                lastException = tcEx;
                attempt++;

                if (attempt <= maxRetries)
                {
                    var delay = CalculateExponentialBackoff(attempt, baseDelayMs);
                    logger?.LogWarning("{OperationName} - Timeout hatası (deneme {Attempt}/{MaxRetries}): {Error}. {Delay}ms sonra tekrar denenecek.",
                        operationName, attempt, maxRetries + 1, tcEx.Message, delay);

                    await Task.Delay(delay);
                }
            }
            catch (InvalidOperationException invOpEx) when (invOpEx.Message.Contains("Retryable Process"))
            {
                lastException = invOpEx;
                attempt++;

                if (attempt <= maxRetries)
                {
                    var delay = CalculateExponentialBackoff(attempt, baseDelayMs);
                    logger?.LogWarning("{OperationName} - Retryable hata (deneme {Attempt}/{MaxRetries}): {Error}. {Delay}ms sonra tekrar denenecek.",
                        operationName, attempt, maxRetries + 1, invOpEx.Message, delay);

                    await Task.Delay(delay);
                }
            }
            catch (Exception ex)
            {
                // Retry edilemeyecek hatalar için direkt fırlat
                logger?.LogError(ex, "{OperationName} - Retry edilemeyecek hata oluştu", operationName);
                throw;
            }
        }

        logger?.LogError(lastException, "{OperationName} - {MaxRetries} deneme sonrasında başarısız oldu",
            operationName, maxRetries + 1);

        throw lastException ?? new InvalidOperationException($"{operationName} failed after {maxRetries + 1} attempts");
    }

    /// <summary>
    /// HTTP hatasının retry edilebilir olup olmadığını kontrol eder
    /// </summary>
    /// <param name="httpException">HTTP exception</param>
    /// <returns>Retry edilebilir ise true</returns>
    private static bool IsRetryableHttpError(HttpRequestException httpException)
    {
        var message = httpException.Message.ToLowerInvariant();

        // Too Many Requests (429)
        if (message.Contains("429") || message.Contains("too many requests"))
            return true;

        // Service Unavailable (503)
        if (message.Contains("503") || message.Contains("service unavailable"))
            return true;

        // Bad Gateway (502)
        if (message.Contains("502") || message.Contains("bad gateway"))
            return true;

        // Gateway Timeout (504)
        if (message.Contains("504") || message.Contains("gateway timeout"))
            return true;

        // Internal Server Error (500)
        if (message.Contains("500") || message.Contains("internal server error"))
            return true;

        return false;
    }

    /// <summary>
    /// Exponential backoff ile bekleme süresini hesaplar
    /// </summary>
    /// <param name="attempt">Deneme sayısı</param>
    /// <param name="baseDelayMs">Temel bekleme süresi</param>
    /// <returns>Hesaplanan bekleme süresi (milisaniye)</returns>
    private static int CalculateExponentialBackoff(int attempt, int baseDelayMs)
    {
        // Exponential backoff: baseDelay * 2^(attempt-1) + jitter
        var exponentialDelay = baseDelayMs * Math.Pow(2, attempt - 1);

        // Maksimum 30 saniye ile sınırla
        exponentialDelay = Math.Min(exponentialDelay, 30000);

        // Jitter ekle (±20%)
        var random = new Random();
        var jitter = exponentialDelay * 0.2 * (random.NextDouble() - 0.5);

        return (int)(exponentialDelay + jitter);
    }
}