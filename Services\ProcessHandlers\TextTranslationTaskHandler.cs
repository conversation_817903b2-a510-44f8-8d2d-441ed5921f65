using LLMSharp.OpenAi.Tokenizer;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Pgvector.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using TranslationAgentServer.Data;
using TranslationAgentServer.Helpers;
using TranslationAgentServer.Interfaces;
using TranslationAgentServer.Models;
using TranslationAgentServer.Models.Responses;

namespace TranslationAgentServer.Services.ProcessHandlers
{
    /// <summary>
    /// Metin çevirisi görevini işler.
    /// </summary>
    public class TextTranslationTaskHandler : ProcessHandlerBase
    {
        private readonly OpenAiChatCompletionsTokenizer openAITokenizer;
        public TextTranslationTaskHandler(
            IDatabaseService databaseService,
            IGoogleSheetsService googleSheetsService,
            IProjectService projectService,
            ITextService textService,
            ITermService termService,
            IContextService contextService,
            IGeminiService geminiService,
            IWebScraperService webScraperService,

            ILogger<TextTranslationTaskHandler> logger,
            IProcessUpdateService processUpdateService) : base(
                databaseService,
                googleSheetsService,
                projectService,
                textService,
                termService,
                contextService,
                geminiService,
                webScraperService,
                logger,
                processUpdateService)
        {

            openAITokenizer = new OpenAiChatCompletionsTokenizer();
        }

        /// <summary>
        /// Metin çevirisi görevini yürütür
        /// Google Sheets'ten verileri çeker ve Text servisi ile toplu metin oluşturur
        /// </summary>
        public override async Task ExecuteAsync(Process process, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Metin çevirisi task'ı çalıştırılıyor: {ProcessId}", process.Id);

            // Promptları al
            await LoadTextPrompts(process);

            //Worker sayısını al
            int workerCount = process.Settings?.RootElement.TryGetProperty("WorkerCount", out var workerCountProperty) == true &&
                                workerCountProperty.TryGetInt32(out var workerCountValue) ? workerCountValue : 1;

            _logger.LogInformation("Metin çevirisi için {WorkerCount} worker kullanılacak.", workerCount);

            //Worker sayısı kadar taskı sıralayarak delay ile oluştur
            var tasks = new List<Task>();
            for (int i = 0; i < workerCount; i++)
            {
                var workerId = i + 1; // Worker ID'leri 1'den başlasın
                tasks.Add(Task.Run(async () =>
                {
                    await Task.Delay(workerId * 3000);
                    await TranslateTextsWorkerAsync(workerId, process, cancellationToken);
                }, cancellationToken));
            }

            //Taskları sırayla çalıştırarak çalışmasını sağla
            await Task.WhenAll(tasks);
        }

        /// <summary>
        /// Metin çevirisi worker'ını çalıştırır
        /// </summary>
        private async Task TranslateTextsWorkerAsync(int workerId, Process process, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Worker {WorkerId} başlatıldı.", workerId);

            while (true)
            {
                //Worker için kullanılacak metinleri ayır
                var textsForWorker = await process.Data.ExecuteAsync(async () =>
                {
                    var filteredQuery = FilteringService.ApplyFilters(process.Data.Client!.Texts.AsQueryable(), process.Data.TextFilter!);
                    var texts = await filteredQuery
                        .Where(t => !process.Data.IsUsing.Contains(t.Id) && !process.Data.IsProcessed.Contains(t.Id)
                               && t.Status != (int)TextStatus.DUPE && t.Status != (int)TextStatus.NULL && t.Status != (int)TextStatus.TR)
                               .OrderBy(t => t.RowID) // RowID'ye göre sırala
                               .Take(100) // 100 adet metin al
                        .ToListAsync();

                    foreach (var text in texts)
                    {
                        process.Data.IsUsing.Add(text.Id);
                    }

                    return texts;
                }, cancellationToken);

                cancellationToken.ThrowIfCancellationRequested();

                //Worker için kullanılacak metinler varsa gruplama ve çeviri işlemlerini yap
                if (textsForWorker.Count > 0)
                {
                    var iscontinue = false;
                    try
                    {
                        var betweenText = textsForWorker[0].Id + "-" + textsForWorker[^1].Id;
                        _logger.LogInformation("Worker {WorkerId} için {BetweenText} idleri arasındaki toplam {TextCount} metin ayrıldı.",
                            workerId, betweenText, textsForWorker.Count);

                        // Metinleri grupla
                        var groupedTexts = await GroupTextsAsync(process, workerId, textsForWorker, cancellationToken);
                        if (groupedTexts.Groups.Count == 0)
                        {
                            _logger.LogInformation("Worker {WorkerId} için gruplandırılacak metin bulunamadı.", workerId);
                            iscontinue = true;
                            continue;
                        }

                        //Eğer gruplar birden fazlaysa son grubu at ve kullanılan metinleri temizle
                        if (groupedTexts.Groups.Count > 1)
                        {
                            _logger.LogInformation("Worker {WorkerId} için {GroupCount} grup bulundu, son grup atılıyor.", workerId, groupedTexts.Groups.Count);
                            foreach (var text in groupedTexts.Groups.Last().Texts)
                            {
                                process.Data.IsUsing.Remove(text);
                            }
                            groupedTexts.Groups.RemoveAt(groupedTexts.Groups.Count - 1);
                        }

                        foreach (var group in groupedTexts.Groups)
                        {
                            var textsInGroup = textsForWorker.Where(t => group.Texts.Contains(t.Id)).ToList();
                            _logger.LogInformation("Worker {WorkerId} için '{Category}' kategorisinde {Count} metin çevriliyor.",
                                workerId, group.Category, textsInGroup.Count);

                            // Token sayısına göre dinamik batch oluştur
                            var batches = CreateTokenBasedBatches(textsInGroup, process, group.Category);

                            foreach (var batch in batches)
                            {
                                var prompt = await BuildContextualPromptAsync(process, batch, group.Category, cancellationToken);
                                await TranslateTextsAsync(process, workerId, prompt, cancellationToken);

                                await Task.Delay(1000, cancellationToken); // Rate limiting
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Metin çeviri ve gruplama işleminde hata meydana geldi.");
                        if (ex is not InvalidOperationException && !ex.Message.Contains("Retriable Process"))
                        {
                            throw;
                        }
                    }
                    finally
                    {
                        // Exception olsa bile mutlaka temizle
                        await process.Data.ExecuteAsync(() =>
                        {
                            foreach (var text in textsForWorker)
                            {
                                if (process.Data.IsUsing.Contains(text.Id))
                                {
                                    process.Data.IsUsing.Remove(text.Id);
                                }

                                if (!iscontinue && !process.Data.IsProcessed.Contains(text.Id))
                                {
                                    process.Data.IsProcessed.Add(text.Id);
                                }
                            }
                            return Task.CompletedTask;
                        }, cancellationToken);
                    }
                }
                else
                {
                    _logger.LogInformation("Worker {WorkerId} için işlenecek metin kalmadı.", workerId);
                    break;
                }
            }

            _logger.LogInformation("Worker {WorkerId} tamamlandı.", workerId);
        }

        /// <summary>
        /// Token sayısına göre metinleri batch'lere ayırır
        /// </summary>
        /// <param name="texts">Batch'lenecek metinler</param>
        /// <param name="process">Mevcut işlem</param>
        /// <param name="category">Metin kategorisi</param>
        /// <returns>Token limitine göre oluşturulmuş batch'ler</returns>
        private List<List<Text>> CreateTokenBasedBatches(List<Text> texts, Process process, string category)
        {
            var batches = new List<List<Text>>();

            // Token limitini process settings'ten al, yoksa varsayılan 600 kullan
            int maxTokensPerBatch = process.Settings?.RootElement.TryGetProperty("MaxTokensPerBatch", out var tokenLimitProperty) == true &&
                tokenLimitProperty.TryGetInt32(out var tokenLimitValue) ? tokenLimitValue : 600;

            var currentBatch = new List<Text>();
            int currentTokenCount = 0;


            foreach (var text in texts)
            {
                // Bu metnin token sayısını hesapla
                var textTokens = openAITokenizer.CountTokens(text.En);

                // Eğer bu metin eklendiğinde limit aşılacaksa, mevcut batch'i kaydet ve yeni batch başlat
                if (currentBatch.Count > 0 && (currentTokenCount + textTokens) > maxTokensPerBatch)
                {
                    batches.Add(currentBatch);
                    currentBatch.Clear();
                    currentTokenCount = 0;
                }

                // Metni mevcut batch'e ekle
                currentBatch.Add(text);
                currentTokenCount += textTokens;
            }

            // Son batch'i ekle (eğer boş değilse)
            if (currentBatch.Count > 0)
            {
                batches.Add(currentBatch);
            }

            // Eğer hiç batch oluşturulamadıysa, en azından bir batch oluştur
            if (batches.Count == 0 && texts.Count > 0)
            {
                batches.Add(texts);
            }

            _logger.LogInformation("Token tabanlı batch oluşturma: {TextCount} metin {BatchCount} batch'e ayrıldı. Max token limit: {MaxTokens}",
                texts.Count, batches.Count, maxTokensPerBatch);

            return batches;
        }

        /// <summary>
        /// Metinleri gruplandırmak için AI'ya gönderir ve yanıtı parse eder
        /// </summary>
        private async Task<TextGroupResponse> GroupTextsAsync(Process process, int workerId, List<Text> texts, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Worker {WorkerId} için metinler gruplandırılıyor.", workerId);

            StringBuilder combinedText = new("Gruplandırılacak metinler:\n");

            var shouldIncludeProps = process.Settings?.RootElement.TryGetProperty("IncludeNamespaceAndKey", out var includePropsProperty) == true &&
                includePropsProperty.GetBoolean();

            foreach (var text in texts)
            {
                cancellationToken.ThrowIfCancellationRequested();
                combinedText.AppendLine($"ID: {text.Id}");
                if (shouldIncludeProps)
                {
                    if (!string.IsNullOrEmpty(text.Namespace))
                    {
                        combinedText.AppendLine($"Namespace: {text.Namespace}");
                    }
                    if (!string.IsNullOrEmpty(text.Key))
                    {
                        combinedText.AppendLine($"Key: {text.Key}");
                    }
                }
                combinedText.AppendLine($"Metin: {TextProcessingHelper.TruncateText(text.En, 500)}");
                combinedText.AppendLine("---");
            }


            GeminiContentRequest request = new()
            {
                Prompt = combinedText.ToString(),
                Model = process.Settings?.RootElement.TryGetProperty("GroupModel", out var modelProperty) == true ?
                    modelProperty.GetString() ?? "" : "",
                SystemInstruction = process.TextPrompts.Group,
                Temperature = (float)0.7,
                ThinkingBudget = process.Settings?.RootElement.TryGetProperty("ThinkingBudget", out var thinkingBudgetProperty) == true &&
                    thinkingBudgetProperty.TryGetInt32(out var thinkingBudgetValue) ? thinkingBudgetValue : 0
            };

            var response = await _geminiService.GenerateContentAsync(request, cancellationToken, true);
            if (!response.Success)
            {
                throw new InvalidOperationException($"Metin gruplama isteği başarısız oldu. Hata: {response.ErrorMessage}. Retriable Process");
            }

            try
            {
                return JsonSerializer.Deserialize<TextGroupResponse>(response.JsonOutput!) ??
                    throw new InvalidOperationException("Metin gruplama yanıtı deserialize edilemedi. Retriable Process");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Metin gruplama yanıtı işlenirken hata oluştu: {Response}", response.Text);
                throw new InvalidOperationException($"Metin gruplama yanıtı işlenemedi: {ex.Message}. Retriable Process");
            }
        }

        /// <summary>
        /// Çeviri modeline gönderilecek olan bağlam açısından zengin bir prompt hazırlar.
        /// </summary>
        /// <param name="process">Mevcut işlem.</param>
        /// <param name="batch">Çevrilecek metin grubu.</param>
        /// <param name="category">Metinlerin kategorisi.</param>
        /// <param name="cancellationToken">İptal token'ı.</param>
        /// <returns>Oluşturulan bağlamsal prompt.</returns>
        private async Task<string> BuildContextualPromptAsync(Process process, List<Text> batch, string category, CancellationToken cancellationToken)
        {
            StringBuilder combinedText = new();

            switch (category)
            {
                case "UiSystemMechanics":
                    combinedText.AppendLine(process.TextPrompts.UiSystemMechanics);
                    break;
                case "SequentialDialogue":
                    combinedText.AppendLine(process.TextPrompts.SequentialDialogue);
                    break;
                case "MixedDialogue":
                    combinedText.AppendLine(process.TextPrompts.MixedDialogue);
                    break;
                case "InGameContent":
                    combinedText.AppendLine(process.TextPrompts.InGameContent);
                    break;
                case "StoryLore":
                    combinedText.AppendLine(process.TextPrompts.StoryLore);
                    break;
                case "MixedOther":
                    combinedText.AppendLine(process.TextPrompts.MixedOther);
                    break;
                default:
                    combinedText.AppendLine(process.TextPrompts.MixedOther);
                    break;
            }

            var textsData = await process.Data.ExecuteAsync(async () =>
            {
                List<Term> allterms = new();
                List<Context> allContexts = new();
                List<Text> allSimilarTexts = new();
                foreach (var text in batch)
                {
                    cancellationToken.ThrowIfCancellationRequested();
                    var terms = await process.Data.Client!.Terms
                    .Where(t => t.Status == (int)TermStatus.TR)
                    .Where(t => text.EnTsvector.Matches(EF.Functions.ToTsQuery(t.En)))
                    .Take(10)
                    .ToListAsync(cancellationToken);

                    var termsStage2 = await process.Data.Client!.Terms
                    .Where(t => t.Status == (int)TermStatus.TR)
                    .Where(t => t.Embedding.CosineDistance(text.Embedding) < 0.15)
                    .Take(2)
                    .ToListAsync(cancellationToken);

                    if (termsStage2.Count > 0)
                    {
                        terms = terms.Union(termsStage2).ToList();
                    }

                    var contexts = await process.Data.Client!.Contexts
                    .Where(c => c.CombinedTsvector.Matches(EF.Functions.ToTsQuery(text.En))).Take(3)
                    .ToListAsync(cancellationToken);
                    int ct = 0;

                    if (contexts.Count < 3)
                    {
                        ct = 3 - contexts.Count;
                    }

                    if (ct > 0)
                    {
                        var contextsStage2 = await process.Data.Client!.Contexts
                            .Where(c => c.Embedding.CosineDistance(text.Embedding) < 0.15)
                            .OrderBy(c => c.Embedding.CosineDistance(text.Embedding)).Take(ct)
                    .ToListAsync(cancellationToken);

                        contexts = contexts.Union(contextsStage2).ToList(); // Eşleşen bağlamları birleştir
                    }

                    var similarTexts = await process.Data.Client!.Texts
                    .Where(t => t.Status == (int)TextStatus.TR && t.Id != text.Id)
                    .Where(t =>
                        EF.Functions.ILike(t.Lemma, $"%{text.Lemma}%") ||
                        EF.Functions.ILike(text.Lemma, $"%{t.Lemma}%"))
                    .Take(3)
                    .ToListAsync(cancellationToken);

                    int st = 0;

                    if (similarTexts.Count < 3)
                    {
                        st = 3 - similarTexts.Count;
                    }

                    if (st > 0)
                    {
                        var similarTextsStage2 = await process.Data.Client!.Texts
                        .Where(t => t.Status == (int)TextStatus.TR && t.Id != text.Id)
                        .Where(t => t.EnTsvector.Matches(EF.Functions.ToTsQuery(text.En)) ||
                        t.Embedding.CosineDistance(text.Embedding) < 0.15)
                        .OrderBy(t => t.Embedding.CosineDistance(text.Embedding))
                        .Take(st)
                        .ToListAsync(cancellationToken);

                        similarTexts = similarTexts.Union(similarTextsStage2).ToList();
                    }

                    allterms = allterms.Union(terms).ToList(); // Eşleşen terimleri birleştir
                    allContexts = allContexts.Union(contexts).ToList(); // Eşleşen bağlamları birleştir
                    allSimilarTexts = allSimilarTexts.Union(similarTexts).ToList(); // Eşleşen metinleri birleştir
                }

                return (allterms, allContexts, allSimilarTexts);
            }, cancellationToken);

            // Promptu oluştur
            // Terimleri listele
            if (textsData.allterms.Count > 0)
            {
                combinedText.AppendLine("Terimler:");
                foreach (var term in textsData.allterms)
                {
                    combinedText.AppendLine($"- {term.En} : {term.Tr}");
                    combinedText.AppendLine($"  ({term.Category}) - {term.Info}");
                    combinedText.AppendLine("---");
                }
            }

            // Bağlamları listele
            if (textsData.allContexts.Count > 0)
            {
                combinedText.AppendLine("Bağlamlar:");
                foreach (var context in textsData.allContexts)
                {
                    combinedText.AppendLine($"- {context.Title} ({context.Category})");
                    combinedText.AppendLine($"  {context.Content}");
                    combinedText.AppendLine("---");
                }
            }

            // Benzer metinleri listele
            if (textsData.allSimilarTexts.Count > 0)
            {
                combinedText.AppendLine("Benzer metinler:");
                foreach (var text in textsData.allSimilarTexts)
                {
                    combinedText.AppendLine($"- {text.En}");
                    combinedText.AppendLine($"  {text.Tr}");
                    combinedText.AppendLine("---");
                }
            }

            combinedText.AppendLine("Çevrilecek metinler:");
            var shouldIncludeProps = process.Settings?.RootElement.TryGetProperty("IncludeNamespaceAndKey", out var includePropsProperty) == true &&
                includePropsProperty.GetBoolean();

            foreach (var text in batch)
            {
                cancellationToken.ThrowIfCancellationRequested();
                combinedText.AppendLine($"ID: {text.Id}");
                if (shouldIncludeProps)
                {
                    if (!string.IsNullOrEmpty(text.Namespace))
                    {
                        combinedText.AppendLine($"Namespace: {text.Namespace}");
                    }
                    if (!string.IsNullOrEmpty(text.Key))
                    {
                        combinedText.AppendLine($"Key: {text.Key}");
                    }
                }
                combinedText.AppendLine($"Metin: {text.En}");
                combinedText.AppendLine("---");
            }

            return combinedText.ToString();
        }

        /// <summary>
        /// Metinleri çevirmek için AI'ya gönderir ve yanıtı parse eder
        /// </summary>
        private async Task TranslateTextsAsync(Process process, int workerId, string prompt, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Worker {WorkerId} metin çevriliyor.", workerId);

            GeminiContentRequest request = new()
            {
                Prompt = prompt,
                Model = process.Settings?.RootElement.TryGetProperty("Model", out var modelProperty) == true ?
                    modelProperty.GetString() ?? "" : "",
                SystemInstruction = process.TextPrompts.Translate,
                Temperature = (float)0.7,
                ThinkingBudget = process.Settings?.RootElement.TryGetProperty("ThinkingBudget", out var thinkingBudgetProperty) == true &&
                    thinkingBudgetProperty.TryGetInt32(out var thinkingBudgetValue) ? thinkingBudgetValue : 0
            };

            var response = await _geminiService.GenerateContentAsync(request, cancellationToken, true);
            if (!response.Success)
            {
                throw new InvalidOperationException($"Metin çevirme isteği başarısız oldu. Hata: {response.ErrorMessage}. Retriable Process");
            }

            try
            {
                var translations = JsonSerializer.Deserialize<TranslationResponse>(response.JsonOutput!) ??
                    throw new InvalidOperationException("Metin çevirme yanıtı deserialize edilemedi. Retriable Process");

                if (translations.Texts.Count > 0)
                {
                    await process.Data.ExecuteAsync(async () =>
                    {
                        foreach (var translation in translations.Texts)
                        {
                            var text = await process.Data.Client!.Texts.FirstAsync(t => t.Id == translation.ID);
                            text.Tr = translation.Tr;
                            text.Status = (int)TextStatus.TR;

                            var textTranslation = new TextTranslation
                            {
                                Id = text.Id,
                                AiModel = request.Model,
                                Prompt = request.Prompt,
                                Result = translation.Tr,
                                Reasoning = response.Text,
                                ProcessedAt = DateTime.UtcNow
                            };

                            await process.Data.Client!.TextTranslations.AddAsync(textTranslation);
                            if (process.Data.IsUsing.Contains(text.Id))
                            {
                                process.Data.IsUsing.Remove(text.Id);
                            }

                            if (!process.Data.IsProcessed.Contains(text.Id))
                            {
                                process.Data.IsProcessed.Add(text.Id);
                            }
                        }
                        await process.Data.Client!.SaveChangesAsync();
                    }, cancellationToken);

                }
                else
                {
                    _logger.LogError("Worker {WorkerId} çevrilmiş metin yok.", workerId);
                    throw new InvalidOperationException($"Worker {workerId} çevrilmiş metin yok. Retriable Process");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Metin çevirme yanıtı işlenirken hata oluştu: {Response}", response.Text);
                throw new InvalidOperationException($"Metin çevirme yanıtı işlenemedi: {ex.Message}. Retriable Process");
            }
        }
    }
}