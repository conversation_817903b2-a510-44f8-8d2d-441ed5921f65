using TranslationAgentServer.Models;
using TranslationAgentServer.Models.Filtering;
using System.Text.Json;

namespace TranslationAgentServer.Interfaces;

/// <summary>
/// Text servisi arayüzü
/// Metin verilerinin CRUD işlemlerini ve özel sorguları tanımlar
/// </summary>
public interface ITextService
{
    /// <summary>
    /// Belirtilen projedeki tüm metinleri getirir (filtreleme seçenekleri ile)
    /// </summary>
    /// <param name="ProjectId">Proje ID'si</param>
    /// <returns>Metin listesi</returns>
    Task<List<Text>> GetAllTextsAsync(int ProjectId);

    /// <summary>
    /// Gelişmiş filtreleme ile metinleri getirir
    /// </summary>
    /// <param name="ProjectId">Proje ID'si</param>
    /// <param name="filterQuery">Filtreleme sorgusu</param>
    /// <returns>Filtrelenmiş metin listesi</returns>
    Task<List<Text>> GetAllTextsAsync(int ProjectId, TextFilterQuery filterQuery);

    /// <summary>
    /// JsonElement ile filtreleme (backend kullanımı için)
    /// </summary>
    /// <param name="ProjectId">Proje ID'si</param>
    /// <param name="filterElement">JsonElement formatında filtreleme sorgusu</param>
    /// <returns>Filtrelenmiş metin listesi</returns>
    Task<List<Text>> GetAllTextsAsync(int ProjectId, JsonElement filterElement);

    /// <summary>
    /// ID'ye göre metin getirir
    /// </summary>
    /// <param name="id">Metin ID'si</param>
    /// <param name="ProjectId">Proje ID'si</param>
    /// <returns>Metin</returns>
    Task<Text?> GetTextByIdAsync(int id, int ProjectId);

    /// <summary>
    /// ID'ye göre metin ve çeviri geçmişini getirir
    /// </summary>
    /// <param name="id">Metin ID'si</param>
    /// <param name="ProjectId">Proje ID'si</param>
    /// <returns>Metin ve çeviri geçmişi</returns>
    Task<TextTranslation?> GetTextTranslationsAsync(int id, int ProjectId);

    /// <summary>
    /// Embedding vektörüne göre benzer metinleri bulur
    /// </summary>
    /// <param name="embedding">Embedding vektörü</param>
    /// <param name="limit">Sonuç limiti</param>
    /// <param name="ProjectId">Proje ID'si</param>
    /// <returns>Benzer metin listesi</returns>
    Task<List<Text>> FindSimilarTextsAsync(float[] embedding, int limit, int ProjectId);

    /// <summary>
    /// Yeni metin oluşturur
    /// </summary>
    /// <param name="textCreateDto">Metin oluşturma DTO'su</param>
    /// <param name="ProjectId">Proje ID'si</param>
    /// <returns>Oluşturulan metin</returns>
    Task<Text?> CreateTextAsync(TextCreateDto textCreateDto, int ProjectId);

    /// <summary>
    /// Yeni metin ekler
    /// </summary>
    /// <param name="text">Eklenen metin</param>
    /// <param name="ProjectId">Proje ID'si</param>
    /// <returns>Eklenen metin</returns>
    Task<Text?> AddTextAsync(Text text, int ProjectId);

    /// <summary>
    /// Metin günceller
    /// </summary>
    /// <param name="id">Metin ID'si</param>
    /// <param name="textUpdateDto">Metin güncelleme DTO'su</param>
    /// <param name="ProjectId">Proje ID'si</param>
    /// <returns>Güncellenmiş metin</returns>
    Task<Text?> UpdateTextAsync(int id, TextUpdateDto textUpdateDto, int ProjectId);

    /// <summary>
    /// Metin siler
    /// </summary>
    /// <param name="id">Metin ID'si</param>
    /// <param name="ProjectId">Proje ID'si</param>
    /// <returns>Silme işlemi başarılı mı</returns>
    Task<bool> DeleteTextAsync(int id, int ProjectId);

    /// <summary>
    /// Metinleri toplu olarak oluşturur
    /// </summary>
    /// <param name="textCreateDtos">Metin oluşturma DTO listesi</param>
    /// <param name="ProjectId">Proje ID'si</param>
    /// <returns>Oluşturulan metin listesi</returns>
    Task<List<Text>> CreateTextsAsync(List<TextCreateDto> textCreateDtos, int projectId, CancellationToken cancellationToken);

    /// <summary>
    /// Metinleri toplu olarak ekler
    /// </summary>
    /// <param name="addTexts">Eklenen metin listesi</param>
    /// <param name="ProjectId">Proje ID'si</param>
    /// <returns>Eklenen metin sayısı</returns>
    Task<int> AddTextsAsync(List<Text> addTexts, int projectId, CancellationToken cancellationToken);

    /// <summary>
    /// Metinleri toplu olarak günceller
    /// </summary>
    /// <param name="textUpdates">Metin güncelleme listesi (ID ve DTO çiftleri)</param>
    /// <param name="ProjectId">Proje ID'si</param>
    /// <returns>Güncellenmiş metin sayısı</returns>
    Task<int> UpdateTextsAsync(List<(int Id, TextUpdateDto UpdateDto)> textUpdates, int ProjectId, CancellationToken cancellationToken);

    /// <summary>
    /// Metinleri toplu olarak siler
    /// </summary>
    /// <param name="ids">Silinecek metin ID'leri</param>
    /// <param name="ProjectId">Proje ID'si</param>
    /// <returns>Silinen metin sayısı</returns>
    Task<int> DeleteTextsAsync(List<int> ids, int ProjectId);


    /// <summary>
    /// Metin istatistiklerini getirir
    /// </summary>
    /// <param name="ProjectId">Proje ID'si</param>
    /// <returns>Metin istatistikleri</returns>
    Task<TextStatistics> GetTextStatisticsAsync(int ProjectId);
}

/// <summary>
/// Metin istatistikleri modeli
/// </summary>
public class TextStatistics
{
    /// <summary>
    /// Toplam metin sayısı
    /// </summary>
    public int TotalTexts { get; set; }

    /// <summary>
    /// İngilizce metin sayısı
    /// </summary>
    public int EnglishTexts { get; set; }

    /// <summary>
    /// Türkçe metin sayısı
    /// </summary>
    public int TurkishTexts { get; set; }

    /// <summary>
    /// Tekrar eden metin sayısı
    /// </summary>
    public int DuplicateTexts { get; set; }

    /// <summary>
    /// Boş metin sayısı
    /// </summary>
    public int NullTexts { get; set; }

    /// <summary>
    /// Embedding'i olan metin sayısı
    /// </summary>
    public int TextsWithEmbedding { get; set; }

    /// <summary>
    /// Lemmatize edilmiş metin sayısı
    /// </summary>
    public int LemmatizedTexts { get; set; }

    /// <summary>
    /// Namespace'lere göre dağılım
    /// </summary>
    public Dictionary<string, int> NamespaceDistribution { get; set; } = new();

    /// <summary>
    /// Sheet'lere göre dağılım
    /// </summary>
    public Dictionary<int, int> SheetDistribution { get; set; } = new();
}

