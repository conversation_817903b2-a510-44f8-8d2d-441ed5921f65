using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Catalyst;
using Mosaik.Core;
using TranslationAgentServer.Interfaces;

namespace TranslationAgentServer.Helpers
{
    /// <summary>
    /// Metin işleme için yardımcı fonksiyonlar
    /// </summary>
    public static class TextProcessingHelper
    {
        private static INlpService? _nlpService;

        /// <summary>
        /// NLP servisini ayarlar (Dependency Injection için)
        /// </summary>
        /// <param name="nlpService">NLP servisi</param>
        public static void SetNlpService(INlpService nlpService)
        {
            _nlpService = nlpService;
        }


        /// <summary>
        /// Verilen terimi temizler ve stopword'leri kaldırır
        /// </summary>
        /// <param name="term">Temizlenecek terim</param>
        /// <returns>Temizlenmiş terim</returns>
        /// 
        public static string TermCleanStopWords(string? term)
        {
            if (string.IsNullOrWhiteSpace(term))
                return string.Empty;
            // Stopword'leri temizle (a, an, the)

            string[] stopwords = { "a", "an", "the" };
            foreach (var stopword in stopwords)
            {
                // Kelimenin başında ve ardından bir boşlukla eşleşen stopword'leri temizle
                term = Regex.Replace(term, $"^{stopword}\\s+", "", RegexOptions.IgnoreCase);
            }

            return term;
        }

        /// <summary>
        /// Verilen metni lemmatization işleminden geçirir (NLP Service ile - Async)
        /// </summary>
        /// <param name="text">İşlenecek metin</param>
        /// <returns>Lemmatized ve temizlenmiş metin</returns>
        public static async Task<string> ProcessTextToLemmaAsync(string? text)
        {
            if (string.IsNullOrWhiteSpace(text))
                return string.Empty;

            if (_nlpService == null)
            {
                // NLP servisi yoksa basit temizleme yap
                return Regex.Replace(text.Trim(), @"\s+", " ");
            }

            try
            {
                return await _nlpService.ProcessTextToLemmaAsync(text);
            }
            catch (Exception)
            {
                // Hata durumunda orijinal metni döndür (temizlenmiş haliyle)
                return Regex.Replace(text.Trim(), @"\s+", " ");
            }
        }



        /// <summary>
        /// Metni NLP ile işler ve lemmatize terim etrafında 500 karaktere kısaltır
        /// </summary>
        /// <param name="text">İşlenecek metin</param>
        /// <param name="term">Aranacak lemmatize terim</param>
        /// <returns>Kısaltılmış metin</returns>
        public static async Task<string> ProcessTextWithLemmaSearchAsync(string text, string term)
        {
            try
            {
                if (text.Length < 500)
                {
                    return text;
                }
                // Metni cümlelere ayır
                var doc = new Document(text, Language.English);
                _nlpService!.GetPipeline()!.ProcessSingle(doc);

                var lemmatizedTerm = await _nlpService.ProcessTextToLemmaAsync(term);

                // Lemmatize terimi içeren cümleyi bul
                var targetSentence = string.Empty;
                var targetSentenceIndex = -1;
                var regexLemma = new Regex($@"\b{Regex.Escape(lemmatizedTerm)}\b", RegexOptions.IgnoreCase | RegexOptions.Compiled);
                for (int i = 0; i < doc.Length; i++)
                {
                    var sentence = doc[i];

                    var sentenceLemmas = await _nlpService.ProcessTextToLemmaAsync(sentence.Value);

                    if (regexLemma.IsMatch(sentenceLemmas))
                    {
                        targetSentence = sentence.Value;
                        targetSentenceIndex = i;
                        break;
                    }
                }

                // Eğer terim bulunamazsa, orijinal metni 500 karaktere kısalt
                if (targetSentenceIndex == -1)
                {
                    return TruncateText(text, 500);
                }

                // Hedef cümle etrafında 500 karakterlik bölge oluştur
                return CreateContextAroundSentence(text, targetSentence, 500);
            }
            catch (Exception)
            {
                // Hata durumunda metni basit şekilde kısalt
                return TruncateText(text, 500);
            }
        }



        /// <summary>
        /// Verilen cümle etrafında belirtilen karakter sayısında bağlam oluşturur
        /// </summary>
        /// <param name="fullText">Tam metin</param>
        /// <param name="targetSentence">Hedef cümle</param>
        /// <param name="maxLength">Maksimum karakter sayısı</param>
        /// <returns>Bağlam metni</returns>
        public static string CreateContextAroundSentence(string fullText, string targetSentence, int maxLength)
        {
            var sentenceIndex = fullText.IndexOf(targetSentence, StringComparison.OrdinalIgnoreCase);
            if (sentenceIndex == -1)
            {
                return TruncateText(fullText, maxLength);
            }

            var sentenceLength = targetSentence.Length;
            var remainingChars = maxLength - sentenceLength;

            if (remainingChars <= 0)
            {
                // Cümle zaten çok uzunsa, cümleyi kısalt
                return "..." + targetSentence.Substring(0, maxLength - 6) + "...";
            }

            var beforeChars = remainingChars / 2;
            var afterChars = remainingChars - beforeChars;

            // Kelime sınırlarını bul
            var startIndex = FindNearestWordBoundary(fullText, Math.Max(0, sentenceIndex - beforeChars), false);
            var endIndex = FindNearestWordBoundary(fullText, Math.Min(fullText.Length, sentenceIndex + sentenceLength + afterChars), true);

            var result = fullText.Substring(startIndex, endIndex - startIndex);

            // Başına ve sonuna "..." ekle (eğer kısaltma yapıldıysa)
            if (startIndex > 0)
                result = "..." + result;
            if (endIndex < fullText.Length)
                result = result + "...";

            return result;
        }



        /// <summary>
        /// Verilen pozisyona en yakın kelime sınırını bulur
        /// </summary>
        /// <param name="text">Aranacak metin</param>
        /// <param name="position">Başlangıç pozisyonu</param>
        /// <param name="forward">İleriye doğru mu arama yapılacak</param>
        /// <returns>Kelim sınırı pozisyonu</returns>
        private static int FindNearestWordBoundary(string text, int position, bool forward)
        {
            if (position <= 0 || position >= text.Length)
                return position;

            var step = forward ? 1 : -1;

            while (position > 0 && position < text.Length && !char.IsWhiteSpace(text[position]))
            {
                position += step;
            }

            return position;
        }


        /// <summary>
        /// Verilen metni belirtilen uzunlukta kısaltır ve sonuna "..." ekler.
        /// </summary>
        /// <param name="text">Kısaltılacak metin.</param>
        /// <param name="maxLength">Maksimum uzunluk.</param>
        /// <returns>Kısaltılmış metin.</returns>
        public static string TruncateText(string? text, int maxLength)
        {
            if (string.IsNullOrWhiteSpace(text))
                return string.Empty;

            if (text.Length <= maxLength)
                return text;

            return text.Substring(0, maxLength) + "...";
        }
    }

}
