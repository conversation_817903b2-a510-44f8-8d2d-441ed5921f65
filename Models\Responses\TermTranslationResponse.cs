// <summary>
// AI tarafından döndürülen terim çevirisi yanıtını temsil eder. Sadece TermTranslation işlemi için kullan<PERSON>lır.
// </summary>
namespace TranslationAgentServer.Models.Responses
{
    public class TermTranslationResponse
    {

        /// <summary>
        /// Terim
        /// </summary>
        public string? Term { get; set; }

        /// <summary>
        /// Çevrilen terim.
        /// </summary>
        public string? Translation { get; set; }

        /// <summary>
        /// Terimin kategorisi (varsa).
        /// </summary>
        public string? Category { get; set; }

        /// <summary>
        /// Terim hakkında ek bilgi (varsa).
        /// </summary>
        public string? Info { get; set; }

        /// <summary>
        /// Diğer seçenekler (varsa).
        /// </summary>
        public List<string>? OtherChoices { get; set; }
    }
}