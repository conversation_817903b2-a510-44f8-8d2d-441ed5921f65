using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;
using Newtonsoft.Json;
using TranslationAgentServer.Helpers;

namespace TranslationAgentServer.Models;

/// <summary>
/// Google Sheets entegrasyonu ve çeviri projelerini yönetir
/// </summary>
[Table("projects")]
public class Project
{
    /// <summary>
    /// Projenin benzersiz kimliği
    /// </summary>
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    [Column("id")]
    public int Id { get; set; }

    /// <summary>
    /// Projenin oluşturulma tarihi
    /// </summary>
    [Column("created_at")]
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Proje adı
    /// </summary>
    [Column("name")]
    [StringLength(255)]
    public string? Name { get; set; }

    /// <summary>
    /// Google Sheets doküman kimliği
    /// </summary>
    [Column("spreadsheet_id")]
    [StringLength(500)]
    public string? SpreadsheetId { get; set; }

    /// <summary>
    /// Metinler sayfa adı (Google Sheets içindeki sayfa)
    /// </summary>
    [Column("texts_table")]
    [StringLength(255)]
    public string? TextsTable { get; set; }

    /// <summary>
    /// Terimce sayfa adı (Google Sheets içindeki sayfa)
    /// </summary>
    [Column("terms_table")]
    [StringLength(255)]
    public string? TermsTable { get; set; }

    /// <summary>
    /// Şema adı
    /// </summay>
    [Column("schema_name")]
    [StringLength(255)]
    public string? SchemaName { get; set; }

    /// <summary>
    /// Proje ana bağlamı
    /// </summary>
    [Column("main_context")]
    public string? MainContext { get; set; }


    /// <summary>
    /// Proje ayarları (JSON formatında)
    /// </summary>
    [Column("settings")]
    [JsonConverter(typeof(JsonDocumentConverter))]
    public JsonDocument? Settings { get; set; }


    public override bool Equals(object? obj)
    {
        return obj is Project other && Id == other.Id;
    }

    public override int GetHashCode()
    {
        return Id.GetHashCode();
    }
}

/// <summary>
/// Proje oluşturma ve güncelleme için kullanılan DTO modeli
/// </summary>
public class ProjectDto
{
    /// <summary>
    /// Proje adı
    /// </summary>
    [Required(ErrorMessage = "Proje adı zorunludur")]
    [StringLength(255, ErrorMessage = "Proje adı en fazla 255 karakter olabilir")]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Google Sheets doküman kimliği
    /// </summary>
    [StringLength(500, ErrorMessage = "Spreadsheet ID en fazla 500 karakter olabilir")]
    public string? SpreadsheetId { get; set; }

    /// <summary>
    /// Metinler sayfa adı
    /// </summary>
    [StringLength(255, ErrorMessage = "Metinler sayfa adı en fazla 255 karakter olabilir")]
    public string? TextsTable { get; set; }

    /// <summary>
    /// Terimce sayfa adı
    /// </summary>
    [StringLength(255, ErrorMessage = "Terimce sayfa adı en fazla 255 karakter olabilir")]
    public string? TermsTable { get; set; }

    /// <summary>
    /// Şema adı
    /// </summary>
    [StringLength(255, ErrorMessage = "Şema adı en fazla 255 karakter olabilir")]
    public string? SchemaName { get; set; }

    /// <summary>
    /// Proje ana bağlamı
    /// </summary>
    public string? MainContext { get; set; }

    /// <summary>
    /// Proje ayarları (JSON formatında)
    /// </summary>
    public JsonDocument? Settings { get; set; }
}

/// <summary>
/// Proje güncelleme için kullanılan DTO modeli
/// </summary>
public class ProjectUpdateDto
{
    /// <summary>
    /// Proje adı
    /// </summary>
    [StringLength(255, ErrorMessage = "Proje adı en fazla 255 karakter olabilir")]
    public string? Name { get; set; }

    /// <summary>
    /// Google Sheets doküman kimliği
    /// </summary>
    [StringLength(500, ErrorMessage = "Spreadsheet ID en fazla 500 karakter olabilir")]
    public string? SpreadsheetId { get; set; }

    /// <summary>
    /// Metinler sayfa adı
    /// </summary>
    [StringLength(255, ErrorMessage = "Metinler sayfa adı en fazla 255 karakter olabilir")]
    public string? TextsTable { get; set; }

    /// <summary>
    /// Terimce sayfa adı
    /// </summary>
    [StringLength(255, ErrorMessage = "Terimce sayfa adı en fazla 255 karakter olabilir")]
    public string? TermsTable { get; set; }

    /// <summary>
    /// Proje ana bağlamı
    /// </summary>
    public string? MainContext { get; set; }

    /// <summary>
    /// Proje ayarları (JSON formatında)
    /// </summary>
    public JsonDocument? Settings { get; set; }

}



public class CreateSchemaResult
{
    /// <summary>
    /// İşlemin başarılı olup olmadığını belirtir.
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// İşlemle ilgili mesajı içerir.
    /// </summary>
    public string? Message { get; set; }

    /// <summary>
    /// Şemanın yeni oluşturulup oluşturulmadığını belirtir. (True ise yeni oluşturuldu, False ise zaten vardı)
    /// </summary>
    public bool Schema_Created { get; set; }

    /// <summary>
    /// Oluşturulan tabloların listesini içerir.
    /// </summary>
    public List<string>? Tables_Created { get; set; }
}
public class SchemaAccessResult
{
    public bool Success { get; set; }
    public string? Message { get; set; }
}