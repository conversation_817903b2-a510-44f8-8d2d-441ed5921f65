using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Options;
using Mscc.GenerativeAI;
using TranslationAgentServer.Interfaces;
using TranslationAgentServer.Models;
namespace TranslationAgentServer.Services;

/// <summary>
/// Gemini AI servisi - Google'ın generative AI modellerini kullanarak içerik üretimi ve embedding işlemleri yapar
/// </summary>
public class GeminiService : IGeminiService
{

    private GoogleAI? _googleAI;
    private readonly ILogger<GeminiService> _logger;
    private bool _isInitialized = false;
    private GeminiOptions _options;
    private HttpClient _httpClient;

    public GeminiService(ILogger<GeminiService> logger)
    {
        _logger = logger;
        _logger.LogInformation("Gemini servis oluşturuldu.");
    }

    public bool InitializeAsync(string apiKey)
    {
        if (_isInitialized && _googleAI != null)
            return false;

        try
        {

            if (string.IsNullOrEmpty(apiKey))
            {
                throw new InvalidOperationException("Gemini API anahtarı bulunamadı. Lütfen appsettings.json'da veya main tablosunda 'gemini_api' anahtarını yapılandırın.");
            }
            _options = new GeminiOptions
            {
                ApiKey = apiKey,
                BaseUrl = "https://generativelanguage.googleapis.com/v1beta",
                TimeoutSeconds = 30
            };
            _googleAI = new GoogleAI(apiKey);
            _httpClient = new HttpClient
            {
                Timeout = TimeSpan.FromSeconds(_options.TimeoutSeconds)
            };
            _isInitialized = true;
            _logger.LogInformation("Gemini servis başarıyla başlatıldı.");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Gemini servis başlatılırken hata oluştu.");
            throw;
        }



    }


    /// <summary>
    /// Belirtilen model ve ayarlarla metin içeriği üretir (retry mekanizması ile)
    /// </summary>
    public async Task<GeminiContentResponse> GenerateContentAsync(GeminiContentRequest request, CancellationToken cancellationToken, bool forceJsonOutput = false)
    {
        if (request == null)
            throw new ArgumentNullException(nameof(request));

        _logger.LogInformation("Gemini içerik üretimi başlatılıyor. Model: {Model}", request.Model ?? Model.Gemini25Flash);

        return await Helpers.RetryHelper.ExecuteWithRetryAsync(
            operation: async () => await GenerateContentInternalAsync(request, cancellationToken, forceJsonOutput),
            maxRetries: 3,
            baseDelayMs: 2000,
            logger: _logger,
            operationName: $"GenerateContent({request.Model ?? Model.Gemini25Flash})"
        );
    }

    /// <summary>
    /// İçerik üretimi için internal metod
    /// </summary>
    private async Task<GeminiContentResponse> GenerateContentInternalAsync(GeminiContentRequest request, CancellationToken cancellationToken, bool forceJsonOutput = false)
    {
        var stopwatch = Stopwatch.StartNew();

        try
        {
            cancellationToken.ThrowIfCancellationRequested();
            if (_googleAI == null)
                throw new InvalidOperationException("Gemini AI servisi başlatılamadı.");

            // Model seçimi
            var modelName = request.Model ?? Model.Gemini25Flash;
            var model = _googleAI.GenerativeModel(model: modelName);

            // Sistem talimatı varsa ekle
            if (!string.IsNullOrEmpty(request.SystemInstruction))
            {
                var systemInstruction = new Mscc.GenerativeAI.Content(request.SystemInstruction);
                model = _googleAI.GenerativeModel(model: modelName, systemInstruction: systemInstruction);
            }

            // Grounding ayarları
            if (request.UseGrounding)
            {
                model.UseGrounding = true;
            }

            // Generation config ayarları
            GenerationConfig? generationConfig = null;
            if (request.MaxTokens.HasValue || request.Temperature.HasValue ||
                request.TopP.HasValue || request.TopK.HasValue || request.RequireJsonOutput || request.ThinkingBudget.HasValue)
            {
                generationConfig = new GenerationConfig();

                if (request.MaxTokens.HasValue)
                    generationConfig.MaxOutputTokens = request.MaxTokens.Value;

                if (request.Temperature.HasValue)
                    generationConfig.Temperature = request.Temperature.Value;

                if (request.TopP.HasValue)
                    generationConfig.TopP = request.TopP.Value;

                if (request.TopK.HasValue)
                    generationConfig.TopK = request.TopK.Value;

                if (request.RequireJsonOutput)
                    generationConfig.ResponseMimeType = "application/json";

                if (request.ThinkingBudget.HasValue)
                {
                    generationConfig.ThinkingConfig = new ThinkingConfig { ThinkingBudget = request.ThinkingBudget.Value, IncludeThoughts = true };
                }
                else
                {
                    generationConfig.ThinkingConfig = new ThinkingConfig { ThinkingBudget = 0 };
                }


            }

            // İçerik üretimi
            var response = generationConfig != null
                ? await model.GenerateContent(request.Prompt, generationConfig: generationConfig, cancellationToken: cancellationToken)
                : await model.GenerateContent(request.Prompt, cancellationToken: cancellationToken);

            if (string.IsNullOrEmpty(response.Text))
            {
                throw new InvalidOperationException("Response is null or empty. Retryable Process.");
            }
            string jsonContent = "";
            if (forceJsonOutput)
            {


                // Eğer response markdown code block içindeyse JSON kısmını ayır
                var match = Regex.Match(response.Text, @"```(?:json)?\s*({[\s\S]*?})\s*```");
                if (match.Success)
                {
                    jsonContent = match.Groups[1].Value;
                }
                else
                {
                    // Code block yoksa, sadece JSON objesini bulmaya çalış
                    var jsonMatch = Regex.Match(response.Text, @"({[\s\S]*?})");
                    if (jsonMatch.Success)
                    {
                        jsonContent = jsonMatch.Groups[1].Value;
                    }
                }

            }

            stopwatch.Stop();

            _logger.LogInformation("Gemini içerik üretimi tamamlandı. Süre: {Duration}ms, Model: {Model}",
                stopwatch.ElapsedMilliseconds, modelName);



            return new GeminiContentResponse
            {
                Text = response.Text ?? string.Empty,
                JsonOutput = jsonContent,
                Model = modelName,
                Success = true,
                ProcessingTimeMs = stopwatch.ElapsedMilliseconds,
                TokensUsed = response.UsageMetadata.TotalTokenCount
            };
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Gemini içerik üretimi sırasında hata oluştu.");

            throw;
        }
    }

    /// <summary>
    /// Tekli metin için embedding üretir (retry mekanizması ile)
    /// </summary>
    public async Task<GeminiEmbeddingResponse> GenerateEmbeddingAsync(string text, string? model = null)
    {
        if (string.IsNullOrEmpty(text))
            throw new ArgumentException("Text cannot be null or empty", nameof(text));

        var embeddingModel = model ?? Model.TextEmbedding;
        _logger.LogInformation("Gemini embedding üretimi başlatılıyor. Model: {Model}", embeddingModel);

        return await Helpers.RetryHelper.ExecuteWithRetryAsync(
            operation: async () => await GenerateEmbeddingInternalAsync(text, model),
            maxRetries: 3,
            baseDelayMs: 2000,
            logger: _logger,
            operationName: $"GenerateEmbedding({embeddingModel})"
        );
    }

    /// <summary>
    /// Tekli embedding üretimi için internal metod
    /// </summary>
    private async Task<GeminiEmbeddingResponse> GenerateEmbeddingInternalAsync(string text, string? model = null)
    {

        try
        {
            if (_googleAI == null)
                throw new InvalidOperationException("Gemini AI servisi başlatılamadı.");

            var embeddingModel = model ?? Model.TextEmbedding;
            var geminiModel = _googleAI.GenerativeModel(model: embeddingModel);

            var response = await geminiModel.EmbedContent(text);

            _logger.LogInformation("Gemini embedding üretimi tamamlandı. Model: {Model}", embeddingModel);

            var embedding = response.Embedding?.Values?.ToArray() ?? Array.Empty<float>();

            return new GeminiEmbeddingResponse
            {
                Embedding = embedding,
                Model = embeddingModel,
                Success = true,
                Dimensions = embedding.Length
            };
        }
        catch (Exception ex)
        {

            _logger.LogError(ex, "Gemini embedding üretimi sırasında hata oluştu.");

            throw;
        }
    }

    /// <summary>
    /// Çoklu metin için embedding üretir
    /// </summary>
    public async Task<GeminiMultipleEmbeddingResponse> GenerateMultipleEmbeddingsAsync(List<string> texts, string? model = null)
    {
        var stopwatch = Stopwatch.StartNew();

        try
        {
            if (_googleAI == null)
                throw new InvalidOperationException("Gemini AI servisi başlatılamadı.");

            var embeddingModel = model ?? Model.TextEmbedding;
            var geminiModel = _googleAI.GenerativeModel(model: embeddingModel);

            var embeddings = new List<float[]>();

            var response = await geminiModel.EmbedContent(texts);
            if (response.Embeddings == null)
            {
                throw new Exception("Embedding üretilemedi. Retryable Process.");
            }

            embeddings.AddRange(response.Embeddings.Select(e => e.Values?.ToArray() ?? Array.Empty<float>()).ToList());

            stopwatch.Stop();

            _logger.LogInformation("Gemini çoklu embedding üretimi tamamlandı. Süre: {Duration}ms, Model: {Model}, Metin Sayısı: {Count}",
                stopwatch.ElapsedMilliseconds, embeddingModel, texts.Count);

            var dimensions = embeddings.FirstOrDefault()?.Length ?? 0;

            return new GeminiMultipleEmbeddingResponse
            {
                Embeddings = embeddings,
                Model = embeddingModel,
                Success = true,
                Dimensions = dimensions,
                TextCount = texts.Count,
                ProcessingTimeMs = stopwatch.ElapsedMilliseconds
            };
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Gemini çoklu embedding üretimi sırasında hata oluştu.");

            return new GeminiMultipleEmbeddingResponse
            {
                Success = false,
                ErrorMessage = ex.Message,
                TextCount = texts.Count,
                ProcessingTimeMs = stopwatch.ElapsedMilliseconds
            };
        }
    }

    /// <summary>
    /// Tekli metin için embedding üretir (retry mekanizması ile)
    /// </summary>
    /// <param name="text">Embedding üretilecek metin</param>
    /// <returns>Embedding vektörü</returns>
    public async Task<GeminiEmbeddingResponse> GetEmbeddingAsync(string text)
    {
        if (string.IsNullOrEmpty(text))
            throw new ArgumentException("Text cannot be null or empty", nameof(text));

        _logger.LogInformation("Getting embedding for single text");

        return await Helpers.RetryHelper.ExecuteWithRetryAsync(
            operation: async () => await GetEmbeddingInternalAsync(text),
            maxRetries: 3,
            baseDelayMs: 2000,
            logger: _logger,
            operationName: $"GetEmbedding({text.Length} chars)"
        );
    }

    /// <summary>
    /// Tekli metin için embedding üretimi için internal metod
    /// </summary>
    private async Task<GeminiEmbeddingResponse> GetEmbeddingInternalAsync(string text)
    {
        var stopwatch = Stopwatch.StartNew();
        try
        {
            var request = new Models.EmbedContentRequest
            {
                model = "models/" + Model.GeminiEmbedding,
                content = new Models.Content
                {
                    parts = new List<Models.Part>
                    {
                        new Models.Part { text = text }
                    }
                },
                outputDimensionality = 768,
                taskType = "SEMANTIC_SIMILARITY"
            };

            var jsonRequest = JsonSerializer.Serialize(request, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });

            var url = $"{_options.BaseUrl}/models/" + Model.GeminiEmbedding + $":embedContent?key={_options.ApiKey}";
            var httpContent = new StringContent(jsonRequest, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync(url, httpContent);
            var responseContent = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                stopwatch.Stop();
                _logger.LogError("API request failed with status {StatusCode}: {ResponseContent}",
                    response.StatusCode, responseContent);
                throw new HttpRequestException($"API request failed with status {response.StatusCode}: {responseContent}");
            }

            var embeddingResponse = JsonSerializer.Deserialize<Models.SingleEmbedResponse>(responseContent, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });

            if (embeddingResponse?.embedding?.values == null)
            {
                stopwatch.Stop();
                throw new InvalidOperationException("Response is null or empty. Retryable Process.");
            }

            // Normalize edilmiş vektörü oluştur
            double magnitude = Math.Sqrt(embeddingResponse.embedding.values.Sum(x => x * x));
            List<float> normalizedEmbedding = embeddingResponse.embedding.values
                .Select(x => (float)(x / magnitude))
                .ToList();

            var geminiEmbeddingResponse = new GeminiEmbeddingResponse
            {
                Embedding = normalizedEmbedding.ToArray(),
                Model = Model.GeminiEmbedding,
                Success = true,
                Dimensions = normalizedEmbedding.Count,
                ProcessingTimeMs = stopwatch.ElapsedMilliseconds
            };

            stopwatch.Stop();

            _logger.LogInformation("Successfully retrieved single embedding with {Dimensions} dimensions. Süre: {Duration}ms", normalizedEmbedding.Count, stopwatch.ElapsedMilliseconds);
            return geminiEmbeddingResponse;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Tekli embedding üretimi sırasında hata oluştu.");

            throw;
        }
    }

    /// <summary>
    /// Çoklu metin için embedding üretir (retry mekanizması ile)
    /// </summary>
    public async Task<GeminiMultipleEmbeddingResponse> GetEmbeddingsAsync(List<string> texts, CancellationToken cancellationToken)
    {
        if (texts == null || texts.Count == 0)
            throw new ArgumentException("Texts cannot be null or empty", nameof(texts));

        _logger.LogInformation("Getting embeddings for {Count} texts", texts.Count);

        return await Helpers.RetryHelper.ExecuteWithRetryAsync(
            operation: async () => await GetEmbeddingsInternalAsync(texts, cancellationToken),
            maxRetries: 5,
            baseDelayMs: 3000,
            logger: _logger,
            operationName: $"GetEmbeddings({texts.Count} texts)"
        );
    }

    /// <summary>
    /// Embedding üretimi için internal metod
    /// </summary>
    private async Task<GeminiMultipleEmbeddingResponse> GetEmbeddingsInternalAsync(List<string> texts, CancellationToken cancellationToken)
    {
        var stopwatch = Stopwatch.StartNew();
        try
        {

            var requests = new List<Models.EmbedContentRequest>();
            foreach (var text in texts)
            {

                requests.Add(new Models.EmbedContentRequest
                {
                    model = "models/" + Model.GeminiEmbedding,
                    content = new Models.Content
                    {
                        parts = new List<Models.Part>
                            {
                                new Models.Part { text = text }
                            }
                    },
                    outputDimensionality = 768,
                    taskType = "SEMANTIC_SIMILARITY"
                });
            }
            cancellationToken.ThrowIfCancellationRequested();
            var batchRequest = new BatchEmbedRequest
            {
                requests = requests
            };

            var jsonRequest = JsonSerializer.Serialize(batchRequest, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });

            var url = $"{_options.BaseUrl}/models/" + Model.GeminiEmbedding + $":batchEmbedContents?key={_options.ApiKey}";
            var httpContent = new StringContent(jsonRequest, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync(url, httpContent, cancellationToken);
            var responseContent = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                stopwatch.Stop();
                _logger.LogError("API request failed with status {StatusCode}: {ResponseContent}",
                    response.StatusCode, responseContent);
                throw new HttpRequestException($"API request failed with status {response.StatusCode}: {responseContent}");
            }

            var embeddingResponse = JsonSerializer.Deserialize<BatchEmbedResponse>(responseContent, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });
            if (embeddingResponse?.embeddings?.Count == 0)
            {
                stopwatch.Stop();
                throw new InvalidOperationException("Response is null or empty. Retryable Process.");
            }
            cancellationToken.ThrowIfCancellationRequested();
            var results = new List<float[]>();
            foreach (var embedding in embeddingResponse.embeddings)
            {
                double magnitude = Math.Sqrt(embedding.values.Sum(x => x * x));

                // Normalize edilmiş vektörü oluştur
                float[] normalizedEmbedding = embedding.values.ToArray()
                    .Select(x => (float)(x / magnitude))
                    .ToArray();

                results.Add(normalizedEmbedding);
            }
            var geminiMultipleEmbeddingResponse = new GeminiMultipleEmbeddingResponse
            {
                Embeddings = results,
                Model = Model.GeminiEmbedding,
                Success = true,
                Dimensions = results.FirstOrDefault()?.Length ?? 0,
                TextCount = texts.Count,
                ProcessingTimeMs = stopwatch.ElapsedMilliseconds
            };

            stopwatch.Stop();

            _logger.LogInformation("Successfully retrieved {Count} embeddings. Süre: {Duration}ms", results.Count, stopwatch.ElapsedMilliseconds);
            return geminiMultipleEmbeddingResponse;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Batch embedding üretimi sırasında hata oluştu.");

            throw;
        }
    }

}