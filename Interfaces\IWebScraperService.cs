using System.Collections.Generic;
using System.Threading.Tasks;
using TranslationAgentServer.Models;

namespace TranslationAgentServer.Interfaces
{
    /// <summary>
    /// Playwright kütüphanesi kullanarak web sitelerinden içerik çıkarmak için servis arayüzü
    /// </summary>
    public interface IWebScraperService
    {
        /// <summary>
        /// Belirtilen URL'den içerik çıkarır
        /// </summary>
        /// <param name="url">İçerik çıkarılacak web sitesi URL'si</param>
        /// <returns>Çıkarılan içerik</returns>
        Task<string> ScrapeContentAsync(string url);

        /// <summary>
        /// Belirtilen URL'den başlayarak belirli derinlikte ve sayfa sayısında içerik çıkarır
        /// </summary>
        /// <param name="url">Başlangıç URL'si</param>
        /// <param name="maxDepth">Maksimum derinlik seviyesi</param>
        /// <param name="maxPages"><PERSON><PERSON><PERSON><PERSON> sayfa sayısı</param>
        /// <param name="urlFilter">URL filtreleme fonksiyonu (isteğe bağlı)</param>
        /// <param name="contentFilter">İçerik filtreleme fonksiyonu (isteğe bağlı)</param>
        /// <returns>URL ve içerik eşleştirmelerini içeren sözlük</returns>
        Task<List<WebScrapCreateDto>> ScrapeWebsiteAsync(string url, string selector = null, int maxDepth = 1, int maxPages = 10, Func<string, bool>? urlFilter = null, Func<string, bool>? contentFilter = null, Process process = null);

        /// <summary>
        /// Belirtilen URL'den belirli HTML elementlerini çıkarır
        /// </summary>
        /// <param name="url">İçerik çıkarılacak web sitesi URL'si</param>
        /// <param name="selector">CSS veya XPath seçici</param>
        /// <returns>Seçilen elementlerin içeriği</returns>
        Task<List<string>> ScrapeElementsAsync(string url, string selector);

        /// <summary>
        /// Playwright tarayıcısını başlatır
        /// </summary>
        /// <returns>Başlatma işlemi sonucu</returns>
        Task<bool> InitializeBrowserAsync();

        /// <summary>
        /// Playwright tarayıcısını kapatır
        /// </summary>
        /// <returns>Kapatma işlemi sonucu</returns>
        Task<bool> CloseBrowserAsync();

        // CRUD Operations
        /// <summary>
        /// Creates a new web scraping record in the database
        /// </summary>
        /// <param name="webScrapDto">Web scraping data transfer object</param>
        /// <param name="projectId">Project identifier</param>
        /// <returns>Created web scraping record</returns>
        Task<WebScrap> CreateWebScrapAsync(WebScrapCreateDto webScrapDto, int projectId);

        /// <summary>
        /// Retrieves a web scraping record by its ID
        /// </summary>
        /// <param name="id">Web scraping record identifier</param>
        /// <param name="projectId">Project identifier</param>
        /// <returns>Web scraping record if found, null otherwise</returns>
        Task<WebScrap?> GetWebScrapByIdAsync(long id, int projectId);

        /// <summary>
        /// Retrieves a paginated list of web scraping records
        /// </summary>
        /// <param name="skip">Number of records to skip</param>
        /// <param name="take">Number of records to take</param>
        /// <param name="projectId">Project identifier</param>
        /// <returns>List of web scraping records</returns>
        Task<List<WebScrap>> GetAllWebScrapsAsync(int skip = 0, int take = 100, int projectId = 0);

        /// <summary>
        /// Retrieves web scraping records by URL
        /// </summary>
        /// <param name="url">URL to search for</param>
        /// <param name="projectId">Project identifier</param>
        /// <returns>List of matching web scraping records</returns>
        Task<List<WebScrap>> GetWebScrapsByUrlAsync(string url, int projectId);

        /// <summary>
        /// Updates an existing web scraping record
        /// </summary>
        /// <param name="id">Record identifier to update</param>
        /// <param name="webScrapDto">Updated web scraping data</param>
        /// <param name="projectId">Project identifier</param>
        /// <returns>Updated web scraping record if found, null otherwise</returns>
        Task<WebScrap?> UpdateWebScrapAsync(long id, WebScrapUpdateDto webScrapDto, int projectId);

        /// <summary>
        /// Deletes a web scraping record
        /// </summary>
        /// <param name="id">Record identifier to delete</param>
        /// <param name="projectId">Project identifier</param>
        /// <returns>True if deletion was successful, false otherwise</returns>
        Task<bool> DeleteWebScrapAsync(long id, int projectId);

        /// <summary>
        /// Creates multiple web scraping records in batch
        /// </summary>
        /// <param name="webscraps">List of web scraping data transfer objects</param>
        /// <param name="projectId">Project identifier</param>
        /// <returns>True if batch creation was successful, false otherwise</returns>
        Task<bool> CreateWebScrapsAsync(List<WebScrapCreateDto> webscraps, int projectId);

        /// <summary>
        /// Retrieves statistics for web scraping operations
        /// </summary>
        /// <param name="projectId">Project identifier</param>
        /// <returns>Web scraping statistics</returns>
        Task<WebScrapStatistics> GetWebScrapStatisticsAsync(int projectId);

        /// <summary>
        /// Performs web scraping on a URL and saves the result
        /// </summary>
        /// <param name="url">URL to scrape</param>
        /// <param name="projectId">Project identifier</param>
        /// <returns>Saved web scraping record</returns>
        Task<WebScrap> ScrapeAndSaveAsync(string url, int projectId);
    }
}