using System.Threading;
using System.Threading.Tasks;
using TranslationAgentServer.Models;

namespace TranslationAgentServer.Services.ProcessHandlers
{
    /// <summary>
    /// Tüm işlem görevi handler'lar<PERSON> i<PERSON>in temel arayüz.
    /// </summary>
    public interface IProcessTaskHandler
    {
        /// <summary>
        /// Belirli bir işlem görevini yürütür.
        /// </summary>
        /// <param name="process">Yürütülecek işlem.</param>
        /// <param name="cancellationToken">İptal token'ı.</param>
        /// <returns>Task</returns>
        Task ExecuteAsync(Process process, CancellationToken cancellationToken);
    }
}