using Microsoft.AspNetCore.Mvc;
using TranslationAgentServer.Interfaces;
using TranslationAgentServer.Models;
using TranslationAgentServer.Models.Filtering;
using System.Text.Json;

namespace TranslationAgentServer.Controllers;

/// <summary>
/// İşlem yönetimi API kontrolcüsü
/// Proje işlemlerinin CRUD operasyonları ve durum yönetimi için endpoint'ler sağlar
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class ProcessController : ControllerBase
{
    private readonly IProcessService _processService;
    private readonly IProjectService _projectService;
    private readonly ILogger<ProcessController> _logger;

    public ProcessController(IProcessService processService, IProjectService projectService, ILogger<ProcessController> logger)
    {
        _processService = processService;
        _projectService = projectService;
        _logger = logger;
    }



    /// <summary>
    /// Belirtilen ID'ye sahip işlemi getirir
    /// </summary>
    /// <param name="id"><PERSON>ş<PERSON> kimliği</param>
    /// <returns>İşlem bilgileri</returns>
    [HttpGet("{id}")]
    public async Task<ActionResult<Process>> GetProcess(Guid id)
    {
        try
        {
            _logger.LogInformation("İşlem isteniyor: {ProcessId}", id);

            var process = await _processService.GetProcessByIdAsync(id);
            if (process == null)
            {
                return NotFound(new { message = "İşlem bulunamadı" });
            }

            return Ok(process);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "İşlem getirilirken hata oluştu: {ProcessId}", id);
            return StatusCode(500, new { message = "İşlem getirilirken bir hata oluştu", error = ex.Message });
        }
    }

    /// <summary>
    /// Yeni işlem oluşturur ve başlatır
    /// </summary>
    /// <param name="processDto">İşlem bilgileri</param>
    /// <returns>Oluşturulan ve başlatılan işlem</returns>
    [HttpPost]
    public async Task<ActionResult<Process>> CreateAndStartProcess([FromBody] ProcessCreateDto processDto)
    {
        try
        {
            _logger.LogInformation("Yeni işlem oluşturuluyor: ProjectId={ProjectId}, TaskType={TaskType}",
                processDto.ProjectId, processDto.TaskType);

            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var project = await _projectService.GetProjectByIdAsync(processDto.ProjectId);
            if (project == null)
            {
                return NotFound(new { message = "İşlemin ilişkilendirileceği proje bulunamadı" });
            }

            var process = await _processService.CreateAndStartProcessAsync(processDto);
            return CreatedAtAction(nameof(GetProcess), new { id = process.Id }, process);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "İşlem oluşturulurken hata oluştu: ProjectId={ProjectId}, TaskType={TaskType}",
                processDto.ProjectId, processDto.TaskType);
            return StatusCode(500, new { message = "İşlem oluşturulurken bir hata oluştu", error = ex.Message });
        }
    }

    /// <summary>
    /// Mevcut işlemi günceller
    /// </summary>
    /// <param name="id">İşlem kimliği</param>
    /// <param name="processUpdateDto">Güncellenecek işlem bilgileri</param>
    /// <returns>Güncellenmiş işlem</returns>
    [HttpPut("{id}")]
    public async Task<ActionResult<Process>> UpdateProcess(Guid id, [FromBody] ProcessUpdateDto processUpdateDto)
    {
        try
        {
            _logger.LogInformation("İşlem güncelleniyor: {ProcessId}", id);

            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var process = await _processService.UpdateProcessWithIDAsync(id, processUpdateDto);
            if (process == null)
            {
                return NotFound(new { message = "İşlem bulunamadı" });
            }

            return Ok(process);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "İşlem güncellenirken hata oluştu: {ProcessId}", id);
            return StatusCode(500, new { message = "İşlem güncellenirken bir hata oluştu", error = ex.Message });
        }
    }

    /// <summary>
    /// İşlemi siler
    /// </summary>
    /// <param name="id">İşlem kimliği</param>
    /// <returns>Silme işleminin sonucu</returns>
    [HttpDelete("{id}")]
    public async Task<ActionResult> DeleteProcess(Guid id)
    {
        try
        {
            _logger.LogInformation("İşlem siliniyor: {ProcessId}", id);

            var result = await _processService.DeleteProcessAsync(id);
            if (!result)
            {
                return NotFound(new { message = "İşlem bulunamadı" });
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "İşlem silinirken hata oluştu: {ProcessId}", id);
            return StatusCode(500, new { message = "İşlem silinirken bir hata oluştu", error = ex.Message });
        }
    }

    /// <summary>
    /// İşlemi iptal eder
    /// </summary>
    /// <param name="id">İşlem kimliği</param>
    /// <returns>İptal işleminin sonucu</returns>
    [HttpPost("{id}/cancel")]
    public async Task<ActionResult> CancelProcess(Guid id)
    {
        try
        {
            _logger.LogInformation("İşlem iptal ediliyor: {ProcessId}", id);

            var result = await _processService.CancelProcessAsync(id);
            if (!result)
            {
                return NotFound(new { message = "İşlem bulunamadı" });
            }

            return Ok(new { message = "İşlem başarıyla iptal edildi" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "İşlem iptal edilirken hata oluştu: {ProcessId}", id);
            return StatusCode(500, new { message = "İşlem iptal edilirken bir hata oluştu", error = ex.Message });
        }
    }

    /// <summary>
    /// Gelişmiş filtreleme ile işlemleri getirir
    /// </summary>
    /// <param name="projectId">Proje kimliği</param>
    /// <param name="filterQuery">Filtreleme sorgusu</param>
    /// <returns>Filtrelenmiş işlem listesi</returns>
    [HttpPost("project/{projectId}/filter")]
    public async Task<ActionResult<IEnumerable<Process>>> GetProcessesWithFilter(int projectId, [FromBody] ProcessFilterQuery filterQuery)
    {
        try
        {
            var project = await _projectService.GetProjectByIdAsync(projectId);
            if (project == null)
            {
                return NotFound(new { message = "Proje bulunamadı" });
            }

            var processes = await _processService.GetProcessesByProjectIdAsync(projectId, filterQuery);
            return Ok(processes);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Filtreleme ile işlemler getirilirken hata oluştu: ProjectId={ProjectId}", projectId);
            return StatusCode(500, new { message = "Filtreleme ile işlemler getirilirken bir hata oluştu", error = ex.Message });
        }
    }


}