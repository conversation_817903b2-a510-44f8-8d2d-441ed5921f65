using TranslationAgentServer.Models;

namespace TranslationAgentServer.Interfaces
{
    /// <summary>
    /// Placeholder'ları tespit etme, işleme ve yönetme işlemlerini yönetir.
    /// </summary>
    public interface IPlaceholderService
    {
        /// <summary>
        /// Belirtilen projeyle ili<PERSON>li Google E-Tablosu'ndan metinleri alır ve placeholder'ları gruplar.
        /// </summary>
        /// <param name="project">İşlem yapılacak proje.</param>
        /// <param name="customPatterns">Kullanıcı tarafından tanımlanan ve başlangıç/bitiş ayıraçlarını belirten özel kalıplar.</param>
        /// <returns>Tespit edilen ve gruplanan placeholder'ları içeren bir sözlük.</returns>
        Task<Dictionary<string, List<string>>> DetectPlaceholdersAsync(Project project, List<CustomPatternDto>? customPatterns = null);

        /// <summary>
        /// Onaylanmış placeholder gruplar<PERSON><PERSON><PERSON>, AI ile regex oluşturur ve veritabanına kaydeder.
        /// </summary>
        /// <param name="projectId">Proje kimliği.</param>
        /// <param name="approvedGroups">Onaylanmış ve gruplanmış placeholder'ları içeren sözlük.</param>
        /// <returns>Asenkron işlemi temsil eden bir görev.</returns>
        Task ProcessPlaceholdersAsync(int projectId, Dictionary<string, List<string>> approvedGroups);
    }
}