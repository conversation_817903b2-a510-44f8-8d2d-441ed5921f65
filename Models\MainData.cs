using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;



namespace TranslationAgentServer.Models;

/// <summary>
/// Main tablosu için model sınıfı
/// </summary>
[Table("main")]
public class MainData
{
    /// <summary>
    /// Benz<PERSON>iz kimlik
    /// </summary>
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    [Column("id")]
    public int Id { get; set; }

    /// <summary>
    /// Oluşturulma tarihi
    /// </summary>
    [Column("created_at")]
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Veri ismi
    /// </summary>
    [Column("name")]
    public string? Name { get; set; }

    /// <summary>
    /// Veri değeri
    /// </summary>
    [Column("value")]
    public string? Value { get; set; }

    public override bool Equals(object? obj)
    {
        return obj is MainData other && Id == other.Id;
    }

    public override int GetHashCode()
    {
        return Id.GetHashCode();
    }
}