using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using TranslationAgentServer.Data;
using TranslationAgentServer.Interfaces;
using TranslationAgentServer.Models;

namespace TranslationAgentServer.Services.ProcessHandlers
{
    /// <summary>
    /// Web sitesi kazıma görevini işler.
    /// </summary>
    public class WebScrapingTaskHandler : ProcessHandlerBase
    {
        public WebScrapingTaskHandler(
            IDatabaseService databaseService,
            IGoogleSheetsService googleSheetsService,
            IProjectService projectService,
            ITextService textService,
            ITermService termService,
            IContextService contextService,
            IGeminiService geminiService,
            IWebScraperService webScraperService,
            ILogger<WebScrapingTaskHandler> logger,
            IProcessUpdateService processUpdateService) : base(
                databaseService,
                googleSheetsService,
                projectService,
                textService,
                termService,
                contextService,
                geminiService,
                webScraperService,
                logger,
                processUpdateService)
        {
        }

        /// <summary>
        /// Web sitesi kazıma task'ını çalıştırır
        /// </summary>
        /// <summary>
        /// Web scraping işlemini gerçekleştirir (AI response parse edilmez, mevcut DTO ile devam edilir).
        /// </summary>
        private async Task ExecuteWebScrapingTask(Process process, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Web sitesi kazıma task'ı çalıştırılıyor: {ProcessId}", process.Id);
            try
            {
                process.Progress = 0;
                process.Result = "Web sitesi kazıma başlıyor...";
                await UpdateProcess(process);

                var url = process.Settings?.RootElement.TryGetProperty("url", out var urlProperty) == true ? urlProperty.GetString() ?? null : null;
                var selector = process.Settings?.RootElement.TryGetProperty("selector", out var selectorProperty) == true ? selectorProperty.GetString() ?? null : null;
                var maxDepth = process.Settings?.RootElement.TryGetProperty("maxDepth", out var maxDepthProperty) == true ? maxDepthProperty.GetInt32() : 1;
                var maxPages = process.Settings?.RootElement.TryGetProperty("maxPages", out var maxPagesProperty) == true ? maxPagesProperty.GetInt32() : 10;
                var urlPattern = process.Settings?.RootElement.TryGetProperty("urlPattern", out var urlPatternProperty) == true ? urlPatternProperty.GetString() ?? null : null;
                var contentPattern = process.Settings?.RootElement.TryGetProperty("contentPattern", out var contentPatternProperty) == true ? contentPatternProperty.GetString() ?? null : null;
                if (string.IsNullOrEmpty(url))
                {
                    throw new InvalidOperationException("URL belirtilmedi");
                }

                var results = await _webScraperService.ScrapeWebsiteAsync(url, selector, maxDepth, maxPages, urlPattern == null ? null : (u) => Regex.IsMatch(u, urlPattern), contentPattern == null ? null : (c) => Regex.IsMatch(c, contentPattern), process);

                if (results == null || results.Count == 0)
                {
                    throw new InvalidOperationException("Web sitesi kazımda veri bulunamadı");
                }

                cancellationToken.ThrowIfCancellationRequested();

                process.Progress = 80;
                process.Result = "Web sitesi kazımda veriler veritabanına kaydediliyor...";
                await UpdateProcess(process);

                // Toplu veri oluşturma işlemi
                var createResult = await _webScraperService.CreateWebScrapsAsync(results, process.ProjectId);
                if (createResult == false)
                {
                    throw new InvalidOperationException("Web sitesi kazımda veriler veritabanına kaydedilemedi");
                }

                process.Progress = 100;
                process.Result = "Web sitesi kazıma tamamlandı";
                process.Status = (int)ProcessStatus.Completed;
                process.CompletedAt = DateTime.UtcNow;
                await UpdateProcess(process);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Web sitesi kazımda hata meydana geldi.");
                throw;
            }
        }

        /// <summary>
        /// Web sitesi kazıma görevini yürütür.
        /// </summary>
        public override async Task ExecuteAsync(Process process, CancellationToken cancellationToken)
        {
            await ExecuteWebScrapingTask(process, cancellationToken);
        }
    }
}