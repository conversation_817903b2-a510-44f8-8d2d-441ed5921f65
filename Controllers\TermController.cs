using Microsoft.AspNetCore.Mvc;
using TranslationAgentServer.Interfaces;
using TranslationAgentServer.Models;
using TranslationAgentServer.Models.Filtering;
using System.Text.Json;

namespace TranslationAgentServer.Controllers
{
    /// <summary>
    /// Terim yönetimi için RESTful API controller'ı.
    /// yeni_schema.terms tablosu ile CRUD ve arama işlemlerini sağlar.
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class TermController : ControllerBase
    {
        private readonly ITermService _termService;
        private readonly ILogger<TermController> _logger;

        public TermController(ITermService termService, ILogger<TermController> logger)
        {
            _termService = termService;
            _logger = logger;
        }

        /// <summary>
        /// Belirtilen projedeki tüm terimleri sayfalanmış olarak getirir
        /// </summary>
        /// <param name="projectID"><PERSON><PERSON> kim<PERSON></param>
        /// <returns>Sayfalanmış terim listesi</returns>
        [HttpGet("{projectID}")]
        public async Task<ActionResult<List<Term>>> GetAllTerms(int projectID)
        {
            try
            {
                var result = await _termService.GetAllTermsAsync(projectID);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Tüm terimler getirilirken hata oluştu. Proje: {ProjectID}", projectID);
                return StatusCode(500, new { message = "Terimler getirilirken bir hata oluştu", error = ex.Message });
            }
        }

        /// <summary>
        /// Gelişmiş filtreleme ile terimleri getirir
        /// </summary>
        /// <param name="projectID">Proje kimliği</param>
        /// <param name="filterQuery">Filtreleme sorgusu</param>
        /// <returns>Filtrelenmiş terim listesi</returns>
        [HttpPost("{projectID}/filter")]
        public async Task<ActionResult<List<Term>>> GetTermsWithFilter(int projectID, [FromBody] TermFilterQuery filterQuery)
        {
            try
            {
                var terms = await _termService.GetAllTermsAsync(projectID, filterQuery);
                return Ok(terms);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Filtreleme ile terimler getirilirken hata oluştu. ProjectID: {ProjectID}", projectID);
                return StatusCode(500, new { message = "Filtreleme ile terimler getirilirken bir hata oluştu", error = ex.Message });
            }
        }

        /// <summary>
        /// ID'ye göre terim ve çeviri geçmişini getirir
        /// </summary>
        /// <param name="projectID">Proje kimliği</param>
        /// <param name="id">Terim ID'si</param>
        /// <returns>Bulunan terim ve çeviri geçmişi</returns>
        [HttpGet("{projectID}/{id}")]
        public async Task<ActionResult<TermTranslation>> GetTermById(int projectID, int id)
        {
            try
            {
                var termWithTranslation = await _termService.GetTermTranslationsAsync(id, projectID);
                if (termWithTranslation == null)
                {
                    return NotFound(new { message = $"ID {id} ile terim bulunamadı" });
                }

                return Ok(termWithTranslation);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Terim ve çeviri geçmişi getirilirken hata oluştu. ID: {Id}, Proje: {ProjectID}", id, projectID);
                return StatusCode(500, new { message = "Terim getirilirken bir hata oluştu", error = ex.Message });
            }
        }

        /// <summary>
        /// Terim istatistiklerini getirir
        /// </summary>
        /// <param name="projectID">Proje kimliği</param>
        /// <returns>İstatistik bilgileri</returns>
        [HttpGet("{projectID}/statistics")]
        public async Task<ActionResult<TermStatistics>> GetTermStatistics(int projectID)
        {
            try
            {
                var statistics = await _termService.GetTermStatisticsAsync(projectID);
                return Ok(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Terim istatistikleri alınırken hata oluştu. Proje: {ProjectID}", projectID);
                return StatusCode(500, new { message = "İstatistikler alınırken bir hata oluştu", error = ex.Message });
            }
        }

        /// <summary>
        /// Yeni terim ekler
        /// </summary>
        /// <param name="projectID">projectID</param>
        /// <param name="termDto">Oluşturulacak terim bilgileri</param>
        /// <returns>Oluşturulan terim</returns>
        [HttpPost("{projectID}")]
        public async Task<ActionResult<Term>> AddTerm(int projectID, [FromBody] TermCreateDto termDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var termCreateDto = await _termService.CreateTermAsync(termDto, projectID);
                var term = await _termService.AddTermAsync(termCreateDto, projectID);
                return CreatedAtAction(nameof(GetTermById), new { projectID, id = term.Id }, term);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Terim oluşturulurken hata oluştu. En: {En}, Proje: {ProjectID}", termDto.En, projectID);
                return StatusCode(500, new { message = "Terim oluşturulurken bir hata oluştu", error = ex.Message });
            }
        }

        /// <summary>
        /// Toplu terim ekler
        /// </summary>
        /// <param name="projectID">Proje kimliği</param>
        /// <param name="termCreateDtos">Oluşturulacak terim listesi</param>
        /// <returns>Oluşturma işlemi sonucu</returns>
        [HttpPost("{projectID}/bulk")]
        public async Task<ActionResult> AddTerms(int projectID, [FromBody] List<TermCreateDto> termCreateDtos)
        {
            try
            {
                if (termCreateDtos == null || !termCreateDtos.Any())
                {
                    return BadRequest(new { message = "Terim listesi boş olamaz" });
                }

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var terms = await _termService.CreateTermsAsync(termCreateDtos, projectID, CancellationToken.None);
                var success = await _termService.AddTermsAsync(terms, projectID, CancellationToken.None);
                if (success > 0)
                {
                    return Ok(new { message = $"{termCreateDtos.Count} terim başarıyla oluşturuldu" });
                }
                else
                {
                    return StatusCode(500, new { message = "Terimler eklenirken bir hata oluştu" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Toplu terim eklenirken hata oluştu. Sayı: {Count}, Proje: {ProjectID}", termCreateDtos?.Count ?? 0, projectID);
                return StatusCode(500, new { message = "Terimler eklenirken bir hata oluştu", error = ex.Message });
            }
        }


        /// <summary>
        /// Terimi günceller
        /// </summary>
        /// <param name="projectID">Proje kimliği</param>
        /// <param name="id">Güncellenecek terim ID'si</param>
        /// <param name="termDto">Güncelleme bilgileri</param>
        /// <returns>Güncellenmiş terim</returns>
        [HttpPut("{projectID}/{id}")]
        public async Task<ActionResult<Term>> UpdateTerm(int projectID, int id, [FromBody] TermUpdateDto termDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var term = await _termService.UpdateTermAsync(id, termDto, projectID);
                if (term == null)
                {
                    return NotFound(new { message = $"ID {id} ile terim bulunamadı" });
                }

                return Ok(term);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Terim güncellenirken hata oluştu. ID: {Id}, Proje: {ProjectID}", id, projectID);
                return StatusCode(500, new { message = "Terim güncellenirken bir hata oluştu", error = ex.Message });
            }
        }

        /// <summary>
        /// Toplu terim günceller
        /// </summary>
        /// <param name="projectID">Proje kimliği</param>
        /// <param name="termUpdates">Güncellenecek terim listesi</param>
        /// <returns>Güncellenmiş terim listesi</returns>
        [HttpPut("{projectID}/bulk")]
        public async Task<ActionResult<List<Term>>> UpdateTerms(int projectID, [FromBody] List<TermUpdateRequest> termUpdates)
        {
            try
            {
                if (termUpdates == null || !termUpdates.Any())
                {
                    return BadRequest(new { message = "Güncelleme listesi boş olamaz" });
                }

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var updates = termUpdates.Select(x => (x.Id, x.UpdateDto)).ToList();
                var updatedTerms = await _termService.UpdateTermsAsync(updates, projectID, CancellationToken.None);
                return Ok(updatedTerms);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Toplu terim güncellenirken hata oluştu. Sayı: {Count}, Proje: {ProjectID}", termUpdates?.Count ?? 0, projectID);
                return StatusCode(500, new { message = "Terimler güncellenirken bir hata oluştu", error = ex.Message });
            }
        }

        /// <summary>
        /// Terimi siler
        /// </summary>
        /// <param name="projectID">Proje kimliği</param>
        /// <param name="id">Silinecek terim ID'si</param>
        /// <returns>Silme işlemi sonucu</returns>
        [HttpDelete("{projectID}/{id}")]
        public async Task<ActionResult> DeleteTerm(int projectID, int id)
        {
            try
            {
                var success = await _termService.DeleteTermAsync(id, projectID);
                if (!success)
                {
                    return NotFound(new { message = $"ID {id} ile terim bulunamadı veya silinemedi" });
                }

                return Ok(new { message = "Terim başarıyla silindi" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Terim silinirken hata oluştu. ID: {Id}, Proje: {ProjectID}", id, projectID);
                return StatusCode(500, new { message = "Terim silinirken bir hata oluştu", error = ex.Message });
            }
        }

        /// <summary>
        /// Çoklu terim siler
        /// </summary>
        /// <param name="projectID">Proje kimliği</param>
        /// <param name="ids">Silinecek terim ID'leri</param>
        /// <returns>Silme işlemi sonucu</returns>
        [HttpDelete("{projectID}/bulk")]
        public async Task<ActionResult> DeleteMultipleTerms(int projectID, [FromBody] List<long> ids)
        {
            try
            {
                if (ids == null || !ids.Any())
                {
                    return BadRequest(new { message = "ID listesi boş olamaz" });
                }

                var deletedCount = await _termService.DeleteTermsAsync(ids, projectID);
                return Ok(new { message = $"{deletedCount}/{ids.Count} terim başarıyla silindi", deletedCount });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Çoklu terim silinirken hata oluştu. Sayı: {Count}, Proje: {ProjectID}", ids?.Count ?? 0, projectID);
                return StatusCode(500, new { message = "Terimler silinirken bir hata oluştu", error = ex.Message });
            }
        }
    }

    /// <summary>
    /// Toplu terim güncelleme için yardımcı sınıf
    /// </summary>
    public class TermUpdateRequest
    {
        /// <summary>
        /// Güncellenecek terim ID'si
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Güncelleme bilgileri
        /// </summary>
        public TermUpdateDto UpdateDto { get; set; } = null!;
    }
}