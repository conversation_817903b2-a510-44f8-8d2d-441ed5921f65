using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using NpgsqlTypes;
using Pgvector;


namespace TranslationAgentServer.Models;

/// <summary>
/// <PERSON>je <PERSON> texts tablosuna karşılık gelen metin modeli
/// Çeviri metinlerini ve NLP analiz sonuçlarını yönetir
/// </summary>

[Table("texts")]
public class Text
{
    /// <summary>
    /// Metnin benzersiz kimliği
    /// </summary>
    [Column("id")]
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int Id { get; set; }

    /// <summary>
    /// Google Sheets sayfa kimliği
    /// </summary>
    [Column("row_id")]
    public int RowID { get; set; }

    /// <summary>
    /// Namespace (alan adı)
    /// </summary>
    [Column("namespace")]
    public string? Namespace { get; set; }

    /// <summary>
    /// Metin anahtarı
    /// </summary>
    [Column("key")]
    public string? Key { get; set; }

    /// <summary>
    /// İngilizce metin
    /// </summary>
    [Column("en")]
    public string? En { get; set; }

    /// <summary>
    /// Türkçe metin
    /// </summary>
    [Column("tr")]
    public string? Tr { get; set; }

    /// <summary>
    /// Lemmatize edilmiş metin
    /// </summary>
    [Column("lemma")]
    public string? Lemma { get; set; }

    /// <summary>
    /// Not bilgisi
    /// </summary>
    [Column("note")]
    public string? Note { get; set; }

    /// <summary>
    /// Metin durumu (EN, TR, DUPE, NULL)
    /// </summary>
    [Column("status")]
    public int? Status { get; set; } = (int)TextStatus.EN;

    /// <summary>
    /// İngilizce metin için tsvector (arama vektörü)
    /// </summary>
    [Column("en_tsvector")]
    [DatabaseGenerated(DatabaseGeneratedOption.Computed)]
    public NpgsqlTsVector? EnTsvector { get; set; }


    /// <summary>
    /// Embedding vektörü
    /// </summary>
    [Column("embedding", TypeName = "vector(768)")]
    public Vector? Embedding { get; set; }

    /// <summary>
    /// Oluşturulma tarihi
    /// </summary>
    [Column("created_at")]
    public DateTime? CreatedAt { get; set; }

    /// <summary>
    /// Güncellenme tarihi
    /// </summary>
    [Column("updated_at")]
    public DateTime? UpdatedAt { get; set; }

    public override bool Equals(object? obj)
    {
        return obj is Text other && Id == other.Id;
    }

    public override int GetHashCode()
    {
        return Id.GetHashCode();
    }
}

/// <summary>
/// Metin durumu enum'u
/// </summary>
public enum TextStatus
{
    /// <summary>
    /// İngilizce
    /// </summary>
    EN,

    /// <summary>
    /// Türkçe
    /// </summary>
    TR,

    /// <summary>
    /// Tekrar eden
    /// </summary>
    DUPE,

    /// <summary>
    /// Boş
    /// </summary>
    NULL
}

/// <summary>
/// Metin oluşturma için DTO modeli
/// </summary>
public class TextCreateDto
{
    /// <summary>
    /// Google Sheets sayfa kimliği
    /// </summary>
    [Required(ErrorMessage = "Row ID zorunludur")]
    public int RowID { get; set; }

    /// <summary>
    /// Namespace (alan adı)
    /// </summary>
    public string? Namespace { get; set; }

    /// <summary>
    /// Metin anahtarı
    /// </summary>
    public string? Key { get; set; }

    /// <summary>
    /// İngilizce metin
    /// </summary>
    public string? En { get; set; }

    /// <summary>
    /// Türkçe metin
    /// </summary>
    public string? Tr { get; set; }

    /// <summary>
    /// Lemmatize edilmiş metin
    /// </summary>
    public string? Lemma { get; set; }

    /// <summary>
    /// Not bilgisi
    /// </summary>
    public string? Note { get; set; }

    /// <summary>
    /// Metin durumu
    /// </summary>
    public TextStatus? Status { get; set; }

    /// <summary>
    /// Embedding vektörü
    /// </summary>
    public Vector? Embedding { get; set; }
}

/// <summary>
/// Metin güncelleme için DTO modeli
/// </summary>
public class TextUpdateDto
{
    /// <summary>
    /// Namespace (alan adı)
    /// </summary>
    public string? Namespace { get; set; }

    /// <summary>
    /// Metin anahtarı
    /// </summary>
    public string? Key { get; set; }

    /// <summary>
    /// İngilizce metin
    /// </summary>
    public string? En { get; set; }

    /// <summary>
    /// Türkçe metin
    /// </summary>
    public string? Tr { get; set; }

    /// <summary>
    /// Lemmatize edilmiş metin
    /// </summary>
    public string? Lemma { get; set; }

    /// <summary>
    /// Not bilgisi
    /// </summary>
    public string? Note { get; set; }

    /// <summary>
    /// Metin durumu
    /// </summary>
    public TextStatus? Status { get; set; }

    /// <summary>
    /// Embedding vektörü
    /// </summary>
    public Vector? Embedding { get; set; }
}