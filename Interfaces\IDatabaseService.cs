using TranslationAgentServer.Data;
using TranslationAgentServer.Models;
using System;

namespace TranslationAgentServer.Interfaces;

/// <summary>
/// PostgreSQL veritabanı servisi için arayüz
/// Entity Framework Core ile veritabanı işlemlerini yönetir
/// </summary>
public interface IDatabaseService
{
    /// <summary>
    /// Contexti dönderir
    /// </summary>
    /// <returns>Context</returns>
    ApplicationDbContext GetContext();


    /// <summary>
    /// Proje için özel DbContext'i dönderir
    /// </summary>
    /// <returns>Context</returns>
    DynamicSchemaDbContext GetProjectContext(int projectId);

    /// <summary>
    /// Raw SQL sorgusu çalıştırır
    /// </summary>
    /// <param name="sql">SQL sorgusu</param>
    /// <param name="parameters">Parametreler</param>
    /// <returns>Sorgu sonucu</returns>
    Task<List<Dictionary<string, object>>> ExecuteRawSqlAsync(string sql, params object[] parameters);

    /// <summary>
    /// Veritabanı backup'ı alır
    /// </summary>
    /// <param name="backupPath">Backup dosya yolu</param>
    /// <returns>Backup sonucu</returns>
    Task<bool> CreateBackupAsync(string backupPath);

    /// <summary>
    /// Backup'tan veritabanını geri yükler
    /// </summary>
    /// <param name="backupPath">Backup dosya yolu</param>
    /// <returns>Geri yükleme sonucu</returns>
    Task<bool> RestoreBackupAsync(string backupPath);

    /// <summary>
    /// Belirtilen şema için dinamik DbContext oluşturur
    /// </summary>
    /// <param name="schemaName">Şema adı</param>
    /// <returns>Dinamik şema DbContext'i</returns>
    DynamicSchemaDbContext CreateDynamicContext(string schemaName);

    /// <summary>
    /// Belirtilen proje adını kullanarak pgrst (PostgREST) yapılandırmasına şema ekler.
    /// </summary>
    /// <param name="projectName">Eklenecek projenin adı.</param>
    /// <returns>İşlemin başarılı olup olmadığını belirten bir boolean değer.</returns>
    Task<bool> AddSchemaAsync(string projectName);

}