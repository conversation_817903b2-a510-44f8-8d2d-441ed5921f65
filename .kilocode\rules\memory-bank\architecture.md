# Mi<PERSON><PERSON>: TranslationAgentServer

## Sistem Mimarisi

Proje, **Servis <PERSON> (SOA)** temel alınarak geliştirilmiştir. Uygulama, işlevselliği modüler ve bağımsız servislere ayırır. Her servis kendi iş mantığından sorumludur, bu da geliştirmeyi, bakımı ve güncellemeyi kolaylaştırır.

### Katmanlar ve Sorumluluklar

- **Controllers/**: API'nin giriş noktalarıdır. HTTP isteklerini kabul eder, doğrular ve ilgili servis katmanını çağırır.
- **Services/**: Uygulamanın çekirdek iş mantığı burada bulunur. Veritabanı işlemleri, AI entegrasyonları, arka plan görevleri bu katmanda yürütülür.
- **Interfaces/**: Servisler ve diğer bileşenler için soyutlamalar sağlar. DI ile birlikte implementasyon detaylarını gizler.
- **Data/**: Veritabanı erişimi ve yönetimi. `ApplicationDbContext` genel verileri, `DynamicSchemaDbContext` ise proje bazlı izole verileri yönetir.
- **Models/**: Varlıklar, DTO'lar ve filtreleme modelleri.
- **Middleware/**: Özel middleware'ler (ör. AuthenticationMiddleware).
- **ProcessHandlers/**: Asenkron görev işleyicileri.

### Anahtar Bileşenler

- **Dependency Injection**: Tüm bağımlılıklar merkezi olarak yönetilir.
- **Repository Pattern**: Veritabanı işlemleri soyutlanır.
- **ProcessService & IProcessTaskHandler**: Uzun süren işlemler asenkron olarak yönetilir. Her görev, ilgili işleyici tarafından arka planda yürütülür.
- **Multi-Tenant Mimari**: Her proje için ayrı bir veritabanı şeması oluşturulur ve izole veri yönetimi sağlanır.

### Veri Akışı

1. Controller, HTTP isteğini alır.
2. İlgili Service çağrılır.
3. Service, gerekli DbContext ile veritabanı işlemini yapar.
4. Sonuç, Controller üzerinden istemciye döner.
5. Uzun süren işlemler ProcessService ile arka planda yürütülür.

### Asenkron Görev Yönetimi

- ProcessService, gelen isteğe göre ilgili IProcessTaskHandler'ı seçer.
- Görev arka planda Task.Run ile başlatılır.
- Her işleyici, kendi iş akışını yönetir (ör. metin gruplama, AI ile çeviri, sonuç kaydı).
- Paralel worker modeli ile büyük veri setleri hızlıca işlenir.

## Tasarım Desenleri

- Dependency Injection
- Repository Pattern
- Asenkron Task/Worker Modeli
- Multi-Tenant veri izolasyonu

## Sonuç

TranslationAgentServer, modüler, ölçeklenebilir ve bakımı kolay bir mimariye sahiptir. Asenkron görev yönetimi ve multi-tenant stratejisi, büyük ölçekli çeviri iş akışlarını verimli şekilde yönetmeyi sağlar.