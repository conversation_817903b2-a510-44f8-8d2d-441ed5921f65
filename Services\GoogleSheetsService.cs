using System.Text.Json;
using Google.Apis.Auth.OAuth2;
using Google.Apis.Drive.v3;
using Google.Apis.Services;
using Google.Apis.Sheets.v4;
using Google.Apis.Sheets.v4.Data;
using TranslationAgentServer.Interfaces;
using TranslationAgentServer.Models;

namespace TranslationAgentServer.Services;

/// <summary>
/// Google Sheets API işlemlerini gerçekleştiren servis
/// </summary>
public class GoogleSheetsService : IGoogleSheetsService
{
    private readonly ILogger<GoogleSheetsService> _logger;
    private SheetsService? _sheetsService;
    private DriveService? _driveService;
    private bool _isInitialized = false;

    /// <summary>
    /// GoogleSheetsService constructor
    /// </summary>
    /// <param name="logger">Logger instance</param>
    public GoogleSheetsService(ILogger<GoogleSheetsService> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Service account JSON ile Google Sheets servisini başlatır
    /// </summary>
    /// <param name="serviceAccountJson">Service account JSON içeriği</param>
    /// <returns>İşlem sonucu</returns>
    public async Task<GoogleSheetsApiResponse<bool>> InitializeServiceAsync(string serviceAccountJson)
    {
        try
        {
            _logger.LogInformation("Google Sheets servisi başlatılıyor...");

            // Service account credential oluştur
            var credential = GoogleCredential.FromJson(serviceAccountJson)
                .CreateScoped(new[] { SheetsService.Scope.Spreadsheets, DriveService.Scope.DriveReadonly });

            // Sheets service oluştur
            _sheetsService = new SheetsService(new BaseClientService.Initializer()
            {
                HttpClientInitializer = credential,
                ApplicationName = "TranslationAgentServer"
            });

            // Drive service oluştur (spreadsheet listesi için)
            _driveService = new DriveService(new BaseClientService.Initializer()
            {
                HttpClientInitializer = credential,
                ApplicationName = "TranslationAgentServer"
            });

            _isInitialized = true;
            _logger.LogInformation("Google Sheets servisi başarıyla başlatıldı.");

            return new GoogleSheetsApiResponse<bool>
            {
                Success = true,
                Data = true
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Google Sheets servisi başlatılırken hata oluştu.");
            return new GoogleSheetsApiResponse<bool>
            {
                Success = false,
                ErrorMessage = ex.Message
            };
        }
    }

    /// <summary>
    /// Kullanıcının erişebildiği tüm spreadsheet'leri listeler
    /// </summary>
    /// <returns>Spreadsheet listesi</returns>
    public async Task<GoogleSheetsApiResponse<List<SpreadsheetInfo>>> GetSpreadsheetsAsync()
    {
        try
        {
            if (!_isInitialized || _driveService == null)
            {
                return new GoogleSheetsApiResponse<List<SpreadsheetInfo>>
                {
                    Success = false,
                    ErrorMessage = "Servis başlatılmamış. Önce InitializeServiceAsync metodunu çağırın."
                };
            }

            var request = _driveService.Files.List();
            request.Q = "mimeType='application/vnd.google-apps.spreadsheet'";
            request.Fields = "files(id,name,createdTime,modifiedTime,webViewLink)";

            var response = await request.ExecuteAsync();
            var spreadsheets = new List<SpreadsheetInfo>();

            foreach (var file in response.Files)
            {
                spreadsheets.Add(new SpreadsheetInfo
                {
                    SpreadsheetId = file.Id,
                    Title = file.Name,
                    CreatedTime = file.CreatedTimeDateTimeOffset?.DateTime ?? DateTime.MinValue,
                    ModifiedTime = file.ModifiedTimeDateTimeOffset?.DateTime ?? DateTime.MinValue,
                    Url = file.WebViewLink ?? string.Empty
                });
            }

            return new GoogleSheetsApiResponse<List<SpreadsheetInfo>>
            {
                Success = true,
                Data = spreadsheets
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Spreadsheet'ler listelenirken hata oluştu.");
            return new GoogleSheetsApiResponse<List<SpreadsheetInfo>>
            {
                Success = false,
                ErrorMessage = ex.Message
            };
        }
    }

    /// <summary>
    /// Belirtilen spreadsheet'in tüm sheet'lerini listeler
    /// </summary>
    /// <param name="spreadsheetId">Spreadsheet ID'si</param>
    /// <returns>Sheet listesi</returns>
    public async Task<GoogleSheetsApiResponse<List<SheetInfo>>> GetSheetsAsync(string spreadsheetId)
    {
        try
        {
            if (!_isInitialized || _sheetsService == null)
            {
                return new GoogleSheetsApiResponse<List<SheetInfo>>
                {
                    Success = false,
                    ErrorMessage = "Servis başlatılmamış. Önce InitializeServiceAsync metodunu çağırın."
                };
            }

            var request = _sheetsService.Spreadsheets.Get(spreadsheetId);
            var response = await request.ExecuteAsync();
            var sheets = new List<SheetInfo>();

            foreach (var sheet in response.Sheets)
            {
                sheets.Add(new SheetInfo
                {
                    SheetId = sheet.Properties.SheetId ?? 0,
                    Title = sheet.Properties.Title,
                    Index = sheet.Properties.Index ?? 0,
                    RowCount = sheet.Properties.GridProperties?.RowCount ?? 0,
                    ColumnCount = sheet.Properties.GridProperties?.ColumnCount ?? 0
                });
            }

            return new GoogleSheetsApiResponse<List<SheetInfo>>
            {
                Success = true,
                Data = sheets
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Sheet'ler listelenirken hata oluştu. SpreadsheetId: {SpreadsheetId}", spreadsheetId);
            return new GoogleSheetsApiResponse<List<SheetInfo>>
            {
                Success = false,
                ErrorMessage = ex.Message
            };
        }
    }

    /// <summary>
    /// Belirtilen aralıktaki verileri getirir
    /// </summary>
    /// <param name="spreadsheetId">Spreadsheet ID'si</param>
    /// <param name="range">Veri aralığı (örn: Sheet1!A1:C10)</param>
    /// <returns>Aralık verisi</returns>
    public async Task<GoogleSheetsApiResponse<RangeData>> GetRangeDataAsync(string spreadsheetId, string range)
    {
        try
        {
            if (!_isInitialized || _sheetsService == null)
            {
                return new GoogleSheetsApiResponse<RangeData>
                {
                    Success = false,
                    ErrorMessage = "Servis başlatılmamış. Önce InitializeServiceAsync metodunu çağırın."
                };
            }

            var request = _sheetsService.Spreadsheets.Values.Get(spreadsheetId, range);
            var response = await request.ExecuteAsync();

            var rangeData = new RangeData
            {
                Range = response.Range,
                Values = response.Values?.Select(row => row.Select(cell => cell?.ToString() ?? string.Empty).ToList()).ToList() ?? new List<List<string>>()
            };

            return new GoogleSheetsApiResponse<RangeData>
            {
                Success = true,
                Data = rangeData
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Aralık verisi alınırken hata oluştu. SpreadsheetId: {SpreadsheetId}, Range: {Range}", spreadsheetId, range);
            return new GoogleSheetsApiResponse<RangeData>
            {
                Success = false,
                ErrorMessage = ex.Message
            };
        }
    }

    /// <summary>
    /// Belirtilen sheet'in tüm verilerini getirir
    /// </summary>
    /// <param name="spreadsheetId">Spreadsheet ID'si</param>
    /// <param name="sheetName">Sheet adı</param>
    /// <returns>Sheet verisi</returns>
    public async Task<GoogleSheetsApiResponse<List<List<string>>>> GetSheetDataAsync(string spreadsheetId, string sheetName)
    {
        try
        {
            if (!_isInitialized || _sheetsService == null)
            {
                return new GoogleSheetsApiResponse<List<List<string>>>
                {
                    Success = false,
                    ErrorMessage = "Servis başlatılmamış. Önce InitializeServiceAsync metodunu çağırın."
                };
            }

            var request = _sheetsService.Spreadsheets.Values.Get(spreadsheetId, sheetName);
            var response = await request.ExecuteAsync();

            var data = response.Values?.Select(row => row.Select(cell => cell?.ToString() ?? string.Empty).ToList()).ToList() ?? new List<List<string>>();

            return new GoogleSheetsApiResponse<List<List<string>>>
            {
                Success = true,
                Data = data
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Sheet verisi alınırken hata oluştu. SpreadsheetId: {SpreadsheetId}, SheetName: {SheetName}", spreadsheetId, sheetName);
            return new GoogleSheetsApiResponse<List<List<string>>>
            {
                Success = false,
                ErrorMessage = ex.Message
            };
        }
    }

    /// <summary>
    /// Belirtilen sheet'e yeni satır ekler
    /// </summary>
    /// <param name="spreadsheetId">Spreadsheet ID'si</param>
    /// <param name="sheetName">Sheet adı</param>
    /// <param name="rowData">Eklenecek satır verisi</param>
    /// <returns>İşlem sonucu</returns>
    public async Task<GoogleSheetsApiResponse<bool>> AddRowAsync(string spreadsheetId, string sheetName, SheetRowData rowData)
    {
        try
        {
            if (!_isInitialized || _sheetsService == null)
            {
                return new GoogleSheetsApiResponse<bool>
                {
                    Success = false,
                    ErrorMessage = "Servis başlatılmamış. Önce InitializeServiceAsync metodunu çağırın."
                };
            }

            var valueRange = new ValueRange
            {
                Values = new List<IList<object>> { rowData.Values.Cast<object>().ToList() }
            };

            var request = _sheetsService.Spreadsheets.Values.Append(valueRange, spreadsheetId, sheetName);
            request.ValueInputOption = SpreadsheetsResource.ValuesResource.AppendRequest.ValueInputOptionEnum.USERENTERED;
            request.InsertDataOption = SpreadsheetsResource.ValuesResource.AppendRequest.InsertDataOptionEnum.INSERTROWS;

            await request.ExecuteAsync();

            return new GoogleSheetsApiResponse<bool>
            {
                Success = true,
                Data = true
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Satır eklenirken hata oluştu. SpreadsheetId: {SpreadsheetId}, SheetName: {SheetName}", spreadsheetId, sheetName);
            return new GoogleSheetsApiResponse<bool>
            {
                Success = false,
                ErrorMessage = ex.Message
            };
        }
    }

    /// <summary>
    /// Belirtilen sheet'e birden fazla satır ekler
    /// </summary>
    /// <param name="spreadsheetId">Spreadsheet ID'si</param>
    /// <param name="sheetName">Sheet adı</param>
    /// <param name="rowsData">Eklenecek satırlar</param>
    /// <returns>İşlem sonucu</returns>
    public async Task<GoogleSheetsApiResponse<bool>> AddRowsAsync(string spreadsheetId, string sheetName, List<SheetRowData> rowsData)
    {
        try
        {
            if (!_isInitialized || _sheetsService == null)
            {
                return new GoogleSheetsApiResponse<bool>
                {
                    Success = false,
                    ErrorMessage = "Servis başlatılmamış. Önce InitializeServiceAsync metodunu çağırın."
                };
            }

            var valueRange = new ValueRange
            {
                Values = rowsData.Select(row => row.Values.Cast<object>().ToList() as IList<object>).ToList()
            };

            var request = _sheetsService.Spreadsheets.Values.Append(valueRange, spreadsheetId, sheetName);
            request.ValueInputOption = SpreadsheetsResource.ValuesResource.AppendRequest.ValueInputOptionEnum.USERENTERED;
            request.InsertDataOption = SpreadsheetsResource.ValuesResource.AppendRequest.InsertDataOptionEnum.INSERTROWS;

            await request.ExecuteAsync();

            return new GoogleSheetsApiResponse<bool>
            {
                Success = true,
                Data = true
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Satırlar eklenirken hata oluştu. SpreadsheetId: {SpreadsheetId}, SheetName: {SheetName}", spreadsheetId, sheetName);
            return new GoogleSheetsApiResponse<bool>
            {
                Success = false,
                ErrorMessage = ex.Message
            };
        }
    }

    /// <summary>
    /// Belirtilen satırı siler
    /// </summary>
    /// <param name="spreadsheetId">Spreadsheet ID'si</param>
    /// <param name="sheetId">Sheet ID'si</param>
    /// <param name="rowIndex">Silinecek satır indeksi (0-based)</param>
    /// <returns>İşlem sonucu</returns>
    public async Task<GoogleSheetsApiResponse<bool>> DeleteRowAsync(string spreadsheetId, int sheetId, int rowIndex)
    {
        return await DeleteRowsAsync(spreadsheetId, sheetId, rowIndex, rowIndex + 1);
    }

    /// <summary>
    /// Belirtilen satır aralığını siler
    /// </summary>
    /// <param name="spreadsheetId">Spreadsheet ID'si</param>
    /// <param name="sheetId">Sheet ID'si</param>
    /// <param name="startRowIndex">Başlangıç satır indeksi (0-based)</param>
    /// <param name="endRowIndex">Bitiş satır indeksi (0-based, dahil değil)</param>
    /// <returns>İşlem sonucu</returns>
    public async Task<GoogleSheetsApiResponse<bool>> DeleteRowsAsync(string spreadsheetId, int sheetId, int startRowIndex, int endRowIndex)
    {
        try
        {
            if (!_isInitialized || _sheetsService == null)
            {
                return new GoogleSheetsApiResponse<bool>
                {
                    Success = false,
                    ErrorMessage = "Servis başlatılmamış. Önce InitializeServiceAsync metodunu çağırın."
                };
            }

            var deleteRequest = new DeleteDimensionRequest
            {
                Range = new DimensionRange
                {
                    SheetId = sheetId,
                    Dimension = "ROWS",
                    StartIndex = startRowIndex,
                    EndIndex = endRowIndex
                }
            };

            var batchRequest = new BatchUpdateSpreadsheetRequest
            {
                Requests = new List<Request>
                {
                    new Request { DeleteDimension = deleteRequest }
                }
            };

            var request = _sheetsService.Spreadsheets.BatchUpdate(batchRequest, spreadsheetId);
            await request.ExecuteAsync();

            return new GoogleSheetsApiResponse<bool>
            {
                Success = true,
                Data = true
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Satırlar silinirken hata oluştu. SpreadsheetId: {SpreadsheetId}, SheetId: {SheetId}, StartRow: {StartRow}, EndRow: {EndRow}",
                spreadsheetId, sheetId, startRowIndex, endRowIndex);
            return new GoogleSheetsApiResponse<bool>
            {
                Success = false,
                ErrorMessage = ex.Message
            };
        }
    }

    /// <summary>
    /// Belirtilen hücreyi günceller
    /// </summary>
    /// <param name="spreadsheetId">Spreadsheet ID'si</param>
    /// <param name="range">Hücre aralığı (örn: Sheet1!A1)</param>
    /// <param name="value">Yeni değer</param>
    /// <returns>İşlem sonucu</returns>
    public async Task<GoogleSheetsApiResponse<bool>> UpdateCellAsync(string spreadsheetId, string range, string value)
    {
        return await UpdateRangeAsync(spreadsheetId, range, new List<List<string>> { new List<string> { value } });
    }

    /// <summary>
    /// Belirtilen aralığı günceller
    /// </summary>
    /// <param name="spreadsheetId">Spreadsheet ID'si</param>
    /// <param name="range">Aralık (örn: Sheet1!A1:C3)</param>
    /// <param name="values">Yeni değerler</param>
    /// <returns>İşlem sonucu</returns>
    public async Task<GoogleSheetsApiResponse<bool>> UpdateRangeAsync(string spreadsheetId, string range, List<List<string>> values)
    {
        try
        {
            if (!_isInitialized || _sheetsService == null)
            {
                return new GoogleSheetsApiResponse<bool>
                {
                    Success = false,
                    ErrorMessage = "Servis başlatılmamış. Önce InitializeServiceAsync metodunu çağırın."
                };
            }

            var valueRange = new ValueRange
            {
                Values = values.Select(row => row.Cast<object>().ToList() as IList<object>).ToList()
            };

            var request = _sheetsService.Spreadsheets.Values.Update(valueRange, spreadsheetId, range);
            request.ValueInputOption = SpreadsheetsResource.ValuesResource.UpdateRequest.ValueInputOptionEnum.USERENTERED;

            await request.ExecuteAsync();

            return new GoogleSheetsApiResponse<bool>
            {
                Success = true,
                Data = true
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Aralık güncellenirken hata oluştu. SpreadsheetId: {SpreadsheetId}, Range: {Range}", spreadsheetId, range);
            return new GoogleSheetsApiResponse<bool>
            {
                Success = false,
                ErrorMessage = ex.Message
            };
        }
    }

    /// <summary>
    /// Toplu güncelleme yapar
    /// </summary>
    /// <param name="spreadsheetId">Spreadsheet ID'si</param>
    /// <param name="batchRequest">Toplu güncelleme isteği</param>
    /// <returns>İşlem sonucu</returns>
    public async Task<GoogleSheetsApiResponse<bool>> BatchUpdateAsync(string spreadsheetId, BatchUpdateRequest batchRequest)
    {
        try
        {
            if (!_isInitialized || _sheetsService == null)
            {
                return new GoogleSheetsApiResponse<bool>
                {
                    Success = false,
                    ErrorMessage = "Servis başlatılmamış. Önce InitializeServiceAsync metodunu çağırın."
                };
            }

            var valueRanges = batchRequest.Updates.Select(update => new ValueRange
            {
                Range = update.Range,
                Values = update.Values.Select(row => row.Cast<object>().ToList() as IList<object>).ToList()
            }).ToList();

            var batchUpdateRequest = new BatchUpdateValuesRequest
            {
                ValueInputOption = "USER_ENTERED",
                Data = valueRanges
            };

            var request = _sheetsService.Spreadsheets.Values.BatchUpdate(batchUpdateRequest, spreadsheetId);
            await request.ExecuteAsync();

            return new GoogleSheetsApiResponse<bool>
            {
                Success = true,
                Data = true
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Toplu güncelleme yapılırken hata oluştu. SpreadsheetId: {SpreadsheetId}", spreadsheetId);
            return new GoogleSheetsApiResponse<bool>
            {
                Success = false,
                ErrorMessage = ex.Message
            };
        }
    }

    /// <summary>
    /// Yeni bir sheet oluşturur
    /// </summary>
    /// <param name="spreadsheetId">Spreadsheet ID'si</param>
    /// <param name="createRequest">Sheet oluşturma isteği</param>
    /// <returns>Oluşturulan sheet bilgisi</returns>
    public async Task<GoogleSheetsApiResponse<SheetInfo>> CreateSheetAsync(string spreadsheetId, CreateSheetRequest createRequest)
    {
        try
        {
            if (!_isInitialized || _sheetsService == null)
            {
                return new GoogleSheetsApiResponse<SheetInfo>
                {
                    Success = false,
                    ErrorMessage = "Servis başlatılmamış. Önce InitializeServiceAsync metodunu çağırın."
                };
            }

            var addSheetRequest = new AddSheetRequest
            {
                Properties = new SheetProperties
                {
                    Title = createRequest.Title,
                    GridProperties = new GridProperties
                    {
                        RowCount = createRequest.RowCount,
                        ColumnCount = createRequest.ColumnCount
                    }
                }
            };

            var batchRequest = new BatchUpdateSpreadsheetRequest
            {
                Requests = new List<Request>
                {
                    new Request { AddSheet = addSheetRequest }
                }
            };

            var request = _sheetsService.Spreadsheets.BatchUpdate(batchRequest, spreadsheetId);
            var response = await request.ExecuteAsync();

            var addedSheet = response.Replies[0].AddSheet;
            var sheetInfo = new SheetInfo
            {
                SheetId = addedSheet.Properties.SheetId ?? 0,
                Title = addedSheet.Properties.Title,
                Index = addedSheet.Properties.Index ?? 0,
                RowCount = addedSheet.Properties.GridProperties?.RowCount ?? 0,
                ColumnCount = addedSheet.Properties.GridProperties?.ColumnCount ?? 0
            };

            return new GoogleSheetsApiResponse<SheetInfo>
            {
                Success = true,
                Data = sheetInfo
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Sheet oluşturulurken hata oluştu. SpreadsheetId: {SpreadsheetId}, Title: {Title}", spreadsheetId, createRequest.Title);
            return new GoogleSheetsApiResponse<SheetInfo>
            {
                Success = false,
                ErrorMessage = ex.Message
            };
        }
    }

    /// <summary>
    /// Belirtilen sheet'i siler
    /// </summary>
    /// <param name="spreadsheetId">Spreadsheet ID'si</param>
    /// <param name="sheetId">Silinecek sheet ID'si</param>
    /// <returns>İşlem sonucu</returns>
    public async Task<GoogleSheetsApiResponse<bool>> DeleteSheetAsync(string spreadsheetId, int sheetId)
    {
        try
        {
            if (!_isInitialized || _sheetsService == null)
            {
                return new GoogleSheetsApiResponse<bool>
                {
                    Success = false,
                    ErrorMessage = "Servis başlatılmamış. Önce InitializeServiceAsync metodunu çağırın."
                };
            }

            var deleteRequest = new DeleteSheetRequest
            {
                SheetId = sheetId
            };

            var batchRequest = new BatchUpdateSpreadsheetRequest
            {
                Requests = new List<Request>
                {
                    new Request { DeleteSheet = deleteRequest }
                }
            };

            var request = _sheetsService.Spreadsheets.BatchUpdate(batchRequest, spreadsheetId);
            await request.ExecuteAsync();

            return new GoogleSheetsApiResponse<bool>
            {
                Success = true,
                Data = true
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Sheet silinirken hata oluştu. SpreadsheetId: {SpreadsheetId}, SheetId: {SheetId}", spreadsheetId, sheetId);
            return new GoogleSheetsApiResponse<bool>
            {
                Success = false,
                ErrorMessage = ex.Message
            };
        }
    }

    /// <summary>
    /// Yeni bir spreadsheet oluşturur
    /// </summary>
    /// <param name="title">Spreadsheet başlığı</param>
    /// <returns>Oluşturulan spreadsheet bilgisi</returns>
    public async Task<GoogleSheetsApiResponse<SpreadsheetInfo>> CreateSpreadsheetAsync(string title)
    {
        try
        {
            if (!_isInitialized || _sheetsService == null)
            {
                return new GoogleSheetsApiResponse<SpreadsheetInfo>
                {
                    Success = false,
                    ErrorMessage = "Servis başlatılmamış. Önce InitializeServiceAsync metodunu çağırın."
                };
            }

            var spreadsheet = new Spreadsheet
            {
                Properties = new SpreadsheetProperties
                {
                    Title = title
                }
            };

            var request = _sheetsService.Spreadsheets.Create(spreadsheet);
            var response = await request.ExecuteAsync();

            var spreadsheetInfo = new SpreadsheetInfo
            {
                SpreadsheetId = response.SpreadsheetId,
                Title = response.Properties.Title,
                Url = response.SpreadsheetUrl
            };

            return new GoogleSheetsApiResponse<SpreadsheetInfo>
            {
                Success = true,
                Data = spreadsheetInfo
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Spreadsheet oluşturulurken hata oluştu. Title: {Title}", title);
            return new GoogleSheetsApiResponse<SpreadsheetInfo>
            {
                Success = false,
                ErrorMessage = ex.Message
            };
        }
    }

    /// <summary>
    /// Belirtilen spreadsheet'in bilgilerini getirir
    /// </summary>
    /// <param name="spreadsheetId">Spreadsheet ID'si</param>
    /// <returns>Spreadsheet bilgisi</returns>
    public async Task<GoogleSheetsApiResponse<SpreadsheetInfo>> GetSpreadsheetInfoAsync(string spreadsheetId)
    {
        try
        {
            if (!_isInitialized || _sheetsService == null)
            {
                return new GoogleSheetsApiResponse<SpreadsheetInfo>
                {
                    Success = false,
                    ErrorMessage = "Servis başlatılmamış. Önce InitializeServiceAsync metodunu çağırın."
                };
            }

            var request = _sheetsService.Spreadsheets.Get(spreadsheetId);
            var response = await request.ExecuteAsync();

            var spreadsheetInfo = new SpreadsheetInfo
            {
                SpreadsheetId = response.SpreadsheetId,
                Title = response.Properties.Title,
                Url = response.SpreadsheetUrl
            };

            return new GoogleSheetsApiResponse<SpreadsheetInfo>
            {
                Success = true,
                Data = spreadsheetInfo
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Spreadsheet bilgisi alınırken hata oluştu. SpreadsheetId: {SpreadsheetId}", spreadsheetId);
            return new GoogleSheetsApiResponse<SpreadsheetInfo>
            {
                Success = false,
                ErrorMessage = ex.Message
            };
        }
    }

    /// <summary>
    /// Servisin başlatılıp başlatılmadığını kontrol eder
    /// </summary>
    /// <returns>Servis durumu</returns>
    public bool IsServiceInitialized()
    {
        return _isInitialized && _sheetsService != null && _driveService != null;
    }

    /// <summary>
    /// Belirtilen sheet'in ilk satırını beklenen şablonla karşılaştırır
    /// </summary>
    /// <param name="spreadsheetId">Spreadsheet ID'si</param>
    /// <param name="sheetName">Sheet adı</param>
    /// <param name="headerTemplate">Beklenen başlık şablonu (| ile ayrılmış)</param>
    /// <returns>Başlık kontrol sonucu</returns>
    public async Task<GoogleSheetsApiResponse<HeaderValidationResult>> ValidateHeaderAsync(string spreadsheetId, string sheetName, string headerTemplate)
    {
        try
        {
            if (_sheetsService == null)
            {
                return new GoogleSheetsApiResponse<HeaderValidationResult>
                {
                    Success = false,
                    ErrorMessage = "Google Sheets servisi başlatılmamış",
                    Data = null
                };
            }

            // İlk satırı al
            var firstRowResponse = await GetFirstRowAsync(spreadsheetId, sheetName);
            if (!firstRowResponse.Success || firstRowResponse.Data == null)
            {
                return new GoogleSheetsApiResponse<HeaderValidationResult>
                {
                    Success = false,
                    ErrorMessage = firstRowResponse.ErrorMessage ?? "İlk satır alınamadı",
                    Data = null
                };
            }

            // Beklenen başlıkları parse et
            var expectedHeaders = ParseHeaderTemplate(headerTemplate);
            var actualHeaders = firstRowResponse.Data;

            // Karşılaştırma yap
            var result = new HeaderValidationResult
            {
                ExpectedHeaders = expectedHeaders,
                ActualHeaders = actualHeaders,
                MissingHeaders = expectedHeaders.Except(actualHeaders).ToList(),
                ExtraHeaders = actualHeaders.Except(expectedHeaders).ToList()
            };

            result.IsValid = result.MissingHeaders.Count == 0 && result.ExtraHeaders.Count == 0;

            if (!result.IsValid)
            {
                var errorMessages = new List<string>();
                if (result.MissingHeaders.Count > 0)
                {
                    errorMessages.Add($"Eksik sütunlar: {string.Join(", ", result.MissingHeaders)}");
                }
                if (result.ExtraHeaders.Count > 0)
                {
                    errorMessages.Add($"Fazla sütunlar: {string.Join(", ", result.ExtraHeaders)}");
                }
                result.ErrorMessage = string.Join("; ", errorMessages);
            }

            return new GoogleSheetsApiResponse<HeaderValidationResult>
            {
                Success = true,
                Data = result
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Başlık kontrol edilirken hata oluştu: {SpreadsheetId}, {SheetName}", spreadsheetId, sheetName);
            return new GoogleSheetsApiResponse<HeaderValidationResult>
            {
                Success = false,
                ErrorMessage = $"Başlık kontrol edilirken hata oluştu: {ex.Message}",
                Data = null
            };
        }
    }

    /// <summary>
    /// Belirtilen sheet'in ilk satırını alır
    /// </summary>
    /// <param name="spreadsheetId">Spreadsheet ID'si</param>
    /// <param name="sheetName">Sheet adı</param>
    /// <returns>İlk satır verisi</returns>
    public async Task<GoogleSheetsApiResponse<List<string>>> GetFirstRowAsync(string spreadsheetId, string sheetName)
    {
        try
        {
            if (_sheetsService == null)
            {
                return new GoogleSheetsApiResponse<List<string>>
                {
                    Success = false,
                    ErrorMessage = "Google Sheets servisi başlatılmamış",
                    Data = null
                };
            }

            var range = $"{sheetName}!1:1";
            var request = _sheetsService.Spreadsheets.Values.Get(spreadsheetId, range);
            var response = await request.ExecuteAsync();

            if (response?.Values == null || response.Values.Count == 0)
            {
                return new GoogleSheetsApiResponse<List<string>>
                {
                    Success = true,
                    Data = new List<string>()
                };
            }

            var firstRow = response.Values[0].Select(v => v?.ToString() ?? string.Empty).ToList();

            return new GoogleSheetsApiResponse<List<string>>
            {
                Success = true,
                Data = firstRow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "İlk satır alınırken hata oluştu: {SpreadsheetId}, {SheetName}", spreadsheetId, sheetName);
            return new GoogleSheetsApiResponse<List<string>>
            {
                Success = false,
                ErrorMessage = $"İlk satır alınırken hata oluştu: {ex.Message}",
                Data = null
            };
        }
    }

    /// <summary>
    /// Belirtilen sheet'in ilk satırını günceller
    /// </summary>
    /// <param name="spreadsheetId">Spreadsheet ID'si</param>
    /// <param name="sheetName">Sheet adı</param>
    /// <param name="headerTemplate">Yeni başlık şablonu (| ile ayrılmış)</param>
    /// <returns>İşlem sonucu</returns>
    public async Task<GoogleSheetsApiResponse<bool>> UpdateHeaderAsync(string spreadsheetId, string sheetName, string headerTemplate)
    {
        try
        {
            if (_sheetsService == null)
            {
                return new GoogleSheetsApiResponse<bool>
                {
                    Success = false,
                    ErrorMessage = "Google Sheets servisi başlatılmamış",
                    Data = false
                };
            }

            var headers = ParseHeaderTemplate(headerTemplate);
            var range = $"{sheetName}!1:1";

            var valueRange = new ValueRange
            {
                Values = new List<IList<object>> { headers.Cast<object>().ToList() }
            };

            var updateRequest = _sheetsService.Spreadsheets.Values.Update(valueRange, spreadsheetId, range);
            updateRequest.ValueInputOption = SpreadsheetsResource.ValuesResource.UpdateRequest.ValueInputOptionEnum.RAW;

            var response = await updateRequest.ExecuteAsync();

            return new GoogleSheetsApiResponse<bool>
            {
                Success = true,
                Data = response.UpdatedCells > 0
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Başlık güncellenirken hata oluştu: {SpreadsheetId}, {SheetName}", spreadsheetId, sheetName);
            return new GoogleSheetsApiResponse<bool>
            {
                Success = false,
                ErrorMessage = $"Başlık güncellenirken hata oluştu: {ex.Message}",
                Data = false
            };
        }
    }

    /// <summary>
    /// Başlık şablonunu parse eder
    /// </summary>
    /// <param name="headerTemplate">Başlık şablonu (| ile ayrılmış)</param>
    /// <returns>Parse edilmiş başlık listesi</returns>
    public List<string> ParseHeaderTemplate(string headerTemplate)
    {
        if (string.IsNullOrWhiteSpace(headerTemplate))
        {
            return new List<string>();
        }

        return headerTemplate
            .Split('|', StringSplitOptions.RemoveEmptyEntries)
            .Select(h => h.Trim())
            .Where(h => !string.IsNullOrWhiteSpace(h))
            .ToList();
    }



    /// <summary>
    /// Belirtilen sütundan değer alır
    /// </summary>
    /// <param name="row">Satır verisi</param>
    /// <param name="headers">Sütun başlıkları</param>
    /// <param name="columnName">Sütun adı</param>
    /// <returns>Sütun değeri</returns>
    public string? GetColumnValue(List<string> row, List<string> headers, string columnName)
    {
        if (!headers.Any(h => h.Equals(columnName, StringComparison.OrdinalIgnoreCase)))
        {
            return null;
        }

        var columnIndex = headers.IndexOf(columnName); // Sütun indeksi alınırken büyük/küçük harf duyarlılığı olmaması için ToLower kullanılır
        if (columnIndex < 0)
        {
            return null;
        }

        if (columnIndex >= row.Count)
        {
            return null;
        }

        var value = row[columnIndex];
        return string.IsNullOrWhiteSpace(value) ? null : value.Trim();
    }

    /// <summary>
    /// Google Sheets'ten veri işleme işlemini gerçekleştirir.
    /// Belirtilen sheet'ten verileri okur, başlık ve sütun eşlemesini uygular, satırları rowMapper ile DTO'ya dönüştürür.
    /// </summary>
    public async Task<GoogleSheetsProcessingResult<T>> ProcessSheetDataAsync<T>(
        string spreadsheetId,
        string sheetName,
        SheetProcessingOptions options,
        Func<List<string>, List<string>, T> rowMapper)
    {
        var result = new GoogleSheetsProcessingResult<T>
        {
            Success = false,
            ProcessedItems = new List<T>(),
            ValidationErrors = new List<string>()
        };

        try
        {
            // 1. Sheet verisini çek
            var sheetDataResponse = await GetSheetDataAsync(spreadsheetId, sheetName);
            if (!sheetDataResponse.Success || sheetDataResponse.Data == null)
            {
                result.ErrorMessage = sheetDataResponse.ErrorMessage ?? "Sheet verisi alınamadı";
                return result;
            }

            var rows = sheetDataResponse.Data;
            result.TotalRows = rows.Count;

            // 2. Header işlemleri
            List<string> headers;
            if (rows.Count == 0)
            {
                result.ErrorMessage = "Sheet boş";
                return result;
            }

            headers = rows[0];

            // 3. Header validasyonu
            var headerValidation = await ValidateHeaderAsync(spreadsheetId, sheetName, options.HeaderTemplate);
            if (!headerValidation.Success || headerValidation.Data == null || !headerValidation.Data.IsValid)
            {
                result.ValidationErrors = headerValidation.Data?.ErrorMessage != null
                    ? new List<string> { headerValidation.Data.ErrorMessage }
                    : new List<string> { headerValidation.ErrorMessage ?? "Başlık doğrulama başarısız" };
                return result;
            }

            // 4. Satırları işle
            var startIndex = options.SkipHeader ? 1 : 0;
            for (int i = startIndex; i < rows.Count; i++)
            {
                var row = rows[i];
                try
                {
                    var dto = rowMapper(row, headers);
                    if (dto != null)
                    {
                        result.ProcessedItems.Add(dto);
                        result.ValidRows++;
                    }
                }
                catch (Exception ex)
                {
                    result.ValidationErrors ??= new List<string>();
                    result.ValidationErrors.Add($"Satır {i + 1}: {ex.Message}");
                }
            }

            result.Success = result.ValidationErrors == null || result.ValidationErrors.Count == 0;
            return result;
        }
        catch (Exception ex)
        {
            result.ErrorMessage = $"İşleme sırasında hata oluştu: {ex.Message}";
            return result;
        }
    }
    /// <summary>
    /// Belirtilen satırları RowId'ye göre günceller.
    /// </summary>
    /// <param name="spreadsheetId">Spreadsheet ID'si</param>
    /// <param name="sheetName">Sheet adı</param>
    /// <param name="updates">Güncellenecek satır verileri</param>
    /// <param name="headerTemplate">Başlık şablonu</param>
    /// <returns>İşlem sonucu</returns>
    public async Task<GoogleSheetsApiResponse<bool>> UpdateRowsByRowIdAsync(string spreadsheetId, string sheetName, List<RowUpdateData> updates, string headerTemplate)
    {
        try
        {
            if (!_isInitialized || _sheetsService == null)
            {
                return new GoogleSheetsApiResponse<bool>
                {
                    Success = false,
                    ErrorMessage = "Servis başlatılmamış."
                };
            }

            // 1. Sheet verisini ve başlıkları al
            var sheetDataResponse = await GetSheetDataAsync(spreadsheetId, sheetName);
            if (!sheetDataResponse.Success || sheetDataResponse.Data == null)
            {
                return new GoogleSheetsApiResponse<bool> { Success = false, ErrorMessage = "Sheet verisi alınamadı." };
            }

            var headers = sheetDataResponse.Data.FirstOrDefault();
            if (headers == null)
            {
                return new GoogleSheetsApiResponse<bool> { Success = false, ErrorMessage = "Sheet başlıkları bulunamadı." };
            }

            var rowIdColumnIndex = headers.IndexOf("row_id");
            if (rowIdColumnIndex == -1)
            {
                return new GoogleSheetsApiResponse<bool> { Success = false, ErrorMessage = "'row_id' sütunu bulunamadı." };
            }

            var expectedHeaders = ParseHeaderTemplate(headerTemplate);
            var sheetRows = sheetDataResponse.Data;
            var batchUpdate = new BatchUpdateValuesRequest
            {
                ValueInputOption = "USER_ENTERED",
                Data = new List<ValueRange>()
            };

            // 2. Güncellemeleri hazırla
            foreach (var update in updates)
            {
                var rowIndex = sheetRows.FindIndex(1, r => r.Count > rowIdColumnIndex && r[rowIdColumnIndex] == update.RowId.ToString());
                if (rowIndex != -1)
                {
                    var newValues = new List<object>();
                    for (int i = 0; i < expectedHeaders.Count; i++)
                    {
                        var header = expectedHeaders[i];
                        var headerIndex = headers.IndexOf(header);
                        if (headerIndex != -1 && headerIndex < update.Values.Count)
                        {
                            newValues.Add(update.Values[headerIndex]);
                        }
                        else
                        {
                            newValues.Add(""); // Eşleşme yoksa boş string ekle
                        }
                    }

                    batchUpdate.Data.Add(new ValueRange
                    {
                        Range = $"{sheetName}!A{rowIndex + 1}",
                        Values = new List<IList<object>> { newValues }
                    });
                }
            }

            if (batchUpdate.Data.Count == 0)
            {
                return new GoogleSheetsApiResponse<bool> { Success = true, Data = true, ErrorMessage = "Güncellenecek satır bulunamadı." };
            }

            // 3. Toplu güncelleme isteğini gönder
            var request = _sheetsService.Spreadsheets.Values.BatchUpdate(batchUpdate, spreadsheetId);
            await request.ExecuteAsync();

            return new GoogleSheetsApiResponse<bool> { Success = true, Data = true };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Satırlar RowId'ye göre güncellenirken hata oluştu.");
            return new GoogleSheetsApiResponse<bool>
            {
                Success = false,
                ErrorMessage = ex.Message
            };
        }
    }
}