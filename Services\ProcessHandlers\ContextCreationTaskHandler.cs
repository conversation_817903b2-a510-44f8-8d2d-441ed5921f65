using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Pgvector;
using Pgvector.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using TranslationAgentServer.Data;
using TranslationAgentServer.Interfaces;
using TranslationAgentServer.Models;
using TranslationAgentServer.Models.Responses;

namespace TranslationAgentServer.Services.ProcessHandlers
{
    /// <summary>
    /// Context oluşturma görevini işler.
    /// </summary>
    public class ContextCreationTaskHandler : ProcessHandlerBase
    {
        public ContextCreationTaskHandler(
            IDatabaseService databaseService,
            IGoogleSheetsService googleSheetsService,
            IProjectService projectService,
            ITextService textService,
            ITermService termService,
            IContextService contextService,
            IGeminiService geminiService,
            IWebScraperService webScraperService,
            ILogger<ContextCreationTaskHandler> logger,
            IProcessUpdateService processUpdateService) : base(
                databaseService,
                googleSheetsService,
                projectService,
                textService,
                termService,
                contextService,
                geminiService,
                webScraperService,
                logger,
                processUpdateService)
        {
        }

        /// <summary>
        /// Context oluşturma task'ını çalıştırır
        /// </summary>
        public override async Task ExecuteAsync(Process process, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Context oluşturma task'ı çalıştırılıyor: {ProcessId}", process.Id);

            // Promptları al
            await LoadContextPrompts(process);

            //Worker sayısını al
            int workerCount = process.Settings?.RootElement.TryGetProperty("WorkerCount", out var workerCountProperty) == true &&
                                workerCountProperty.TryGetInt32(out var workerCountValue) ? workerCountValue : 1;

            _logger.LogInformation("Context oluşturma için {WorkerCount} worker kullanılacak.", workerCount);

            //Worker sayısı kadar taskı sıralayarak delay ile oluştur
            var tasks = new List<Task>();
            for (int i = 0; i < workerCount; i++)
            {
                var workerId = i + 1; // Worker ID'leri 1'den başlasın
                tasks.Add(Task.Run(async () =>
                {

                    await Task.Delay(workerId * 3000);
                    await ContextCreationWorkerAsync(workerId, process, cancellationToken);
                }, cancellationToken));
            }

            //Taskları sırayla çalıştırarak çalışmasını  sağla
            await Task.WhenAll(tasks);

        }

        private async Task ContextCreationWorkerAsync(int workerId, Process process, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Context worker {WorkerId} başlatıldı.", workerId);
            while (true)
            {
                var iscontinue = false;
                //Worker için kullanılacak metinleri ayır
                var contentForWorker = await process.Data.ExecuteAsync(async () =>
              {
                  var content = await process.Data.Client!.WebScraps
                        .Where(t => !process.Data.IsUsing.Contains(t.Id) && !process.Data.IsProcessed.Contains(t.Id) && t.Processed == false).FirstOrDefaultAsync();

                  if (content != null)
                  {
                      process.Data.IsUsing.Add(content.Id);
                  }

                  return content;
              }, cancellationToken);

                if (contentForWorker == null)
                {
                    _logger.LogInformation("Context worker {WorkerId} için işlenecek içerik kalmadı.", workerId);
                    break; // İşlenecek içerik kalmadı, döngüden çık
                }

                cancellationToken.ThrowIfCancellationRequested();

                try
                {
                    _logger.LogInformation("Context worker {WorkerId} için içerik işleniyor: {ContentId}", workerId, contentForWorker.Id);


                    var contexts = await CreateContextAsync(process, workerId, contentForWorker, cancellationToken);
                    if (contexts == null)
                    {
                        throw new InvalidOperationException("Context oluşturulamadı");
                    }

                    if (contexts.Count == 0)
                    {
                        _logger.LogInformation("Context worker {WorkerId} için oluşturulan context bulunamadı: {ContentId}", workerId, contentForWorker.Id);
                        iscontinue = true; // Context oluşturulmadıysa bir sonraki içeriğe geç
                        continue;
                    }

                    var selectedContexts = await ConfirmContextAsync(process, workerId, contexts, cancellationToken);

                    if (selectedContexts.Count == 0)
                    {
                        _logger.LogInformation("Context worker {WorkerId} için onaylanan context bulunamadı: {ContentId}", workerId, contentForWorker.Id);
                        iscontinue = true; // Onaylanacak context yoksa bir sonraki içeriğe geç
                        continue;
                    }

                    var addcontexts = selectedContexts.Select(dto => new Context
                    {
                        Category = dto.Category,
                        Title = dto.Title,
                        Content = dto.Content,
                        ScrapId = contentForWorker.Id,
                        Embedding = dto.Embedding,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    }).ToList();

                    await process.Data.ExecuteAsync(async () =>
                     {
                         contentForWorker.Processed = true;
                         await process.Data.Client!.Contexts.AddRangeAsync(addcontexts);
                         var ret = await process.Data.Client!.SaveChangesAsync();
                         if (ret > 0)
                         {
                             process.Data.IsProcessed.Add(contentForWorker.Id);
                         }

                     }, cancellationToken);

                    _logger.LogInformation("Context worker {WorkerId} için içerik işlendi ve context oluşturuldu: {ContentId}", workerId, contentForWorker.Id);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Context worker {WorkerId} için içerik işleme hatası: {ContentId}", workerId, contentForWorker.Id);

                    if (ex is not InvalidOperationException && !ex.Message.Contains("Retriable Process"))
                    {
                        throw;
                    }

                }
                finally
                {
                    // Kullanılan içeriği temizle
                    await process.Data.ExecuteAsync(() =>
                    {
                        process.Data.IsUsing.Remove(contentForWorker.Id);
                        if (iscontinue)
                        {
                            process.Data.IsProcessed.Add(contentForWorker.Id);
                        }
                        return Task.CompletedTask;
                    }, cancellationToken);
                }


            }
        }

        private async Task<List<ContextCreateDto>> ConfirmContextAsync(Process process, int workerId, List<ContextCreateDto> context, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Context worker {WorkerId} için {ContextCount} adet context onaylanıyor.", workerId, context.Count);
            var contexts = new List<ContextCreateDto>();

            await process.Data.ExecuteAsync(async () =>
            {
                foreach (var ctx in context)
                {
                    cancellationToken.ThrowIfCancellationRequested();

                    // Context'in embedding'ini kontrol et
                    if (ctx.Embedding == null)
                    {
                        _logger.LogWarning("Context worker {WorkerId} için context embedding'i boş: {ContentId}", workerId, ctx.Title);
                        continue; // Embedding boşsa atla
                    }
                    float threshold = 0.05f;
                    var check = await process.Data.Client.Contexts
                        .AnyAsync(d => d.Embedding.CosineDistance(ctx.Embedding) <= threshold);

                    if (!check)
                    {
                        contexts.Add(ctx);
                    }
                    else
                    {
                        _logger.LogInformation("Context worker {WorkerId} için context zaten mevcut: {ContentId}", workerId, ctx.Title);
                    }

                }
            }, cancellationToken);
            return contexts;
        }

        /// <summary>
        /// Context oluşturma işlemini gerçekleştirir
        /// </summary>
        /// <summary>
        /// AI dönüşünü ContextCreationResponse ile parse ederek context oluşturma işlemini gerçekleştirir.
        /// </summary>
        private async Task<List<ContextCreateDto>> CreateContextAsync(Process process, int workerId, WebScrap content, CancellationToken cancellationToken)
        {
            var prompt = "url:" + content.Url + "\n<kaynak_veri>\n" + content.Content + "\n</kaynak_veri>";
            var model = process.Settings?.RootElement.TryGetProperty("Model", out var modelProperty) == true ? modelProperty.GetString() ?? "gemini-2.5-flash" : "gemini-2.5-flash";
            var request = new GeminiContentRequest
            {
                Prompt = prompt,
                Model = model,
                SystemInstruction = process.ContextPrompts.Create,
                Temperature = 0.7f,
                ThinkingBudget = process.Settings?.RootElement.TryGetProperty("ThinkingBudget", out var thinkingBudgetProperty) == true &&
                thinkingBudgetProperty.TryGetInt32(out var thinkingBudgetValue) ? thinkingBudgetValue : 0
            };

            var response = await _geminiService.GenerateContentAsync(request, cancellationToken, true);
            if (!response.Success)
            {
                _logger.LogError("Context worker {WorkerId} için içerik oluşturma hatası: {ContentId}", workerId, content.Id);
                return null;
            }

            List<ContextCreateDto> contexts = new();
            try
            {
                // AI dönüşünü ContextCreationResponse ile parse et
                var contextCreationResponse = JsonSerializer.Deserialize<ContextCreationResponse>(response.JsonOutput!);
                if (contextCreationResponse == null || contextCreationResponse.Contexts == null)
                {
                    _logger.LogError("Gemini API response ContextCreationResponse olarak parse edilemedi: {Response}", response.Text);
                    throw new InvalidOperationException("Gemini API response ContextCreationResponse olarak parse edilemedi. Retriable Process.");
                }

                foreach (var ctx in contextCreationResponse.Contexts)
                {
                    contexts.Add(new ContextCreateDto
                    {
                        Content = ctx.Content,
                        Category = ctx.Category,
                        Title = ctx.Title
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Gemini API response işleme hatası: {Response}", response.Text);
                throw new InvalidOperationException($"Gemini API response işlenemedi: {ex.Message}. Retriable Process.");
            }

            if (contexts.Count > 0)
            {
                var batchSize = 100;
                for (int i = 0; i < contexts.Count; i += batchSize)
                {
                    var batch = contexts.Skip(i).Take(batchSize).ToList();
                    // Retry mekanizması ile embedding üretimi
                    var embeddingsResponse = await _geminiService.GetEmbeddingsAsync(batch.Select(t => $"{t.Category} {t.Title} {t.Content}").ToList(), cancellationToken);

                    if (!embeddingsResponse.Success)
                    {
                        throw new Exception($"Embedding oluşturulamadı. Hata: {embeddingsResponse.ErrorMessage}");
                    }

                    for (int j = 0; j < batch.Count; j++)
                    {
                        batch[j].Embedding = new Vector(embeddingsResponse.Embeddings[j]);
                    }

                    // Batch'ler arası bekleme süresi (rate limiting için)
                    await Task.Delay(3500);
                }
            }

            if (contexts.Count == 0)
            {
                _logger.LogWarning("Context worker {WorkerId} için oluşturulan context bulunamadı: {ContentId}", workerId, content.Id);
            }
            else
            {
                _logger.LogInformation("Context worker {WorkerId} için {ContextCount} adet context oluşturuldu: {ContentId}", workerId, contexts.Count, content.Id);
            }
            return contexts;
        }
    }
}