using System.Threading;
using System.Threading.Tasks;
using TranslationAgentServer.Models;

namespace TranslationAgentServer.Interfaces
{
    /// <summary>
    /// OpenAI AI servisi için arayüz.
    /// Bu servis, OpenAI API'sini kullanarak içerik üretme işlemlerini yönetir.
    /// </summary>
    public interface IOpenAIService
    {
        /// <summary>
        /// OpenAI servisini verilen API anahtarı ile başlatır.
        /// </summary>
        /// <param name="apiKey">OpenAI API anahtarı.</param>
        /// <returns>Başlatma işleminin başarılı olup olmadığını belirten bir boolean değer.</returns>
        Task<bool> InitializeAsync(string apiKey);

        /// <summary>
        /// Belirtilen isteğe göre OpenAI modelini kullanarak içerik üretir.
        /// </summary>
        /// <param name="request">İçerik üretim isteği için gerekli parametreleri içeren model.</param>
        /// <param name="cancellationToken">İşlemi iptal etmek için kullanılabilecek token.</param>
        /// <returns>Üretilen içeriği ve işlem detaylarını içeren bir yanıt nesnesi.</returns>
        Task<OpenAIContentResponse> GenerateContentAsync(OpenAIContentRequest request, CancellationToken cancellationToken);
    }
}