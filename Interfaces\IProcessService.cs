using TranslationAgentServer.Models;
using TranslationAgentServer.Models.Filtering;
using System.Text.Json;

namespace TranslationAgentServer.Interfaces;

/// <summary>
/// Birleştirilmiş işlem yönetimi servisinin arayüzü
/// Proje işlemlerinin CRUD operasyonlarını, durum yönetimini ve arka plan görevlerini tanımlar
/// </summary>
public interface IProcessService
{
    // CRUD Operations
    /// <summary>
    /// Belirtilen projeye ait tüm işlemleri listeler
    /// </summary>
    /// <param name="projectId">Proje kimliği</param>
    /// <returns>İşlem listesi</returns>
    Task<IEnumerable<Process>> GetProcessesByProjectIdAsync(int projectId);

    /// <summary>
    /// Gelişmiş filtreleme ile işlemleri getirir
    /// </summary>
    /// <param name="projectId"><PERSON>je kimliğ<PERSON></param>
    /// <param name="filterQuery">Filtreleme sorgusu</param>
    /// <returns>Filtrelenmiş işlem listesi</returns>
    Task<IEnumerable<Process>> GetProcessesByProjectIdAsync(int projectId, ProcessFilterQuery filterQuery);


    /// <summary>
    /// JsonElement ile filtreleme (backend kullanımı için)
    /// </summary>
    /// <param name="projectId">Proje kimliği</param>
    /// <param name="filterElement">JsonElement formatında filtreleme sorgusu</param>
    /// <returns>Filtrelenmiş işlem listesi</returns>
    Task<IEnumerable<Process>> GetProcessesByProjectIdAsync(int projectId, JsonElement filterElement);

    /// <summary>
    /// Belirtilen ID'ye sahip işlemi getirir
    /// </summary>
    /// <param name="id">İşlem kimliği</param>
    /// <returns>İşlem bilgileri veya null</returns>
    Task<Process?> GetProcessByIdAsync(Guid id);

    /// <summary>
    /// Yeni işlem oluşturur ve başlatır
    /// </summary>
    /// <param name="processDto">İşlem bilgileri</param>
    /// <returns>Oluşturulan ve başlatılan işlem</returns>
    Task<Process> CreateAndStartProcessAsync(ProcessCreateDto processDto);

    /// <summary>
    /// Mevcut işlemi günceller
    /// </summary>
    /// <param name="id">İşlem kimliği</param>
    /// <param name="processUpdateDto">Güncellenecek işlem bilgileri</param>
    /// <returns>Güncellenmiş işlem veya null</returns>
    Task<Process?> UpdateProcessWithIDAsync(Guid id, ProcessUpdateDto processUpdateDto);

    /// <summary>
    /// İşlemi siler
    /// </summary>
    /// <param name="id">İşlem kimliği</param>
    /// <returns>Silme işleminin başarılı olup olmadığı</returns>
    Task<bool> DeleteProcessAsync(Guid id);

    /// <summary>
    /// İşlemi iptal eder
    /// </summary>
    /// <param name="id">İşlem kimliği</param>
    /// <returns>İptal işleminin başarılı olup olmadığı</returns>
    Task<bool> CancelProcessAsync(Guid id);

    /// <summary>
    /// Mevcut işlemi günceller
    /// </summary>
    /// <param name="existingProcess">Güncellenecek işlem bilgileri</param>
    /// <returns>Güncellenmiş işlem veya null</returns>
    Task UpdateProcessAsync(Process existingProcess);

}
