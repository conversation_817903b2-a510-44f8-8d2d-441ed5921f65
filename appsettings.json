{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "User Id=postgres.dbzlkykhinyktgjhuorb;Password=***************************;Server=aws-0-eu-central-1.pooler.supabase.com;Port=5432;Database=postgres"}, "Supabase": {"Url": "https://dbzlkykhinyktgjhuorb.supabase.co", "Key": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRiemxreWtoaW55a3Rnamh1b3JiIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTcxMTI2MCwiZXhwIjoyMDY3Mjg3MjYwfQ.GS1-LbO3gdiyZpbO3ABTs0Hu-vk3xjYqli11K8OaPWM"}}