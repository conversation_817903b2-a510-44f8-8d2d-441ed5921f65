using System.Collections.Concurrent;
using Microsoft.AspNetCore.Mvc.NewtonsoftJson;
using Microsoft.EntityFrameworkCore;
using TranslationAgentServer.Interfaces;
using TranslationAgentServer.Models;
using TranslationAgentServer.Models.Filtering;
using System.Text.Json;
using TranslationAgentServer.Services.ProcessHandlers;
namespace TranslationAgentServer.Services;

/// <summary>
/// Birleştirilmiş işlem yönetimi servisinin implementasyonu
/// </summary>
public class ProcessService : IProcessService
{
    private readonly IDatabaseService _databaseService;
    private readonly IGoogleSheetsService _googleSheetsService;
    private readonly IProjectService _projectService;
    private readonly ITextService _textService;
    private readonly ITermService _termService;
    private readonly IProcessUpdateService _processUpdateService;
    private readonly ILogger<ProcessService> _logger;

    private readonly IProcessTaskHandler _projectCreationTaskHandler;
    private readonly IProcessTaskHandler _textsTranslationTaskHandler;
    private readonly IProcessTaskHandler _termsDetectionTaskHandler;
    private readonly IProcessTaskHandler _termsTranslationTaskHandler;
    private readonly IProcessTaskHandler _exportToGoogleSheetsTaskHandler;
    private readonly IProcessTaskHandler _webScrapingTaskHandler;
    private readonly IProcessTaskHandler _contextCreationTaskHandler;
    private readonly IProcessTaskHandler _projectSyncTaskHandler;

    public ConcurrentDictionary<Guid, CancellationTokenSource> TaskCancellationTokens { get; }


    public ProcessService(
        IDatabaseService databaseService,
        IGoogleSheetsService googleSheetsService,
        IProjectService projectService,
        ITextService textService,
        ITermService termService,
                    IProcessTaskHandler projectCreationTaskHandler,
            IProcessTaskHandler textsTranslationTaskHandler,
            IProcessTaskHandler termsDetectionTaskHandler,
            IProcessTaskHandler termsTranslationTaskHandler,
            IProcessTaskHandler exportToGoogleSheetsTaskHandler,
            IProcessTaskHandler webScrapingTaskHandler,
            IProcessTaskHandler contextCreationTaskHandler,
            IProcessTaskHandler projectSyncTaskHandler,
            IProcessUpdateService processUpdateService,
        ILogger<ProcessService> logger)
    {
        _databaseService = databaseService;
        _googleSheetsService = googleSheetsService;
        _projectService = projectService;
        _textService = textService;
        _termService = termService;
        _processUpdateService = processUpdateService;
        _logger = logger;
        // Handler'ları ata
        _projectCreationTaskHandler = projectCreationTaskHandler;
        _textsTranslationTaskHandler = textsTranslationTaskHandler;
        _termsDetectionTaskHandler = termsDetectionTaskHandler;
        _termsTranslationTaskHandler = termsTranslationTaskHandler;
        _exportToGoogleSheetsTaskHandler = exportToGoogleSheetsTaskHandler;
        _webScrapingTaskHandler = webScrapingTaskHandler;
        _contextCreationTaskHandler = contextCreationTaskHandler;
        _projectSyncTaskHandler = projectSyncTaskHandler;
        _processUpdateService = processUpdateService;

        TaskCancellationTokens = new ConcurrentDictionary<Guid, CancellationTokenSource>();


    }


    #region CRUD Operations

    /// <summary>
    /// Belirtilen projeye ait tüm işlemleri listeler
    /// </summary>
    /// <param name="projectId">Proje kimliği</param>
    /// <returns>İşlem listesi</returns>
    public async Task<IEnumerable<Process>> GetProcessesByProjectIdAsync(int projectId)
    {
        try
        {



            await using var client = _databaseService.GetContext();
            var query = client.Processes.Where(x => x.Project.Id == projectId);


            var response = await query
                .ToListAsync();

            return response ?? new List<Process>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Proje işlemleri getirilirken hata oluştu: ProjectId={ProjectId}",

                projectId);
            throw;
        }
    }

    public async Task<IEnumerable<Process>> GetProcessesByProjectIdAsync(int projectId, ProcessFilterQuery filterQuery)
    {
        try
        {
            _logger.LogInformation("Gelişmiş filtreleme ile proje işlemleri getiriliyor: ProjectId={ProjectId}", projectId);


            await using var client = _databaseService.GetContext();
            var query = client.Processes
                .Where(x => x.Project.Id == projectId)
                .AsQueryable();


            var filteredQuery = FilteringService.ApplyFilters(query, filterQuery);


            return await filteredQuery.ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Gelişmiş filtreleme ile proje işlemleri getirilirken hata oluştu: ProjectId={ProjectId}", projectId);
            throw;
        }
    }


    public async Task<IEnumerable<Process>> GetProcessesByProjectIdAsync(int projectId, JsonElement filterElement)
    {
        try
        {
            var filterQuery = ProcessFilterQuery.FromJsonElement(filterElement);
            return await GetProcessesByProjectIdAsync(projectId, filterQuery);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "JsonElement filtreleme ile proje işlemleri getirilirken hata oluştu: ProjectId={ProjectId}", projectId);
            throw;
        }
    }



    /// <summary>
    /// Belirtilen ID'ye sahip işlemi getirir
    /// </summary>
    /// <param name="id">İşlem kimliği</param>
    /// <returns>İşlem bilgileri veya null</returns>
    public async Task<Process?> GetProcessByIdAsync(Guid id)
    {
        try
        {
            _logger.LogInformation("İşlem getiriliyor: {ProcessId}", id);

            await using var client = _databaseService.GetContext();
            var response = await client.Processes
                .Where(x => x.Id == id)
                .SingleOrDefaultAsync();

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "İşlem getirilirken hata oluştu: {ProcessId}", id);
            return null;
        }
    }

    /// <summary>
    /// Yeni işlem oluşturur ve başlatır
    /// </summary>
    /// <param name="processDto">İşlem bilgileri</param>
    /// <returns>Oluşturulan ve başlatılan işlem</returns>
    public async Task<Process> CreateAndStartProcessAsync(ProcessCreateDto processDto)
    {
        try
        {
            await using var client = _databaseService.GetContext();
            // çalışan process kontrolü (proje için)
            var check = await client.Processes
                .Where(p => p.Project.Id == processDto.ProjectId)
                .Where(p => p.Status == (int)ProcessStatus.InProgress || p.Status == (int)ProcessStatus.Pending)
                .SingleOrDefaultAsync();

            if (check != null)
            {
                throw new InvalidOperationException("Projenin zaten bir işlemi var. Önceki işlemi tamamlamadan veya durdurmadan yeni bir işlem oluşturamazsınız.");
            }

            _logger.LogInformation("Yeni işlem oluşturuluyor: ProjectId={ProjectId}, TaskType={TaskType}",
                processDto.ProjectId, processDto.TaskType);

            var process = new Process
            {
                Id = Guid.NewGuid(),
                ProjectId = processDto.ProjectId,
                TaskType = (int)processDto.TaskType,
                Status = (int)ProcessStatus.Pending,
                CreatedAt = DateTime.UtcNow,
                LastPing = DateTime.UtcNow,
                Progress = 0,
                Result = null,
                ErrorMessage = null,
            };
            var projectSettings = (await _projectService.GetProjectByIdAsync(processDto.ProjectId)).Settings;
            var filteredSettings = FilterSettingsByTaskType(projectSettings, processDto.TaskType);

            if (processDto.Settings != null)
            {
                // Settings özelliği zaten ayarlandıysa, onu kullan ve override et
                process.Settings = OverrideSettings(filteredSettings, processDto.Settings);
            }
            else
            {
                process.Settings = filteredSettings;
            }
            var response = await client.Processes.AddAsync(process);
            var result = await client.SaveChangesAsync();

            var createdProcess = result > 0 ? process : null;
            if (createdProcess == null)
            {
                throw new InvalidOperationException("İşlem oluşturulamadı");
            }

            _logger.LogInformation("İşlem başarıyla oluşturuldu: {ProcessId}", createdProcess.Id);


            _logger.LogInformation("Process task başlatılıyor: {ProcessId}, Type: {TaskType}", createdProcess.Id, createdProcess.TaskType);

            // Cancellation token oluştur
            var cts = new CancellationTokenSource();
            TaskCancellationTokens[process.Id] = cts;

            // Task'ı arka planda başlat
            _ = Task.Run(async () => await ExecuteProcessTaskInternalAsync(createdProcess, cts.Token));

            _logger.LogInformation("Process task başarıyla başlatıldı: {ProcessId}", createdProcess.Id);
            return createdProcess;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "İşlem oluşturulurken hata oluştu: ProjectId={ProjectId}, TaskType={TaskType}",
                processDto.ProjectId, processDto.TaskType);
            throw;
        }
    }

    /// <summary>
    /// Mevcut işlemi günceller
    /// </summary>
    /// <param name="id">İşlem kimliği</param>
    /// <param name="processUpdateDto">Güncellenecek işlem bilgileri</param>
    /// <returns>Güncellenmiş işlem veya null</returns>
    public async Task<Process?> UpdateProcessWithIDAsync(Guid id, ProcessUpdateDto processUpdateDto)
    {
        try
        {
            _logger.LogInformation("İşlem güncelleniyor: {ProcessId}", id);

            // Önce işlemin var olup olmadığını kontrol et
            var existingProcess = await GetProcessByIdAsync(id);
            if (existingProcess == null)
            {
                _logger.LogWarning("Güncellenecek işlem bulunamadı: {ProcessId}", id);
                return null;
            }

            existingProcess.Status = (int)processUpdateDto.Status;
            existingProcess.Result = processUpdateDto.Result ?? existingProcess.Result;
            existingProcess.Progress = processUpdateDto.Progress ?? existingProcess.Progress;
            existingProcess.ErrorMessage = processUpdateDto.ErrorMessage ?? existingProcess.ErrorMessage;
            existingProcess.Settings = OverrideSettings(existingProcess.Settings, processUpdateDto.Settings);
            existingProcess.LastPing = DateTime.UtcNow;
            existingProcess.CompletedAt = (int)processUpdateDto.Status == (int)ProcessStatus.Completed || (int)processUpdateDto.Status == (int)ProcessStatus.Failed || (int)processUpdateDto.Status == (int)ProcessStatus.Cancelled
              ? DateTime.UtcNow : existingProcess.CompletedAt;

            await using var client = _databaseService.GetContext();

            existingProcess = client.Processes.Update(existingProcess).Entity;
            var result = await client.SaveChangesAsync();

            var updatedProcess = result > 0 ? existingProcess : null;
            if (updatedProcess != null)
            {
                _logger.LogInformation("İşlem başarıyla güncellendi: {ProcessId}", id);
            }

            return updatedProcess;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "İşlem güncellenirken hata oluştu: {ProcessId}", id);
            throw;
        }
    }

    /// <summary>
    /// İşlemi siler
    /// </summary>
    /// <param name="id">İşlem kimliği</param>
    /// <returns>Silme işleminin başarılı olup olmadığı</returns>
    public async Task<bool> DeleteProcessAsync(Guid id)
    {
        try
        {
            _logger.LogInformation("İşlem siliniyor: {ProcessId}", id);

            // Önce işlemin var olup olmadığını kontrol et
            var existingProcess = await GetProcessByIdAsync(id);
            if (existingProcess == null)
            {
                _logger.LogWarning("Silinecek işlem bulunamadı: {ProcessId}", id);
                return false;
            }

            if (existingProcess.Status == (int)ProcessStatus.InProgress)
            {
                var cancel = await CancelProcessAsync(id);
                if (!cancel)
                {
                    _logger.LogWarning("İşlem durdurulamadı: {ProcessId}", id);
                    return false;
                }
            }

            await using var client = _databaseService.GetContext();
            client.Processes.Remove(existingProcess);
            var result = await client.SaveChangesAsync();

            var deletedProcess = result > 0 ? existingProcess : null;
            if (deletedProcess != null)
            {
                _logger.LogInformation("İşlem başarıyla silindi: {ProcessId}", id);
            }

            return deletedProcess != null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "İşlem silinirken hata oluştu: {ProcessId}", id);
            throw;
        }
    }

    /// <summary>
    /// İşlemi iptal eder
    /// </summary>
    /// <param name="id">İşlem kimliği</param>
    /// <returns>İptal işleminin başarılı olup olmadığı</returns>
    public async Task<bool> CancelProcessAsync(Guid id)
    {
        try
        {
            _logger.LogInformation("İşlem iptal ediliyor: {ProcessId}", id);
            if (TaskCancellationTokens.TryGetValue(id, out var cts))
            {
                cts.Cancel();

                // Durumu güncelle
                var updateDto = new ProcessUpdateDto
                {
                    Status = ProcessStatus.Cancelled,
                    Result = "İşlem kullanıcı tarafından iptal edildi"
                };
                await UpdateProcessWithIDAsync(id, updateDto);

                _logger.LogInformation("Process task başarıyla iptal edildi: {ProcessId}", id);
                return true;
            }
            else
            {
                _logger.LogWarning("İşlem iptal edilemedi: {ProcessId}", id);
                return false;
            }

        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "İşlem iptal edilirken hata oluştu: {ProcessId}", id);
            throw;
        }
    }

    public async Task UpdateProcessAsync(Process existingProcess)
    {
        await _processUpdateService.UpdateProcess(existingProcess.Id, process =>
        {
            process.Status = existingProcess.Status;
            process.Result = existingProcess.Result;
            process.ErrorMessage = existingProcess.ErrorMessage;
            process.Progress = existingProcess.Progress;
            process.Settings = existingProcess.Settings;
        });
    }

    #endregion

    /// <summary>
    /// TaskType'a göre proje ayarlarını filtreler ve düzenler
    /// </summary>
    /// <param name="projectSettings">Proje ayarları</param>
    /// <param name="taskType">İşlem türü</param>
    /// <returns>Filtrelenmiş ayarlar</returns>
    private static JsonDocument FilterSettingsByTaskType(JsonDocument projectSettings, ProcessTaskType taskType)
    {
        try
        {
            var settingsDict = new Dictionary<string, object>();
            var rootElement = projectSettings.RootElement;

            switch (taskType)
            {
                case ProcessTaskType.TermsDetection:
                    // Terim tespiti için gerekli ayarlar
                    if (rootElement.TryGetProperty("TermDetectModel", out var termDetectModel))
                        settingsDict["Model"] = termDetectModel.GetString() ?? "gemini-2.5-flash";


                    if (rootElement.TryGetProperty("TermDetectSendCount", out var termDetectSendCount))
                        settingsDict["SendCount"] = termDetectSendCount.GetInt32();


                    if (rootElement.TryGetProperty("TermDetectWorkerCount", out var termDetectWorkerCount))
                        settingsDict["WorkerCount"] = termDetectWorkerCount.GetInt32();


                    if (rootElement.TryGetProperty("TermDetectIncludeNamespaceAndKey", out var termDetectInclude))
                        settingsDict["IncludeNamespaceAndKey"] = termDetectInclude.GetBoolean();
                    break;

                case ProcessTaskType.TermsTranslation:
                    // Terim çevirisi için gerekli ayarlar
                    if (rootElement.TryGetProperty("TermTranslateModel", out var termTranslateModel))
                        settingsDict["Model"] = termTranslateModel.GetString() ?? "gemini-2.5-flash";


                    if (rootElement.TryGetProperty("TermTranslateWorkerCount", out var termTranslateWorkerCount))
                        settingsDict["WorkerCount"] = termTranslateWorkerCount.GetInt32();


                    if (rootElement.TryGetProperty("TermTranslateIncludeNamespaceAndKey", out var termTranslateInclude))
                        settingsDict["IncludeNamespaceAndKey"] = termTranslateInclude.GetBoolean();
                    break;

                case ProcessTaskType.TextsTranslation:
                    // Metin çevirisi için gerekli ayarlar
                    if (rootElement.TryGetProperty("TextStrategyModel", out var textStrategyModel))
                        settingsDict["StrategyModel"] = textStrategyModel.GetString() ?? "gemini-2.5-flash";


                    if (rootElement.TryGetProperty("TextTranslateModel", out var textTranslateModel))
                        settingsDict["TranslateModel"] = textTranslateModel.GetString() ?? "gemini-2.5-flash";


                    if (rootElement.TryGetProperty("TextTranslateHighModel", out var textTranslateHighModel))
                        settingsDict["HighQualityModel"] = textTranslateHighModel.GetString() ?? "gemini-2.5-pro";


                    if (rootElement.TryGetProperty("TextTranslateWorkerCount", out var textTranslateWorkerCount))
                        settingsDict["WorkerCount"] = textTranslateWorkerCount.GetInt32();


                    if (rootElement.TryGetProperty("TextTranslateIncludeNamespaceAndKey", out var textTranslateInclude))
                        settingsDict["IncludeNamespaceAndKey"] = textTranslateInclude.GetBoolean();
                    break;

                case ProcessTaskType.ProjectCreation:
                case ProcessTaskType.ExportToGoogleSheets:
                case ProcessTaskType.WebScraping:
                    // Proje oluşturma, Google Sheets'e aktarma ve Web Scraping için genel ayarlar
                    if (rootElement.TryGetProperty("WebScrapingMaxPages", out var webScrapingMaxPages))
                        settingsDict["MaxPages"] = webScrapingMaxPages.GetInt32();

                    if (rootElement.TryGetProperty("WebScrapingMaxDepth", out var webScrapingMaxDepth))
                        settingsDict["MaxDepth"] = webScrapingMaxDepth.GetInt32();
                    break;
                case ProcessTaskType.ContextCreation:
                    // Context oluşturma için gerekli ayarlar
                    if (rootElement.TryGetProperty("ContextCreationModel", out var contextModel))
                        settingsDict["Model"] = contextModel.GetString() ?? "gemini-2.5-flash";
                    if (rootElement.TryGetProperty("ContextCreationWorkerCount", out var contextWorkerCount))
                        settingsDict["WorkerCount"] = contextWorkerCount.GetInt32();
                    break;
                default:
                    // Diğer işlem türleri için tüm ayarları kopyala
                    foreach (var property in rootElement.EnumerateObject())
                    {
                        settingsDict[property.Name] = GetJsonElementValue(property.Value);
                    }
                    break;
            }

            // Dictionary'yi JsonDocument'a dönüştür
            var jsonString = JsonSerializer.Serialize(settingsDict);
            return JsonDocument.Parse(jsonString);
        }
        catch (Exception ex)
        {
            throw;
        }
    }

    /// <summary>
    /// JsonElement değerini uygun C# tipine dönüştürür
    /// </summary>
    /// <param name="element">JsonElement</param>
    /// <returns>Dönüştürülmüş değer</returns>
    private static object GetJsonElementValue(JsonElement element)
    {
        return element.ValueKind switch
        {
            JsonValueKind.String => element.GetString() ?? string.Empty,
            JsonValueKind.Number => element.TryGetInt32(out var intValue) ? intValue : element.GetDouble(),
            JsonValueKind.True => true,
            JsonValueKind.False => false,
            JsonValueKind.Null => null!,
            JsonValueKind.Array => element.EnumerateArray().Select(GetJsonElementValue).ToArray(),
            JsonValueKind.Object => element.EnumerateObject().ToDictionary(p => p.Name, p => GetJsonElementValue(p.Value)),
            _ => element.ToString()
        };
    }


    /// <summary>
    /// İlk ayarları ikinci ayarlara göre günceller
    /// </summary>
    /// <param name="settings1">İlk ayarlar</param>
    /// <param name="settings2">İkinci ayarlar</param>
    /// <returns>Güncellenmiş ayarlar</returns>
    private static JsonDocument? OverrideSettings(JsonDocument? settings1, JsonDocument settings2)
    {
        if (settings1 == null) return settings2;

        var settings1Dict = JsonSerializer.Deserialize<Dictionary<string, object>>(settings1.RootElement.GetRawText());
        var settings2Dict = JsonSerializer.Deserialize<Dictionary<string, object>>(settings2.RootElement.GetRawText());

        foreach (var kvp in settings2Dict)
        {
            if (kvp.Value == null) continue;
            settings1Dict[kvp.Key] = kvp.Value;
        }

        var jsonString = JsonSerializer.Serialize(settings1Dict);
        return JsonDocument.Parse(jsonString);
    }


    /// <summary>
    /// Process task'ını çalıştırır
    /// </summary>
    /// <param name="taskInfo">Task bilgileri</param>
    /// <param name="cancellationToken">İptal token'ı</param>
    /// <returns>Task</returns>
    private async Task ExecuteProcessTaskInternalAsync(Process process, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Process task çalıştırılıyor: {ProcessId}, Type: {TaskType}",
                process.Id, process.TaskType);

            process.Status = (int)ProcessStatus.InProgress;
            process.Progress = 0;
            process.Result = "İşlem başlatılıyor...";
            await UpdateProcessAsync(process);

            process.Data.Client = _databaseService.GetProjectContext(process.ProjectId);

            //Filter varsa JsonElement olarak al
            if (process.Data.TextFilter != null && process.Settings?.RootElement.TryGetProperty("TextFilter", out var textFilterProperty) == true)
            {
                process.Data.TextFilter = TextFilterQuery.FromJsonElement(textFilterProperty);
            }
            else
            {
                process.Data.TextFilter = new TextFilterQuery();
            }

            // Task türüne göre işlem yap
            switch (process.TaskType)
            {
                case (int)ProcessTaskType.ProjectCreation: // Proje oluşturma işlemi
                    await _projectCreationTaskHandler.ExecuteAsync(process, cancellationToken);
                    break;
                case (int)ProcessTaskType.TextsTranslation:
                    await _textsTranslationTaskHandler.ExecuteAsync(process, cancellationToken);
                    break;
                case (int)ProcessTaskType.TermsDetection: // Terim tespiti işlemi
                    await _termsDetectionTaskHandler.ExecuteAsync(process, cancellationToken);
                    break;
                case (int)ProcessTaskType.TermsTranslation:
                    await _termsTranslationTaskHandler.ExecuteAsync(process, cancellationToken);
                    break;
                case (int)ProcessTaskType.ExportToGoogleSheets:
                    await _exportToGoogleSheetsTaskHandler.ExecuteAsync(process, cancellationToken);
                    break;
                case (int)ProcessTaskType.WebScraping:
                    await _webScrapingTaskHandler.ExecuteAsync(process, cancellationToken);
                    break;
                case (int)ProcessTaskType.ContextCreation:
                    await _contextCreationTaskHandler.ExecuteAsync(process, cancellationToken);
                    break;
                case (int)ProcessTaskType.ProjectSync:
                    await _projectSyncTaskHandler.ExecuteAsync(process, cancellationToken);
                    break;
                default:
                    throw new NotSupportedException($"Desteklenmeyen task türü: {process.TaskType}");
            }

            // Başarıyla tamamlandı
            process.Result = "İşlem başarıyla tamamlandı";
            process.Status = (int)ProcessStatus.Completed;
            process.Progress = 100;

            // Process durumunu güncelle
            await UpdateProcessAsync(process);

            _logger.LogInformation("Process task başarıyla tamamlandı: {ProcessId}", process.Id);
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("Process task iptal edildi: {ProcessId}", process.Id);
            process.Status = (int)ProcessStatus.Cancelled;

            // Process durumunu güncelle
            await UpdateProcessAsync(process);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Process task çalıştırılırken hata oluştu: {ProcessId}", process.Id);

            process.Status = (int)ProcessStatus.Failed;
            if (ex.InnerException != null)
            {
                process.ErrorMessage = ex.Message + "\n\nInner Exception: " + ex.InnerException.Message;
            }
            else
            {
                process.ErrorMessage = ex.Message;
            }
            process.Result = "İşlem başarısız oldu";
            await UpdateProcessAsync(process);
        }
        finally
        {
            // Kullanılan kaynakları temizle
            if (process.Data.Client != null)
            {
                await process.Data.Client.DisposeAsync();
            }
        }
    }

}