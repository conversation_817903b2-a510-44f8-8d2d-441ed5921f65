# TranslationAgentServer - <PERSON><PERSON>ti

## <PERSON><PERSON>
`TranslationAgentServer`, **.NET 9.0** <PERSON>zerine inşa edilmiş, gelişmiş bir AI destekli çeviri yönetimi platformudur. <PERSON><PERSON><PERSON> temel amacı, metin çeviri süreçlerini otomatize etmek, yönetmek ve zenginleştirmektir.

## <PERSON> Amaç
<PERSON>je, AI destekli çeviri (Gemini, OpenAI), proje yönetim<PERSON>, terminoloji yönet<PERSON>, ba<PERSON><PERSON> analizi, arka plan işlemleri, NLP (`Catalyst`), Google Sheets entegrasyonu ve web scraping (`Playwright`) gibi karmaşık çeviri iş akışlarını basitleştirmeyi hedefler.

## Temel Özellikler
- **AI Destekli Çeviri**: Gemini ve OpenAI entegrasyonları ile yüksek kaliteli çeviri.
- **Proje <PERSON>**: Çeviri projelerinin oluşturulması ve yönetilmesi.
- **Terminoloji Yönetimi**: Proje bazında tutarlı terminoloji kullanımı.
- **Bağlam Analizi**: Çeviri kalitesini artırmak için metinlerin bağlamsal analizi.
- **Arka Plan İşlemleri**: Büyük çeviri görevlerinin arka planda yönetilmesi.
- **NLP Entegrasyonu**: `Catalyst` ile metin analizi ve doğal dil işleme.
- **Google Sheets Entegrasyonu**: Çeviri verilerinin Google E-Tablolar ile senkronizasyonu.
- **Web Scraping**: `Playwright` ile web'den içerik çekme ve çeviriye hazır hale getirme.

## Teknik Özet
- **Backend**: .NET 9.0, ASP.NET Core Web API
- **Veritabanı**: PostgreSQL (pgvector eklentisi ile)
- **ORM**: Entity Framework Core
- **Mimari**: Servis odaklı mimari (SOA) ve Dependency Injection prensipleri.
- **AI & Veri**: Gemini, OpenAI, Catalyst, Playwright
