using System;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using OpenAI;
using OpenAI.Chat;
using TranslationAgentServer.Interfaces;
using TranslationAgentServer.Models;

namespace TranslationAgentServer.Services
{
    /// <summary>
    /// OpenAI AI servisi.
    /// Bu servis, OpenAI API'sini kullanarak içerik üretme işlemlerini yönetir.
    /// </summary>
    public class OpenAIService : IOpenAIService
    {
        private readonly ILogger<OpenAIService> _logger;
        private OpenAIClient _openAIClient;
        private bool _isInitialized = false;

        /// <summary>
        /// OpenAIService sınıfının bir örneğini oluşturur.
        /// </summary>
        /// <param name="logger">Loglama için logger nesnesi.</param>
        public OpenAIService(ILogger<OpenAIService> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// OpenAI servisini verilen API anahtarı ile başlatır.
        /// </summary>
        /// <param name="apiKey">OpenAI API anahtarı.</param>
        /// <returns>Başlatma işleminin başarılı olup olmadığını belirten bir boolean değer.</returns>
        public Task<bool> InitializeAsync(string apiKey)
        {
            if (string.IsNullOrWhiteSpace(apiKey))
            {
                _logger.LogError("OpenAI API anahtarı boş veya geçersiz.");
                throw new ArgumentException("OpenAI API anahtarı boş veya geçersiz olamaz.", nameof(apiKey));
            }

            if (_isInitialized)
            {
                _logger.LogWarning("OpenAI servisi zaten başlatılmış.");
                return Task.FromResult(false);
            }

            try
            {
                _openAIClient = new OpenAIClient(apiKey);
                _isInitialized = true;
                _logger.LogInformation("OpenAI servisi başarıyla başlatıldı.");
                return Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "OpenAI istemcisi başlatılırken bir hata oluştu.");
                throw;
            }
        }

        /// <summary>
        /// Belirtilen isteğe göre OpenAI modelini kullanarak içerik üretir.
        /// </summary>
        /// <param name="request">İçerik üretim isteği için gerekli parametreleri içeren model.</param>
        /// <param name="cancellationToken">İşlemi iptal etmek için kullanılabilecek token.</param>
        /// <returns>Üretilen içeriği ve işlem detaylarını içeren bir yanıt nesnesi.</returns>
        public async Task<OpenAIContentResponse> GenerateContentAsync(OpenAIContentRequest request, CancellationToken cancellationToken)
        {
            if (!_isInitialized)
            {
                throw new InvalidOperationException("OpenAI servisi başlatılmamış. Lütfen önce InitializeAsync metodunu çağırın.");
            }

            var stopwatch = Stopwatch.StartNew();
            _logger.LogInformation("OpenAI içerik üretimi başlatılıyor. Model: {Model}", request.Model ?? "gpt-4.1");

            try
            {
                var chatRequest = new ChatRequest(
                    messages: new[] { new Message(Role.Developer, request.SystemInstruction ?? "", new Message(Role.User, request.Prompt)) },
                    model: request.Model ?? "gpt-4.1",
                    maxTokens: request.MaxTokens,
                    temperature: request.Temperature,
                    reasoningEffort: ReasoningEffort.Low
                );

                var result = await _openAIClient.ChatEndpoint.GetCompletionAsync(chatRequest, cancellationToken);

                stopwatch.Stop();
                _logger.LogInformation("OpenAI içerik üretimi tamamlandı. Süre: {Duration}ms", stopwatch.ElapsedMilliseconds);

                var response = new OpenAIContentResponse
                {
                    Success = true,
                    Text = result.FirstChoice.Message,
                    ProcessingTimeMs = stopwatch.ElapsedMilliseconds,
                    TokensUsed = result.Usage.TotalTokens
                };

                return response;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "OpenAI içerik üretimi sırasında bir hata oluştu.");

                return new OpenAIContentResponse
                {
                    Success = false,
                    ErrorMessage = ex.Message,
                    ProcessingTimeMs = stopwatch.ElapsedMilliseconds
                };
            }
        }
    }
}