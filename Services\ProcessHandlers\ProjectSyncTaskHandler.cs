using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using TranslationAgentServer.Data;
using TranslationAgentServer.Interfaces;
using TranslationAgentServer.Models;

namespace TranslationAgentServer.Services.ProcessHandlers
{
    /// <summary>
    /// Projeyi Google Sheets'ten senkronize etme görevini işler.
    /// </summary>
    public class ProjectSyncTaskHandler : ProcessHandlerBase
    {
        public ProjectSyncTaskHandler(
            IDatabaseService databaseService,
            IGoogleSheetsService googleSheetsService,
            IProjectService projectService,
            ITextService textService,
            ITermService termService,
            IContextService contextService,
            IGeminiService geminiService,
            IWebScraperService webScraperService,
            ILogger<ProjectSyncTaskHandler> logger,
            IProcessUpdateService processUpdateService) : base(
                databaseService,
                googleSheetsService,
                projectService,
                textService,
                termService,
                contextService,
                geminiService,
                webScraperService,
                logger,
                processUpdateService)
        {
        }

        /// <summary>
        /// Projeyi Google Sheets'ten senkronize eder
        /// </summary>
        private async Task SyncProjectFromGoogleSheets(Process process, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Proje senkronizasyon task'ı çalıştırılıyor: {ProcessId}", process.Id);

            try
            {
                var project = await _projectService.GetProjectByIdAsync(process.ProjectId);
                if (project == null)
                    throw new InvalidOperationException($"Proje bulunamadı: {process.ProjectId}");

                if (string.IsNullOrEmpty(project.SpreadsheetId))
                    throw new InvalidOperationException("Proje için Google Sheets bilgileri eksik (SpreadsheetId)");

                if (!_googleSheetsService.IsServiceInitialized())
                    throw new InvalidOperationException("Google Sheets servisi başlatılmamış");

                await SyncTextsAsync(process, project.SpreadsheetId, cancellationToken);
                await SyncTermsAsync(process, project.SpreadsheetId, cancellationToken);
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation("Proje senkronizasyon task'ı iptal edildi: {ProcessId}", process.Id);
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Proje senkronizasyon task'ı çalıştırılırken hata oluştu: {ProcessId}", process.Id);
                throw;
            }
        }

        /// <summary>
        /// Metinleri Google Sheets'ten senkronize eder
        /// </summary>
        private async Task SyncTextsAsync(Process process, string spreadsheetId, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Metinler senkronize ediliyor: {ProcessId}", process.Id);
            process.Result = "Metinler senkronize ediliyor...";
            process.Progress = 10;
            await UpdateProcess(process);

            var project = await _projectService.GetProjectByIdAsync(process.ProjectId);
            await using var mainclient = _databaseService.GetContext();
            var textsColumns = await mainclient.MainData.SingleAsync(x => x.Name == "texts_columns", cancellationToken);

            var options = new SheetProcessingOptions { HeaderTemplate = textsColumns.Value, SkipHeader = true };

            TextCreateDto RowMapper(System.Collections.Generic.List<string> row, System.Collections.Generic.List<string> headers)
            {
                var status = TextStatus.EN;
                var textStatus = _googleSheetsService.GetColumnValue(row, headers, "status");
                if (textStatus?.Equals("TR", StringComparison.OrdinalIgnoreCase) == true) status = TextStatus.TR;
                else if (textStatus?.Equals("DUPE", StringComparison.OrdinalIgnoreCase) == true) status = TextStatus.DUPE;
                else if (textStatus?.Equals("NULL", StringComparison.OrdinalIgnoreCase) == true) status = TextStatus.NULL;

                return new TextCreateDto
                {
                    RowID = int.TryParse(_googleSheetsService.GetColumnValue(row, headers, "row_id"), out var rowId) ? rowId : -1,
                    Namespace = _googleSheetsService.GetColumnValue(row, headers, "namespace"),
                    Key = _googleSheetsService.GetColumnValue(row, headers, "key"),
                    En = _googleSheetsService.GetColumnValue(row, headers, "en"),
                    Tr = _googleSheetsService.GetColumnValue(row, headers, "tr"),
                    Status = status
                };
            }

            var sheetData = await _googleSheetsService.ProcessSheetDataAsync(spreadsheetId, project.TextsTable, options, RowMapper);
            if (!sheetData.Success) throw new InvalidOperationException($"Google Sheets'ten metin verileri alınamadı: {sheetData.ErrorMessage}");

            var sheetTexts = sheetData.ProcessedItems;
            var dbTexts = await _textService.GetAllTextsAsync(process.ProjectId);

            var textsToAdd = sheetTexts.Where(st => st.RowID != -1 && !dbTexts.Any(dt => dt.RowID == st.RowID)).ToList();
            var textsToUpdate = sheetTexts.Where(st => st.RowID != -1 && dbTexts.Any(dt => dt.RowID == st.RowID)).ToList();
            var textsToDelete = dbTexts.Where(dt => dt.RowID != -1 && !sheetTexts.Any(st => st.RowID == dt.RowID)).ToList();

            if (textsToAdd.Any())
            {
                var addedtexts = await _textService.CreateTextsAsync(textsToAdd, process.ProjectId, cancellationToken);
                await _textService.AddTextsAsync(addedtexts, process.ProjectId, cancellationToken);
            }
            if (textsToUpdate.Any())
            {
                var updates = textsToUpdate.Select(st => (dbTexts.First(dt => dt.RowID == st.RowID).Id, new TextUpdateDto { En = st.En, Tr = st.Tr, Status = st.Status, Namespace = st.Namespace, Key = st.Key })).ToList();
                await _textService.UpdateTextsAsync(updates, process.ProjectId, cancellationToken);
            }
            if (textsToDelete.Any()) await _textService.DeleteTextsAsync(textsToDelete.Select(t => t.Id).ToList(), process.ProjectId);

            process.Result = $"Metinler senkronize edildi. Eklendi: {textsToAdd.Count}, Güncellendi: {textsToUpdate.Count}, Silindi: {textsToDelete.Count}";
            process.Progress = 50;
            await UpdateProcess(process);
        }

        /// <summary>
        /// Terimleri Google Sheets'ten senkronize eder
        /// </summary>
        private async Task SyncTermsAsync(Process process, string spreadsheetId, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Terimler senkronize ediliyor: {ProcessId}", process.Id);
            process.Result = "Terimler senkronize ediliyor...";
            process.Progress = 60;
            await UpdateProcess(process);

            var project = await _projectService.GetProjectByIdAsync(process.ProjectId);
            await using var mainclient = _databaseService.GetContext();
            var termsColumns = await mainclient.MainData.SingleAsync(x => x.Name == "terms_columns", cancellationToken);

            var options = new SheetProcessingOptions { HeaderTemplate = termsColumns.Value, SkipHeader = true };

            TermCreateDto RowMapper(System.Collections.Generic.List<string> row, System.Collections.Generic.List<string> headers)
            {
                var status = TermStatus.EN;
                var termStatus = _googleSheetsService.GetColumnValue(row, headers, "status");
                if (termStatus?.Equals("TR", StringComparison.OrdinalIgnoreCase) == true) status = TermStatus.TR;

                return new TermCreateDto
                {
                    RowId = int.TryParse(_googleSheetsService.GetColumnValue(row, headers, "row_id"), out var rowId) ? rowId : -1,
                    En = _googleSheetsService.GetColumnValue(row, headers, "en"),
                    Tr = _googleSheetsService.GetColumnValue(row, headers, "tr"),
                    Category = _googleSheetsService.GetColumnValue(row, headers, "category"),
                    Info = _googleSheetsService.GetColumnValue(row, headers, "info"),
                    Status = status
                };
            }

            var sheetData = await _googleSheetsService.ProcessSheetDataAsync(spreadsheetId, project.TermsTable, options, RowMapper);
            if (!sheetData.Success) throw new InvalidOperationException($"Google Sheets'ten terim verileri alınamadı: {sheetData.ErrorMessage}");

            var sheetTerms = sheetData.ProcessedItems;
            var dbTerms = await _termService.GetAllTermsAsync(process.ProjectId);

            var termsToAdd = sheetTerms.Where(st => st.RowId != -1 && !dbTerms.Any(dt => dt.RowId == st.RowId)).ToList();
            var termsToUpdate = sheetTerms.Where(st => st.RowId != -1 && dbTerms.Any(dt => dt.RowId == st.RowId)).ToList();
            var termsToDelete = dbTerms.Where(dt => dt.RowId != -1 && !sheetTerms.Any(st => st.RowId == dt.RowId)).ToList();

            if (termsToAdd.Any())
            {
                var addTerms = await _termService.CreateTermsAsync(termsToAdd, process.ProjectId, cancellationToken);
                await _termService.AddTermsAsync(addTerms, process.ProjectId, cancellationToken);
            }
            if (termsToUpdate.Any())
            {
                var updates = termsToUpdate.Select(st => (dbTerms.First(dt => dt.RowId == st.RowId).Id, new TermUpdateDto { En = st.En, Tr = st.Tr, Category = st.Category, Info = st.Info, Status = st.Status })).ToList();
                await _termService.UpdateTermsAsync(updates, process.ProjectId, cancellationToken);
            }
            if (termsToDelete.Any()) await _termService.DeleteTermsAsync(termsToDelete.Select(t => (long)t.Id).ToList(), process.ProjectId);

            process.Result = $"Terimler senkronize edildi. Eklendi: {termsToAdd.Count}, Güncellendi: {termsToUpdate.Count}, Silindi: {termsToDelete.Count}";
            process.Progress = 100;
            await UpdateProcess(process);
        }

        /// <summary>
        /// Proje senkronizasyon görevini yürütür.
        /// </summary>
        public override async Task ExecuteAsync(Process process, CancellationToken cancellationToken)
        {
            await SyncProjectFromGoogleSheets(process, cancellationToken);
        }
    }
}