using System.Linq.Expressions;
using System.Reflection;
using System.Text.RegularExpressions;
using TranslationAgentServer.Models.Filtering;

namespace TranslationAgentServer.Services;

/// <summary>
/// Genel filtreleme servisi - Query Object Pattern implementasyonu
/// </summary>
public static class FilteringService
{
    /// <summary>
    /// IQueryable üzerinde filtreleme uygular
    /// </summary>
    public static IQueryable<T> ApplyFilters<T>(IQueryable<T> query, FilterQuery filterQuery)
    {
        if (filterQuery?.Criteria == null || !filterQuery.Criteria.Any())
            return query;

        Expression<Func<T, bool>>? combinedExpression = null;

        foreach (var criteria in filterQuery.Criteria)
        {
            var expression = BuildExpression<T>(criteria);
            if (expression == null) continue;

            if (combinedExpression == null)
            {
                combinedExpression = expression;
            }
            else
            {
                combinedExpression = filterQuery.LogicalOperator == LogicalOperator.And
                    ? CombineExpressions(combinedExpression, expression, Expression.AndAlso)
                    : CombineExpressions(combinedExpression, expression, Expression.OrElse);
            }
        }

        if (combinedExpression != null)
        {
            query = query.Where(combinedExpression);
        }

        // Sıralama uygula
        if (!string.IsNullOrEmpty(filterQuery.OrderBy))
        {
            query = ApplyOrdering(query, filterQuery.OrderBy, filterQuery.SortDirection);
        }

        return query;
    }

    /// <summary>
    /// Liste üzerinde filtreleme uygular (in-memory)
    /// </summary>
    public static IEnumerable<T> ApplyFilters<T>(IEnumerable<T> items, FilterQuery filterQuery)
    {
        if (filterQuery?.Criteria == null || !filterQuery.Criteria.Any())
            return items;

        return items.Where(item => EvaluateItem(item, filterQuery));
    }

    /// <summary>
    /// Tek bir öğeyi filtreleme kriterlerine göre değerlendirir
    /// </summary>
    private static bool EvaluateItem<T>(T item, FilterQuery filterQuery)
    {
        if (filterQuery.Criteria == null || !filterQuery.Criteria.Any())
            return true;

        var results = new List<bool>();

        foreach (var criteria in filterQuery.Criteria)
        {
            var result = EvaluateCriteria(item, criteria);
            results.Add(result);
        }

        return filterQuery.LogicalOperator == LogicalOperator.And
            ? results.All(r => r)
            : results.Any(r => r);
    }

    /// <summary>
    /// Tek bir kriteri değerlendirir
    /// </summary>
    private static bool EvaluateCriteria<T>(T item, FilterCriteria criteria)
    {
        if (item == null) return false;

        var property = typeof(T).GetProperty(criteria.Field, BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);
        if (property == null) return false;

        var value = property.GetValue(item);
        return EvaluateValue(value, criteria);
    }

    /// <summary>
    /// Değeri kritere göre değerlendirir
    /// </summary>
    private static bool EvaluateValue(object? value, FilterCriteria criteria)
    {
        return criteria.Operator switch
        {
            FilterOperator.Equals => AreEqual(value, criteria.Value),
            FilterOperator.NotEquals => !AreEqual(value, criteria.Value),
            FilterOperator.Contains => ContainsValue(value, criteria.Value, criteria.CaseSensitive),
            FilterOperator.NotContains => !ContainsValue(value, criteria.Value, criteria.CaseSensitive),
            FilterOperator.StartsWith => StartsWithValue(value, criteria.Value, criteria.CaseSensitive),
            FilterOperator.EndsWith => EndsWithValue(value, criteria.Value, criteria.CaseSensitive),
            FilterOperator.Regex => MatchesRegex(value, criteria.Value, criteria.CaseSensitive),
            FilterOperator.GreaterThan => IsGreaterThan(value, criteria.Value),
            FilterOperator.GreaterThanOrEqual => IsGreaterThanOrEqual(value, criteria.Value),
            FilterOperator.LessThan => IsLessThan(value, criteria.Value),
            FilterOperator.LessThanOrEqual => IsLessThanOrEqual(value, criteria.Value),
            FilterOperator.Before => IsBefore(value, criteria.Value),
            FilterOperator.After => IsAfter(value, criteria.Value),
            FilterOperator.Between => IsBetween(value, criteria.Value, criteria.SecondValue),
            FilterOperator.IsNull => value == null,
            FilterOperator.IsNotNull => value != null,
            FilterOperator.In => IsInList(value, criteria.Value),
            FilterOperator.NotIn => !IsInList(value, criteria.Value),
            _ => false
        };
    }

    /// <summary>
    /// Expression oluşturur
    /// </summary>
    private static Expression<Func<T, bool>>? BuildExpression<T>(FilterCriteria criteria)
    {
        var parameter = Expression.Parameter(typeof(T), "x");
        var property = typeof(T).GetProperty(criteria.Field, BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);
        
        if (property == null) return null;

        var propertyAccess = Expression.Property(parameter, property);
        var constant = Expression.Constant(criteria.Value);

        Expression? comparison = criteria.Operator switch
        {
            FilterOperator.Equals => Expression.Equal(propertyAccess, constant),
            FilterOperator.NotEquals => Expression.NotEqual(propertyAccess, constant),
            FilterOperator.GreaterThan => Expression.GreaterThan(propertyAccess, constant),
            FilterOperator.GreaterThanOrEqual => Expression.GreaterThanOrEqual(propertyAccess, constant),
            FilterOperator.LessThan => Expression.LessThan(propertyAccess, constant),
            FilterOperator.LessThanOrEqual => Expression.LessThanOrEqual(propertyAccess, constant),
            FilterOperator.IsNull => Expression.Equal(propertyAccess, Expression.Constant(null)),
            FilterOperator.IsNotNull => Expression.NotEqual(propertyAccess, Expression.Constant(null)),
            _ => null
        };

        if (comparison == null) return null;

        return Expression.Lambda<Func<T, bool>>(comparison, parameter);
    }

    /// <summary>
    /// İki expression'ı birleştirir
    /// </summary>
    private static Expression<Func<T, bool>> CombineExpressions<T>(
        Expression<Func<T, bool>> left,
        Expression<Func<T, bool>> right,
        Func<Expression, Expression, Expression> combiner)
    {
        var parameter = Expression.Parameter(typeof(T), "x");
        var leftBody = ReplaceParameter(left.Body, left.Parameters[0], parameter);
        var rightBody = ReplaceParameter(right.Body, right.Parameters[0], parameter);
        var combined = combiner(leftBody, rightBody);
        return Expression.Lambda<Func<T, bool>>(combined, parameter);
    }

    /// <summary>
    /// Expression'da parametre değiştirir
    /// </summary>
    private static Expression ReplaceParameter(Expression expression, ParameterExpression oldParameter, ParameterExpression newParameter)
    {
        return new ParameterReplacer(oldParameter, newParameter).Visit(expression);
    }

    /// <summary>
    /// Sıralama uygular
    /// </summary>
    private static IQueryable<T> ApplyOrdering<T>(IQueryable<T> query, string orderBy, SortDirection direction)
    {
        var property = typeof(T).GetProperty(orderBy, BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);
        if (property == null) return query;

        var parameter = Expression.Parameter(typeof(T), "x");
        var propertyAccess = Expression.Property(parameter, property);
        var lambda = Expression.Lambda(propertyAccess, parameter);

        var methodName = direction == SortDirection.Ascending ? "OrderBy" : "OrderByDescending";
        var method = typeof(Queryable).GetMethods()
            .First(m => m.Name == methodName && m.GetParameters().Length == 2)
            .MakeGenericMethod(typeof(T), property.PropertyType);

        return (IQueryable<T>)method.Invoke(null, new object[] { query, lambda })!;
    }

    // Yardımcı metodlar
    private static bool AreEqual(object? value1, object? value2)
    {
        return Equals(value1, value2);
    }

    private static bool ContainsValue(object? value, object? searchValue, bool caseSensitive)
    {
        if (value?.ToString() == null || searchValue?.ToString() == null) return false;
        var comparison = caseSensitive ? StringComparison.Ordinal : StringComparison.OrdinalIgnoreCase;
        return value.ToString()!.Contains(searchValue.ToString()!, comparison);
    }

    private static bool StartsWithValue(object? value, object? searchValue, bool caseSensitive)
    {
        if (value?.ToString() == null || searchValue?.ToString() == null) return false;
        var comparison = caseSensitive ? StringComparison.Ordinal : StringComparison.OrdinalIgnoreCase;
        return value.ToString()!.StartsWith(searchValue.ToString()!, comparison);
    }

    private static bool EndsWithValue(object? value, object? searchValue, bool caseSensitive)
    {
        if (value?.ToString() == null || searchValue?.ToString() == null) return false;
        var comparison = caseSensitive ? StringComparison.Ordinal : StringComparison.OrdinalIgnoreCase;
        return value.ToString()!.EndsWith(searchValue.ToString()!, comparison);
    }

    private static bool MatchesRegex(object? value, object? pattern, bool caseSensitive)
    {
        if (value?.ToString() == null || pattern?.ToString() == null) return false;
        var options = caseSensitive ? RegexOptions.None : RegexOptions.IgnoreCase;
        try
        {
            return Regex.IsMatch(value.ToString()!, pattern.ToString()!, options);
        }
        catch
        {
            return false;
        }
    }

    private static bool IsGreaterThan(object? value, object? compareValue)
    {
        return CompareValues(value, compareValue) > 0;
    }

    private static bool IsGreaterThanOrEqual(object? value, object? compareValue)
    {
        return CompareValues(value, compareValue) >= 0;
    }

    private static bool IsLessThan(object? value, object? compareValue)
    {
        return CompareValues(value, compareValue) < 0;
    }

    private static bool IsLessThanOrEqual(object? value, object? compareValue)
    {
        return CompareValues(value, compareValue) <= 0;
    }

    private static bool IsBefore(object? value, object? compareValue)
    {
        if (value is DateTime dateValue && compareValue is DateTime compareDate)
            return dateValue < compareDate;
        return false;
    }

    private static bool IsAfter(object? value, object? compareValue)
    {
        if (value is DateTime dateValue && compareValue is DateTime compareDate)
            return dateValue > compareDate;
        return false;
    }

    private static bool IsBetween(object? value, object? minValue, object? maxValue)
    {
        return CompareValues(value, minValue) >= 0 && CompareValues(value, maxValue) <= 0;
    }

    private static bool IsInList(object? value, object? listValue)
    {
        if (listValue is IEnumerable<object> list)
            return list.Contains(value);
        return false;
    }

    private static int CompareValues(object? value1, object? value2)
    {
        if (value1 == null && value2 == null) return 0;
        if (value1 == null) return -1;
        if (value2 == null) return 1;

        if (value1 is IComparable comparable1 && value2 is IComparable)
        {
            try
            {
                return comparable1.CompareTo(value2);
            }
            catch
            {
                return 0;
            }
        }

        return 0;
    }
}

/// <summary>
/// Expression'da parametre değiştirme yardımcı sınıfı
/// </summary>
public class ParameterReplacer : ExpressionVisitor
{
    private readonly ParameterExpression _oldParameter;
    private readonly ParameterExpression _newParameter;

    public ParameterReplacer(ParameterExpression oldParameter, ParameterExpression newParameter)
    {
        _oldParameter = oldParameter;
        _newParameter = newParameter;
    }

    protected override Expression VisitParameter(ParameterExpression node)
    {
        return node == _oldParameter ? _newParameter : base.VisitParameter(node);
    }
}