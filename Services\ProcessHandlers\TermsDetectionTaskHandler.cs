using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using TranslationAgentServer.Data;
using TranslationAgentServer.Helpers;
using TranslationAgentServer.Interfaces;
using TranslationAgentServer.Models;
using TranslationAgentServer.Models.Responses;

namespace TranslationAgentServer.Services.ProcessHandlers
{
    /// <summary>
    /// Terim tespiti görevini işler.
    /// </summary>
    public class TermsDetectionTaskHandler : ProcessHandlerBase
    {
        public TermsDetectionTaskHandler(
            IDatabaseService databaseService,
            IGoogleSheetsService googleSheetsService,
            IProjectService projectService,
            ITextService textService,
            ITermService termService,
            IContextService contextService,
            IGeminiService geminiService,
            IWebScraperService webScraperService,
            ILogger<TermsDetectionTaskHandler> logger,
            IProcessUpdateService processUpdateService) : base(
                databaseService,
                googleSheetsService,
                projectService,
                textService,
                termService,
                contextService,
                geminiService,
                webScraperService,
                logger,
                processUpdateService)
        {
        }

        /// <summary>
        /// Terim tespiti task'ını çalıştırır
        /// </summary>
        public override async Task ExecuteAsync(Process process, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Terim tespiti task'ı çalıştırılıyor: {ProcessId}", process.Id);

            //Promptları al
            await LoadTermPrompts(process);

            //Worker sayısını al
            int workerCount = process.Settings?.RootElement.TryGetProperty("WorkerCount", out var workerCountProperty) == true &&
                                workerCountProperty.TryGetInt32(out var workerCountValue) ? workerCountValue : 1;

            _logger.LogInformation("Terim tespiti için {WorkerCount} worker kullanılacak.", workerCount);

            //Worker sayısı kadar taskı sıralayarak delay ile oluştur
            var tasks = new List<Task>();
            for (int i = 0; i < workerCount; i++)
            {
                var workerId = i + 1; // Worker ID'leri 1'den başlasın
                tasks.Add(Task.Run(async () =>
                {
                    await Task.Delay(workerId * 3000);
                    await DetectTermsWorkerAsync(workerId, process, cancellationToken);
                }, cancellationToken));
            }

            //Taskları sırayla çalıştırarak çalışmasını  sağla
            await Task.WhenAll(tasks);
        }

        /// <summary>
        /// Terim tespiti worker'ını çalıştırır
        /// </summary>
        private async Task DetectTermsWorkerAsync(int workerId, Process process, CancellationToken cancellationToken)
        {
            //SendCount verisini al
            var sendCount = process.Settings?.RootElement.TryGetProperty("SendCount", out var sendCountProperty) == true &&
            sendCountProperty.TryGetInt32(out var sendCountValue) ? sendCountValue : 10;


            _logger.LogInformation("Worker {WorkerId} başlatıldı.", workerId);

            while (true)
            {
                //Worker için kullanılacak metinleri ayır
                var textsForWorker = await process.Data.ExecuteAsync(async () =>
              {

                  var filteredQuery = FilteringService.ApplyFilters(process.Data.Client!.Texts.AsQueryable(), process.Data.TextFilter!);
                  var texts = await filteredQuery
                        .Where(t => !process.Data.IsUsing.Contains(t.Id) && !process.Data.IsProcessed.Contains(t.Id) && t.Status != (int)TextStatus.DUPE && t.Status != (int)TextStatus.NULL)
                        .Take(sendCount)
                        .ToListAsync();

                  foreach (var text in texts)
                  {
                      process.Data.IsUsing.Add(text.Id);

                  }

                  return texts;
              }, cancellationToken);

                cancellationToken.ThrowIfCancellationRequested();

                //Worker için kullanılacak metinler varsa tespit et
                if (textsForWorker.Count > 0)
                {
                    var iscontinue = false;
                    try
                    {
                        var betweenText = textsForWorker[0].Id + "-" + textsForWorker[^1].Id;

                        //Loglama
                        _logger.LogInformation("Worker {WorkerId} için {BetweenText} idleri arasındaki toplam {TextCount} metin ayrıldı.", workerId, betweenText, textsForWorker.Count);


                        var detectedTerms = await DetectTermsFromTextsAsync(process, workerId, textsForWorker, cancellationToken);
                        if (detectedTerms.Count == 0)
                        {
                            _logger.LogInformation("Worker {WorkerId} için tespit edilecek terim bulunamadı.", workerId);
                            iscontinue = true;
                            continue; // Terim tespit edilmediyse bir sonraki metne geç
                        }

                        cancellationToken.ThrowIfCancellationRequested();

                        var selectedTerms = await SelectTermsAsync(process, detectedTerms, cancellationToken);
                        if (selectedTerms.Count == 0)
                        {
                            _logger.LogInformation("Worker {WorkerId} için onaylanacak terim bulunamadı.", workerId);
                            iscontinue = true;
                            continue; // Onaylanacak terim yoksa bir sonraki metne geç
                        }

                        cancellationToken.ThrowIfCancellationRequested();

                        var confirmedTerms = await ConfirmTerm(process, workerId, selectedTerms, cancellationToken);
                        if (confirmedTerms.Count == 0)
                        {
                            _logger.LogInformation("Worker {WorkerId} için onaylanan terim bulunamadı.", workerId);
                            iscontinue = true;
                            continue; // Onaylanan terim yoksa bir sonraki metne geç
                        }

                        cancellationToken.ThrowIfCancellationRequested();

                        var result = await process.Data.ExecuteAsync(async () =>
                        {
                            var addedTerms = await _termService.CreateTermsAsync(confirmedTerms, process.ProjectId, cancellationToken);
                            var result = await _termService.AddTermsAsync(addedTerms, process.ProjectId, cancellationToken, process.Data.Client);
                            if (result > 0)
                            {
                                foreach (var text in textsForWorker)
                                {
                                    process.Data.IsProcessed.Add(text.Id);
                                }
                            }
                            return result;
                        }, cancellationToken);

                        if (result == 0)
                        {
                            throw new InvalidOperationException("Terimler veritabanına kaydedilemedi. Retriable Process");
                        }

                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Terim tespit ve onay işleminde hata meydana geldi.");
                        if (ex is not InvalidOperationException && !ex.Message.Contains("Retriable Process"))
                        {
                            throw;
                        }

                    }
                    finally
                    {
                        // Exception olsa bile mutlaka temizle
                        await process.Data.ExecuteAsync(() =>
                        {
                            foreach (var text in textsForWorker)
                            {
                                process.Data.IsUsing.Remove(text.Id);
                                if (!iscontinue)
                                {
                                    process.Data.IsProcessed.Add(text.Id);
                                }
                            }
                            return Task.CompletedTask;
                        }, cancellationToken);
                    }
                }

                else
                {
                    _logger.LogInformation("Worker {WorkerId} için işlenecek metin kalmadı.", workerId);
                    break; // İşlenecek metin kalmadıysa çık
                }
            }
            _logger.LogInformation("Worker {WorkerId} tamamlandı.", workerId);
        }

        /// <summary>
        /// Verilen terimleri çevirir
        /// </summary>
        /// <param name="terms">Terimler</param>
        /// <param name="projectId">Proje ID'si</param>
        /// <param name="cancellationToken">İptal token'ı</param>
        /// <returns>Çevrilmiş terimler</returns>
        private async Task<List<TermCreateDto>> ConfirmTerm(Process process, int workerID, Dictionary<string, List<Text>> terms, CancellationToken cancellationToken)
        {
            var translatedTerms = new List<TermCreateDto>();

            foreach (var term in terms)
            {
                cancellationToken.ThrowIfCancellationRequested();

                StringBuilder combinedText = new($"Terim:{term.Key}");
                combinedText.AppendLine();
                combinedText.AppendLine("Terimin geçtiği metinler:\n");

                var exampleTexts = term.Value;
                var includeProperty = process.Settings?.RootElement.TryGetProperty("IncludeNamespaceAndKey", out var incProperty) == true &&
                    incProperty.GetBoolean();

                foreach (var text in exampleTexts)
                {
                    if (includeProperty)
                    {
                        if (!string.IsNullOrEmpty(text!.Namespace))
                        {
                            combinedText.AppendLine($"Namespace: {text!.Namespace}");
                        }
                        if (!string.IsNullOrEmpty(text!.Key))
                        {
                            combinedText.AppendLine($"Key: {text!.Key}");
                        }
                    }
                    var en = await TextProcessingHelper.ProcessTextWithLemmaSearchAsync(text!.En, term.Key);
                    combinedText.AppendLine($"Metin: {en}");
                }

                GeminiContentRequest request = new()
                {
                    Prompt = combinedText.ToString(),
                    Model = process.Settings?.RootElement.TryGetProperty("Model", out var modelProperty) == true ? modelProperty.GetString() ?? "" : "",
                    SystemInstruction = process.TermPrompts.Confirm,
                    Temperature = (float)0.7,
                    ThinkingBudget = process.Settings?.RootElement.TryGetProperty("ThinkingBudget", out var thinkingBudgetProperty) == true &&
                    thinkingBudgetProperty.TryGetInt32(out var thinkingBudgetValue) ? thinkingBudgetValue : 0
                };

                var response = await _geminiService.GenerateContentAsync(request, cancellationToken, true);
                if (!response.Success)
                {
                    throw new Exception($"Translate request failed. Error: {response.ErrorMessage}");
                }


                try
                {
                    // ConfirmTermResponse modelini kullanarak AI yanıtını ayrıştır
                    var confirmTermResponse = JsonSerializer.Deserialize<ConfirmTermResponse>(response.JsonOutput!.ToString());
                    if (confirmTermResponse != null)
                    {
                        if (confirmTermResponse.ConfirmTerm)
                        {
                            _logger.LogInformation("Terim onaylandı: {Term}. Worker ID: {WorkerID}", term.Key, workerID);
                            var newTerm = new TermCreateDto
                            {
                                En = confirmTermResponse.Term ?? term.Key,
                                Status = TermStatus.EN
                            };
                            translatedTerms.Add(newTerm);
                        }
                        else
                        {
                            _logger.LogInformation("Terim onaylanmadı: {Term}. Worker ID: {WorkerID}", term.Key, workerID);
                        }
                    }
                    else
                    {
                        _logger.LogError("Gemini API response ConfirmTermResponse olarak parse edilemedi: {Response}", response.Text);
                    }

                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Gemini API response işleme hatası: {Response}", response.Text);
                    throw new InvalidOperationException($"Gemini API response işlenemedi: {ex.Message}. Retriable Process");
                }
                await Task.Delay(1000, cancellationToken); // Her terim için 1 saniye bekle

            }

            return translatedTerms;
        }

        /// <summary>
        /// Verilen terimleri onaylar ve gerekliyse çevirir
        /// </summary>
        /// <param name="terms">Terimler</param>
        /// <param name="cancellationToken">İptal token'ı</param>
        /// <returns>Onaylanan terimler</returns>
        private async Task<Dictionary<string, List<Text>>> SelectTermsAsync(Process process, List<string> terms, CancellationToken cancellationToken)
        {
            var selectedTerms = new Dictionary<string, List<Text>>();
            terms = terms.Select(t => TextProcessingHelper.TermCleanStopWords(t)).Distinct().ToList();
            foreach (var term in terms)
            {
                cancellationToken.ThrowIfCancellationRequested();

                var results = await process.Data.ExecuteAsync(async () =>
                {
                    var lemma = await TextProcessingHelper.ProcessTextToLemmaAsync(term);
                    var duplicatedTerms = await process.Data.Client!.Terms.AnyAsync(t => t.Lemma!.Equals(lemma));
                    if (duplicatedTerms)
                    {
                        return new List<Text>();
                    }
                    return await process.Data.Client!.Texts.Where(t => t.Status != (int)TextStatus.DUPE)
                    .Where(t => t.EnTsvector!.Matches(EF.Functions.PhraseToTsQuery("english", term))).Take(30).ToListAsync();

                }, cancellationToken);

                if (results.Count > 1)
                    selectedTerms.Add(term, results);
            }

            return selectedTerms;
        }


        /// <summary>
        /// Verilen metinlerden tespit edilen terimleri döndürür
        /// </summary>
        /// <param name="process">İşlem</param> 
        /// <param name="texts">Metinler</param>
        /// <param name="cancellationToken">İptal token'ı</param>
        /// <returns>Tespit edilen terimler</returns>
        /// <summary>
        /// AI dönüşünü DetectedTermsResponse ile parse ederek terim tespiti işlemini gerçekleştirir.
        /// </summary>
        private async Task<List<string>> DetectTermsFromTextsAsync(Process process, int workerId, List<Text> texts, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Worker {WorkerId} için terimler tespit ediliyor.", workerId);

            StringBuilder combinedText = new("Analiz etmen gereken metinler:\n");

            foreach (var text in texts)
            {
                cancellationToken.ThrowIfCancellationRequested();

                if (process.Settings?.RootElement.TryGetProperty("IncludeNamespaceAndKey", out var includeProperty) == true &&
                    includeProperty.GetBoolean())
                {
                    if (!string.IsNullOrEmpty(text!.Namespace))
                    {
                        combinedText.AppendLine($"Namespace: {text!.Namespace}");
                    }
                    if (!string.IsNullOrEmpty(text!.Key))
                    {
                        combinedText.AppendLine($"Key: {text!.Key}");
                    }
                }
                combinedText.AppendLine($"Metin: {text!.En}");

            }

            GeminiContentRequest request = new()
            {
                Prompt = combinedText.ToString(),
                Model = process.Settings?.RootElement.TryGetProperty("Model", out var modelProperty) == true ? modelProperty.GetString() ?? "" : "",
                SystemInstruction = process.TermPrompts.Detect,
                Temperature = (float)0.7,
                ThinkingBudget = process.Settings?.RootElement.TryGetProperty("ThinkingBudget", out var thinkingBudgetProperty) == true &&
                thinkingBudgetProperty.TryGetInt32(out var thinkingBudgetValue) ? thinkingBudgetValue : 0
            };

            var response = await _geminiService.GenerateContentAsync(request, cancellationToken, true);
            if (response.Success == false)
            {
                throw new InvalidOperationException("Gemini API'den yanıt alınamadı");
            }

            try
            {
                // AI dönüşünü DetectedTermsResponse ile parse et
                var detectedTermsResponse = JsonSerializer.Deserialize<DetectedTermsResponse>(response.JsonOutput!);
                if (detectedTermsResponse == null || detectedTermsResponse.Terms == null)
                {
                    _logger.LogError("Gemini API response DetectedTermsResponse olarak parse edilemedi: {Response}", response.Text);
                    throw new InvalidOperationException("Gemini API response DetectedTermsResponse olarak parse edilemedi. Retriable Process.");
                }
                return detectedTermsResponse.Terms;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Gemini API response işleme hatası: {Response}", response.Text);
                throw new InvalidOperationException($"Gemini API response işlenemedi: {ex.Message}. Retriable Process.");
            }
        }
    }
}