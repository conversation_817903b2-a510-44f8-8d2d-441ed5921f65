using System.Text.Json;

namespace TranslationAgentServer.Models.Filtering;

/// <summary>
/// Tek bir filtreleme kriteri
/// </summary>
public class FilterCriteria
{
    /// <summary>
    /// Filtrelenecek alan adı
    /// </summary>
    public string Field { get; set; } = string.Empty;
    
    /// <summary>
    /// Filtreleme operatörü
    /// </summary>
    public FilterOperator Operator { get; set; }
    
    /// <summary>
    /// Filtreleme değeri
    /// </summary>
    public object? Value { get; set; }
    
    /// <summary>
    /// Between operatörü için ikinci değer
    /// </summary>
    public object? SecondValue { get; set; }
    
    /// <summary>
    /// Büyük/küçük harf duyarlılığı (string operatörleri için)
    /// </summary>
    public bool CaseSensitive { get; set; } = false;
}

/// <summary>
/// Filtreleme kriterleri koleksiyonu
/// </summary>
public class FilterQuery
{
    /// <summary>
    /// Filtreleme kriterleri
    /// </summary>
    public List<FilterCriteria> Criteria { get; set; } = new();
    
    /// <summary>
    /// Kriterler arası mantıksal operatör (AND/OR)
    /// </summary>
    public LogicalOperator LogicalOperator { get; set; } = LogicalOperator.And;
    
    /// <summary>
    /// Sayfalama - sayfa numarası
    /// </summary>
    public int Page { get; set; } = 1;
    
    /// <summary>
    /// Sayfalama - sayfa boyutu
    /// </summary>
    public int PageSize { get; set; } = 50;
    
    /// <summary>
    /// Sıralama alanı
    /// </summary>
    public string? OrderBy { get; set; }
    
    /// <summary>
    /// Sıralama yönü
    /// </summary>
    public SortDirection SortDirection { get; set; } = SortDirection.Ascending;
    
    /// <summary>
    /// JSON string'den FilterQuery oluşturur
    /// </summary>
    public static FilterQuery FromJson(string json)
    {
        if (string.IsNullOrEmpty(json))
            return new FilterQuery();
            
        try
        {
            return JsonSerializer.Deserialize<FilterQuery>(json, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            }) ?? new FilterQuery();
        }
        catch
        {
            return new FilterQuery();
        }
    }
    
    /// <summary>
    /// JsonElement'den FilterQuery oluşturur
    /// </summary>
    public static FilterQuery FromJsonElement(JsonElement element)
    {
        try
        {
            return JsonSerializer.Deserialize<FilterQuery>(element.GetRawText(), new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            }) ?? new FilterQuery();
        }
        catch
        {
            return new FilterQuery();
        }
    }
}

/// <summary>
/// Mantıksal operatörler
/// </summary>
public enum LogicalOperator
{
    And,
    Or
}

/// <summary>
/// Sıralama yönü
/// </summary>
public enum SortDirection
{
    Ascending,
    Descending
}