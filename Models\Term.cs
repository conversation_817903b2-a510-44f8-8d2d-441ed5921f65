using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using NpgsqlTypes;
using Pgvector;


namespace TranslationAgentServer.Models
{
    /// <summary>
    /// yeni_schema.terms tablosunu temsil eden model sınıfı.
    /// Terim yönetimi ve çeviri işlemleri için kullanılır.
    /// </summary>

    [Table("terms")]
    public class Term
    {
        /// <summary>
        /// Terimin benzersiz kimlik numarası
        /// </summary>
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("id")]
        public int Id { get; set; }

        /// <summary>
        /// Google Sheets sayfa kimliği
        /// </summary>
        [Column("row_id")]

        public int RowId { get; set; }

        /// <summary>
        /// İngilizce terim metni
        /// </summary>
        [Column("en")]
        [Required]
        public string En { get; set; } = string.Empty;

        /// <summary>
        /// Türkçe çeviri metni
        /// </summary>
        [Column("tr")]
        public string? Tr { get; set; }

        /// <summary>
        /// Kategori
        /// </summary>
        [Column("category")]
        public string? Category { get; set; }

        /// <summary>
        /// Bilgi
        /// </summary>
        [Column("info")]
        public string? Info { get; set; }

        /// <summary>
        /// Lemmatized (kök) form
        /// </summary>
        [Column("lemma")]
        public string? Lemma { get; set; }

        /// <summary>
        /// Terimin durumu (EN: İngilizce, TR: Türkçe)
        /// </summary>
        [Column("status")]
        public int? Status { get; set; } = (int)TermStatus.EN;

        /// <summary>
        /// İngilizce metin için full-text search vektörü (otomatik oluşturulan)
        /// </summary>
        [Column("en_tsvector")]
        [DatabaseGenerated(DatabaseGeneratedOption.Computed)]
        public NpgsqlTsVector? EnTsvector { get; set; }


        /// <summary>
        /// AI embedding vektörü
        /// </summary>
        [Column("embedding", TypeName = "vector(768)")]
        public Vector? Embedding { get; set; }

        /// <summary>
        /// Kayıt oluşturulma tarihi
        /// </summary>
        [Column("created_at")]
        public DateTime? CreatedAt { get; set; }

        /// <summary>
        /// Son güncelleme tarihi
        /// </summary>
        [Column("updated_at")]
        public DateTime? UpdatedAt { get; set; }



        public override bool Equals(object? obj)
        {
            return obj is Term other && Id == other.Id;
        }

        public override int GetHashCode()
        {
            return Id.GetHashCode();
        }
    }

    /// <summary>
    /// Terim durumu enum'u
    /// </summary>
    public enum TermStatus
    {
        /// <summary>
        /// İngilizce terim
        /// </summary>
        EN,
        /// <summary>
        /// Türkçe terim
        /// </summary>
        TR
    }

    /// <summary>
    /// Yeni terim oluşturma için DTO
    /// </summary>
    public class TermCreateDto
    {
        /// <summary>
        /// Google Sheets sayfa kimliği
        /// </summary>
        [Required]
        public int RowId { get; set; }

        /// <summary>
        /// İngilizce terim
        /// </summary>
        [Required]
        public string En { get; set; } = string.Empty;

        /// <summary>
        /// Türkçe çeviri
        /// </summary>
        public string? Tr { get; set; }

        /// <summary>
        /// Kategori
        /// </summary>
        public string? Category { get; set; }

        /// <summary>
        /// Bilgi
        /// </summary>
        public string? Info { get; set; }

        /// <summary>
        /// Lemma
        /// </summary>
        public string? Lemma { get; set; }

        /// <summary>
        /// Durum
        /// </summary>
        public TermStatus? Status { get; set; }

        /// <summary>
        /// Embedding vektörü
        /// </summary>
        public Vector? Embedding { get; set; }
    }

    /// <summary>
    /// Terim güncelleme için DTO
    /// </summary>
    public class TermUpdateDto
    {
        /// <summary>
        /// Google Sheets sayfa kimliği
        /// </summary>
        public int? RowId { get; set; }

        /// <summary>
        /// İngilizce terim
        /// </summary>
        public string? En { get; set; }

        /// <summary>
        /// Türkçe çeviri
        /// </summary>
        public string? Tr { get; set; }

        /// <summary>
        /// Kategori
        /// </summary>
        public string? Category { get; set; }

        /// <summary>
        /// Bilgi
        /// </summary>
        public string? Info { get; set; }

        /// <summary>
        /// Lemma
        /// </summary>
        public string? Lemma { get; set; }

        /// <summary>
        /// Durum
        /// </summary>
        public TermStatus? Status { get; set; }

        /// <summary>
        /// Embedding vektörü
        /// </summary>
        public Vector? Embedding { get; set; }
    }

}