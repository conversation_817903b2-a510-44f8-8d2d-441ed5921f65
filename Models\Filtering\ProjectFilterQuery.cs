using System.Text.Json;

namespace TranslationAgentServer.Models.Filtering;

/// <summary>
/// Project modeli için <PERSON>tirilmiş filtreleme sorgusu
/// </summary>
public class ProjectFilterQuery : FilterQuery
{
    /// <summary>
    /// Proje adı filtresi
    /// </summary>
    public string? Name { get; set; }
    
    /// <summary>
    /// SpreadsheetId filtresi
    /// </summary>
    public string? SpreadsheetId { get; set; }
    
    /// <summary>
    /// TextsTable filtresi
    /// </summary>
    public string? TextsTable { get; set; }
    
    /// <summary>
    /// TermsTable filtresi
    /// </summary>
    public string? TermsTable { get; set; }
    
    /// <summary>
    /// SchemaName filtresi
    /// </summary>
    public string? SchemaName { get; set; }
    
    /// <summary>
    /// CreatedAt başlangıç tarihi
    /// </summary>
    public DateTime? CreatedAtFrom { get; set; }
    
    /// <summary>
    /// CreatedAt bitiş tarihi
    /// </summary>
    public DateTime? CreatedAtTo { get; set; }
    
    /// <summary>
    /// JSON string'den ProjectFilterQuery oluşturur
    /// </summary>
    public static new ProjectFilterQuery FromJson(string json)
    {
        if (string.IsNullOrEmpty(json))
            return new ProjectFilterQuery();
            
        try
        {
            return JsonSerializer.Deserialize<ProjectFilterQuery>(json, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            }) ?? new ProjectFilterQuery();
        }
        catch
        {
            return new ProjectFilterQuery();
        }
    }
    
    /// <summary>
    /// JsonElement'den ProjectFilterQuery oluşturur
    /// </summary>
    public static new ProjectFilterQuery FromJsonElement(JsonElement element)
    {
        try
        {
            return JsonSerializer.Deserialize<ProjectFilterQuery>(element.GetRawText(), new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            }) ?? new ProjectFilterQuery();
        }
        catch
        {
            return new ProjectFilterQuery();
        }
    }
}