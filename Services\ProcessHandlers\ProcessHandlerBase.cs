using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Threading;
using System.Threading.Tasks;
using TranslationAgentServer.Data;
using TranslationAgentServer.Interfaces;
using TranslationAgentServer.Models;

namespace TranslationAgentServer.Services.ProcessHandlers
{
    /// <summary>
    /// Tüm işlem görevi handler'ları için temel sınıf.
    /// Ortak metodları ve bağımlılıkları sağlar.
    /// </summary>
    public abstract class ProcessHandlerBase : IProcessTaskHandler
    {
        protected readonly IDatabaseService _databaseService;
        protected readonly IGoogleSheetsService _googleSheetsService;
        protected readonly IProjectService _projectService;
        protected readonly ITextService _textService;
        protected readonly ITermService _termService;
        protected readonly IContextService _contextService;
        protected readonly IGeminiService _geminiService;
        protected readonly IWebScraperService _webScraperService;
        protected readonly ILogger _logger;
        protected readonly IProcessUpdateService _processUpdateService;

        protected ProcessHandlerBase(
            IDatabaseService databaseService,
            IGoogleSheetsService googleSheetsService,
            IProjectService projectService,
            ITextService textService,
            ITermService termService,
            IContextService contextService,
            IGeminiService geminiService,
            IWebScraperService webScraperService,
            ILogger logger,
            IProcessUpdateService processUpdateService)
        {
            _databaseService = databaseService;
            _googleSheetsService = googleSheetsService;
            _projectService = projectService;
            _textService = textService;
            _termService = termService;
            _contextService = contextService;
            _geminiService = geminiService;
            _webScraperService = webScraperService;
            _logger = logger;
            _processUpdateService = processUpdateService;
        }

        /// <summary>
        /// Belirli bir işlem görevini yürütür.
        /// </summary>
        /// <param name="process">Yürütülecek işlem.</param>
        /// <param name="cancellationToken">İptal token'ı.</param>
        /// <returns>Task</returns>
        public abstract Task ExecuteAsync(Process process, CancellationToken cancellationToken);

        /// <summary>
        /// İşlem ilerlemesini ve durumunu veritabanında günceller.
        /// </summary>
        /// <param name="existingProcess">Güncellenecek işlem.</param>
        /// <returns>Task</returns>
        protected async Task UpdateProcess(Process existingProcess)
        {
            await _processUpdateService.UpdateProcess(existingProcess.Id, process =>
            {
                process.Status = existingProcess.Status;
                process.Progress = existingProcess.Progress;
                process.Result = existingProcess.Result;
                process.LastPing = DateTime.UtcNow;
                process.CompletedAt = (int)process.Status == (int)ProcessStatus.Completed || (int)process.Status == (int)ProcessStatus.Failed || (int)process.Status == (int)ProcessStatus.Cancelled
                    ? DateTime.UtcNow : process.CompletedAt;
            });
        }

        /// <summary>
        /// Terim işlemleri için promptları veri tabanından çeker
        /// </summary>
        protected async Task LoadTermPrompts(Process process)
        {
            await using var client = _databaseService.GetContext();

            process.TermPrompts.Detect = (await client.MainData.SingleAsync(x => x.Name == "term_detect_prompt")).Value;
            process.TermPrompts.Confirm = (await client.MainData.SingleAsync(x => x.Name == "term_confirm_prompt")).Value;
            process.TermPrompts.Translate = (await client.MainData.SingleAsync(x => x.Name == "term_translate_prompt")).Value;


            var mainContext = await _projectService.GetProjectByIdAsync(process.ProjectId);
            if (mainContext != null)
            {
                process.TermPrompts.Detect = process.TermPrompts.Detect.Replace("{mainContext}", mainContext.MainContext!);
                process.TermPrompts.Confirm = process.TermPrompts.Confirm.Replace("{mainContext}", mainContext.MainContext!);
                process.TermPrompts.Translate = process.TermPrompts.Translate.Replace("{mainContext}", mainContext.MainContext!);
            }

        }

        /// <summary>
        /// Metin işlemleri için promptları veri tabanından çeker
        /// </summary>
        protected async Task LoadTextPrompts(Process process)
        {
            await using var client = _databaseService.GetContext();
            process.TextPrompts.Group = (await client.MainData.SingleAsync(x => x.Name == "text_group_prompt")).Value;
            process.TextPrompts.Translate = (await client.MainData.SingleAsync(x => x.Name == "text_translate_prompt")).Value;

            // Yeni eklenen prompt özellikleri
            var textPromptsJson = (await client.MainData.SingleAsync(x => x.Name == "text_prompts")).Value;
            var prompts = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, string>>(textPromptsJson);
            if (prompts != null)
            {
                process.TextPrompts.UiSystemMechanics = prompts.ContainsKey("UiSystemMechanics") ? prompts["UiSystemMechanics"] : null;
                process.TextPrompts.SequentialDialogue = prompts.ContainsKey("SequentialDialogue") ? prompts["SequentialDialogue"] : null;
                process.TextPrompts.MixedDialogue = prompts.ContainsKey("MixedDialogue") ? prompts["MixedDialogue"] : null;
                process.TextPrompts.InGameContent = prompts.ContainsKey("InGameContent") ? prompts["InGameContent"] : null;
                process.TextPrompts.StoryLore = prompts.ContainsKey("StoryLore") ? prompts["StoryLore"] : null;
                process.TextPrompts.MixedOther = prompts.ContainsKey("MixedOther") ? prompts["MixedOther"] : null;
            }

            int mainContextID = process.Settings?.RootElement.TryGetProperty("mainContextId", out var mainContextIdProperty) == true &&
                                mainContextIdProperty.TryGetInt32(out var mainContextId) ? mainContextId : -1;
            if (mainContextID > -1)
            {
                var mainContext = await _contextService.GetContextByIdAsync(mainContextID, process.ProjectId);
                if (mainContext != null)
                {
                    process.TextPrompts.Group = process.TextPrompts.Group.Replace("{mainContext}", mainContext.Content);
                    process.TextPrompts.Translate = process.TextPrompts.Translate.Replace("{mainContext}", mainContext.Content);
                }
            }

        }

        /// <summary>
        /// Context işlemleri için promptları veri tabanından çeker
        /// </summary>
        protected async Task LoadContextPrompts(Process process)
        {
            await using var client = _databaseService.GetContext();
            process.ContextPrompts.Create = (await client.MainData.SingleAsync(x => x.Name == "context_create_prompt")).Value;
        }
    }
}