using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using NpgsqlTypes;
using Pgvector;


namespace TranslationAgentServer.Models;

/// <summary>
/// Context veri modeli
/// </summary>
[Table("contexts")]
public class Context
{
    /// <summary>
    /// <PERSON><PERSON>iz kimlik
    /// </summary>
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    [Column("id")]
    public int Id { get; set; }

    /// <summary>
    /// Kategori bilgisi
    /// </summary>
    [Column("category")]
    public string? Category { get; set; }

    /// <summary>
    /// Başlık bilgisi
    /// </summary>
    [Column("title")]
    public string? Title { get; set; }

    /// <summary>
    /// İçerik bilgisi
    /// </summary>
    [Column("content")]
    [Required]
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// Web scraping ID referansı
    /// </summary>
    [Column("scrap_id")]
    public int? ScrapId { get; set; }

    [ForeignKey("ScrapId")]
    public WebScrap? Scrap { get; set; }

    /// <summary>
    /// Otomatik oluşturulan combined text
    /// </summary>
    [Column("combined_text")]
    [DatabaseGenerated(DatabaseGeneratedOption.Computed)]
    public string? CombinedText { get; set; }

    /// <summary>
    /// Birleştirilmiş tsvector (otomatik oluşturulan)
    /// </summary>
    [Column("combined_tsvector")]
    [DatabaseGenerated(DatabaseGeneratedOption.Computed)]
    public NpgsqlTsVector? CombinedTsvector { get; set; }


    /// <summary>
    /// Embedding vektörü
    /// </summary>
    [Column("embedding", TypeName = "vector(768)")]
    public Vector? Embedding { get; set; }

    /// <summary>
    /// Oluşturulma tarihi
    /// </summary>
    [Column("created_at")]
    public DateTime? CreatedAt { get; set; }

    /// <summary>
    /// Güncellenme tarihi
    /// </summary>
    [Column("updated_at")]
    public DateTime? UpdatedAt { get; set; }

    public override bool Equals(object? obj)
    {
        return obj is Context other && Id == other.Id;
    }

    public override int GetHashCode()
    {
        return Id.GetHashCode();
    }
}

/// <summary>
/// Context oluşturma DTO'su
/// </summary>
public class ContextCreateDto
{
    /// <summary>
    /// Kategori bilgisi
    /// </summary>
    [StringLength(255, ErrorMessage = "Kategori en fazla 255 karakter olabilir.")]
    public string? Category { get; set; }

    /// <summary>
    /// Başlık bilgisi
    /// </summary>
    [StringLength(500, ErrorMessage = "Başlık en fazla 500 karakter olabilir.")]
    public string? Title { get; set; }

    /// <summary>
    /// İçerik bilgisi
    /// </summary>
    [Required(ErrorMessage = "İçerik alanı zorunludur.")]
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// Web scraping ID referansı
    /// </summary>
    public int? ScrapId { get; set; }

    /// <summary>
    /// Embedding vektörü (opsiyonel)
    /// </summary>
    public Vector? Embedding { get; set; }
}

/// <summary>
/// Context güncelleme DTO'su
/// </summary>
public class ContextUpdateDto
{
    /// <summary>
    /// Kategori bilgisi
    /// </summary>
    [StringLength(255, ErrorMessage = "Kategori en fazla 255 karakter olabilir.")]
    public string? Category { get; set; }

    /// <summary>
    /// Başlık bilgisi
    /// </summary>
    [StringLength(500, ErrorMessage = "Başlık en fazla 500 karakter olabilir.")]
    public string? Title { get; set; }

    /// <summary>
    /// İçerik bilgisi
    /// </summary>
    public string? Content { get; set; }

    /// <summary>
    /// Web scraping ID referansı
    /// </summary>
    public int? ScrapId { get; set; }

    /// <summary>
    /// Embedding vektörü
    /// </summary>
    public Vector? Embedding { get; set; }
}

/// <summary>
/// Context istatistikleri modeli
/// </summary>
public class ContextStatistics
{
    /// <summary>
    /// Toplam context sayısı
    /// </summary>
    public int TotalContexts { get; set; }

    /// <summary>
    /// Embedding'i olan context sayısı
    /// </summary>
    public int ContextsWithEmbedding { get; set; }

    /// <summary>
    /// Kategorilere göre dağılım
    /// </summary>
    public Dictionary<string, int> CategoryDistribution { get; set; } = new();

    /// <summary>
    /// En son oluşturulan context tarihi
    /// </summary>
    public DateTime? LastCreatedAt { get; set; }

    /// <summary>
    /// En son güncellenen context tarihi
    /// </summary>
    public DateTime? LastUpdatedAt { get; set; }
}