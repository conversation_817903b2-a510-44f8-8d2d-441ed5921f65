using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;
using Newtonsoft.Json;
using TranslationAgentServer.Helpers;

namespace TranslationAgentServer.Models;

/// <summary>
/// Terim çevirilerinin AI model sonuçlarını saklayan model sınıfı
/// Read-only erişim için tasarlanmıştır
/// </summary>
public class TermTranslation
{
    /// <summary>
    /// Benzersiz kimlik
    /// </summary>
    [Key]
    public long Id { get; set; }

    /// <summary>
    ///  terms tablosundaki id ile ilişkili
    /// </summary>
    public int TermId { get; set; }

    [ForeignKey("TermId")]
    public Term? Term { get; set; }

    /// <summary>
    /// Kullanılan AI model adı
    /// </summary>
    public string? AiModel { get; set; }

    /// <summary>
    /// AI modeline gönderilen prompt
    /// </summary>
    public string? Prompt { get; set; }

    /// <summary>
    /// AI modelinden dönen ham yanıt
    /// </summary>
    public string? Response { get; set; }

    /// <summary>
    /// Yapılandırılmış JSON sonuç
    /// </summary>
    public string? Result { get; set; }

    /// <summary>
    /// Kullanılan token sayısı
    /// </summary>
    [Required]
    public int TokenCount { get; set; } = 0;


    /// <summary>
    /// İşlenme tarihi
    /// </summary>
    [Required]
    public DateTime ProcessedAt { get; set; } = DateTime.UtcNow;

    public override bool Equals(object? obj)
    {
        return obj is TermTranslation other && Id == other.Id;
    }

    public override int GetHashCode()
    {
        return Id.GetHashCode();
    }
}