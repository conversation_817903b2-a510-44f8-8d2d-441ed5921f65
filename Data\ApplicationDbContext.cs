using Microsoft.EntityFrameworkCore;
using TranslationAgentServer.Models;

namespace TranslationAgentServer.Data;

/// <summary>
/// Ana veritabanı bağlamı sınıfı
/// PostgreSQL veritabanı ile Entity Framework Core entegrasyonu sağlar
/// </summary>
public class ApplicationDbContext : DbContext
{
    /// <summary>
    /// ApplicationDbContext constructor
    /// </summary>
    /// <param name="options">DbContext seçenekleri</param>
    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
    {
    }

    /// <summary>
    /// Projeler tablosu
    /// </summary>
    public DbSet<Project> Projects { get; set; }

    /// <summary>
    /// İşlemler tablosu
    /// </summary>
    public DbSet<Process> Processes { get; set; }

    /// <summary>
    /// Ana veriler tablosu
    /// </summary>
    public DbSet<MainData> MainData { get; set; }

    // Dinamik tablolar (Context, Text, Term) artık DynamicSchemaDbContext'te yönetiliyor
    // Bu tablolar projeye göre farklı şemalarda bulunacak

    /// <summary>
    /// Model yapılandırması
    /// </summary>
    /// <param name="modelBuilder">Model oluşturucu</param>
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Project tablosu yapılandırması - public şemada sabit

        modelBuilder.Entity<Project>(entity =>
        {
            entity.ToTable("projects", "public");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.CreatedAt).HasColumnName("created_at");
            entity.Property(e => e.Name).HasColumnName("name").HasMaxLength(255);
            entity.Property(e => e.SpreadsheetId).HasColumnName("spreadsheet_id").HasMaxLength(500);
            entity.Property(e => e.TextsTable).HasColumnName("texts_table").HasMaxLength(255);
            entity.Property(e => e.TermsTable).HasColumnName("terms_table").HasMaxLength(255);
            entity.Property(e => e.MainContext).HasColumnName("main_context");
            entity.Property(e => e.SchemaName).HasColumnName("schema_name").HasMaxLength(255);
            entity.Property(e => e.Settings)
                .HasColumnName("settings")
                .HasColumnType("jsonb");
        });

        // Process tablosu yapılandırması - public şemada sabit
        modelBuilder.Entity<Process>(entity =>
        {
            entity.ToTable("processes", "public");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.CreatedAt).HasColumnName("created_at");
            entity.Property(e => e.ProjectId).HasColumnName("project_id");
            entity.Property(e => e.Progress).HasColumnName("progress");
            entity.Property(e => e.Status).HasColumnName("status");
            entity.Property(e => e.CompletedAt).HasColumnName("completed_at");
            entity.Property(e => e.Result).HasColumnName("result");
            entity.Property(e => e.ErrorMessage).HasColumnName("error_message");
            entity.Property(e => e.TaskType).HasColumnName("task_type");
            entity.Property(e => e.LastPing).HasColumnName("last_ping");
            entity.Property(e => e.Settings)
                .HasColumnName("settings")
                .HasColumnType("jsonb");
        });

        // MainData tablosu yapılandırması - public şemada sabit
        modelBuilder.Entity<MainData>(entity =>
        {
            entity.ToTable("main", "public");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.CreatedAt).HasColumnName("created_at");
            entity.Property(e => e.Name).HasColumnName("name").HasMaxLength(255);
            entity.Property(e => e.Value).HasColumnName("value");
        });

        // Dinamik tablo yapılandırmaları (Context, Text, Term) artık DynamicSchemaDbContext'te yapılıyor
        // Bu tablolar projeye göre farklı şemalarda yönetilecek
    }


}