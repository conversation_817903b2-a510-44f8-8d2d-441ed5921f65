using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Pgvector;
using Pgvector.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using TranslationAgentServer.Data;
using TranslationAgentServer.Helpers;
using TranslationAgentServer.Interfaces;
using TranslationAgentServer.Models;
using TranslationAgentServer.Models.Responses;

namespace TranslationAgentServer.Services.ProcessHandlers
{
    /// <summary>
    /// Terim çevirisi görevini işler.
    /// </summary>
    public class TermsTranslationTaskHandler : ProcessHandlerBase
    {
        public TermsTranslationTaskHandler(
            IDatabaseService databaseService,
            IGoogleSheetsService googleSheetsService,
            IProjectService projectService,
            ITextService textService,
            ITermService termService,
            IContextService contextService,
            IGeminiService geminiService,
            IWebScraperService webScraperService,
            ILogger<TermsTranslationTaskHandler> logger,
            IProcessUpdateService processUpdateService) : base(
                databaseService,
                googleSheetsService,
                projectService,
                textService,
                termService,
                contextService,
                geminiService,
                webScraperService,
                logger,
                processUpdateService)
        {
        }

        /// <summary>
        /// Terim çevirisi task'ını çalıştırır
        /// </summary>
        public override async Task ExecuteAsync(Process process, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Terim çevirisi task'ı çalıştırılıyor: {ProcessId}", process.Id);

            //Promptları al
            await LoadTermPrompts(process);

            //Worker sayısını al
            int workerCount = process.Settings?.RootElement.TryGetProperty("WorkerCount", out var workerCountProperty) == true &&
                                workerCountProperty.TryGetInt32(out var workerCountValue) ? workerCountValue : 1;

            _logger.LogInformation("Terim tespiti için {WorkerCount} worker kullanılacak.", workerCount);

            //Worker sayısı kadar taskı sıralayarak delay ile oluştur
            var tasks = new List<Task>();
            for (int i = 0; i < workerCount; i++)
            {
                var workerId = i + 1; // Worker ID'leri 1'den başlasın
                tasks.Add(Task.Run(async () =>
                {
                    await Task.Delay(workerId * 3000);
                    await TranslateTermsWorkerAsync(workerId, process, cancellationToken);
                }, cancellationToken));
            }

            //Taskları sırayla çalıştırarak çalışmasını  sağla
            await Task.WhenAll(tasks);


        }

        private async Task TranslateTermsWorkerAsync(int workerId, Process process, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Worker {WorkerId} başlatıldı.", workerId);
            while (true)
            {
                cancellationToken.ThrowIfCancellationRequested();
                var iscontinue = false;
                (List<Term> similarTerms, List<Context> contexts, List<Text> exampleTexts) termData = new();
                //Worker için kullanılacak terimleri ayır
                var termForWorker = await process.Data.ExecuteAsync(async () =>
              {

                  var term = await process.Data.Client.Terms
                        .Where(t => !process.Data.IsUsing.Contains(t.Id) && !process.Data.IsProcessed.Contains(t.Id) &&
                        t.Status == (int)TermStatus.EN)
                        .FirstOrDefaultAsync(cancellationToken);

                  if (term == null)
                  {
                      return null;
                  }
                  else
                  {
                      process.Data.IsUsing.Add(term.Id);
                  }

                  termData = await GetTermDataAsync(process, workerId, term, cancellationToken);

                  foreach (var similarterm in termData.similarTerms.Where(t => t.Status == (int)TermStatus.EN))
                  {
                      process.Data.IsUsing.Add(similarterm.Id);
                  }

                  return term;
              }, cancellationToken);


                cancellationToken.ThrowIfCancellationRequested();

                if (termForWorker != null)
                {
                    try
                    {
                        await TranslateTermAsync(process, workerId, termForWorker, termData, cancellationToken);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Terim çevirisi worker {WorkerId} için terim çevirme hatası: {TermId}", workerId, termForWorker.Id);
                        if (ex is not InvalidOperationException && !ex.Message.Contains("Retriable Process"))
                        {
                            throw;
                        }
                    }
                    finally
                    {
                        // Exception olsa bile mutlaka temizle
                        await process.Data.ExecuteAsync(() =>
                        {
                            process.Data.IsUsing.Remove(termForWorker.Id);

                            foreach (var similarterm in termData.similarTerms.Where(t => t.Status == (int)TermStatus.EN))
                            {
                                process.Data.IsUsing.Remove(similarterm.Id);
                            }

                            if (iscontinue)
                            {
                                process.Data.IsProcessed.Add(termForWorker.Id);
                            }
                            return Task.CompletedTask;
                        }, cancellationToken);
                    }


                }

                else
                {
                    _logger.LogInformation("Worker {WorkerId} için işlenecek terim kalmadı.", workerId);
                    break; // İşlenecek terim kalmadıysa çık
                }

            }
            _logger.LogInformation("Worker {WorkerId} tamamlandı.", workerId);
        }

        private async Task<(List<Term> similarTerms, List<Context> contexts, List<Text> exampleTexts)> GetTermDataAsync(Process process, int workerId, Term termForWorker, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Worker {WorkerId} terim verilerini alıyor: {TermId}", workerId, termForWorker.Id);
            try
            {
                cancellationToken.ThrowIfCancellationRequested();
                var similarTerms = new List<Term>();
                var exampleTexts = new List<Text>();
                var contexts = new List<Context>();
                if (termForWorker.EnTsvector!.Count > 0)
                {

                    similarTerms = await process.Data.Client!.Terms.Where(t => t.Status != (int)TermStatus.TR && t.Id != termForWorker.Id && t.EnTsvector != null)
                    .Where(t => t.EnTsvector!.Matches(EF.Functions.PhraseToTsQuery("english", termForWorker.En!)) ||
                    termForWorker.EnTsvector!.Matches(EF.Functions.PhraseToTsQuery("english", t.En!))).Take(15).ToListAsync();

                    exampleTexts = await process.Data.Client!.Texts.Where(t => t.Status != (int)TextStatus.DUPE && t.EnTsvector != null)
                    .Where(t => t.EnTsvector!.Matches(EF.Functions.PhraseToTsQuery("english", termForWorker.En!))).Take(15).ToListAsync();

                    contexts = await process.Data.Client!.Contexts.Where(t => t.CombinedTsvector!.Matches(EF.Functions.PhraseToTsQuery("english", termForWorker.En!))).Take(5).ToListAsync();

                }
                else
                {
                    similarTerms = await process.Data.Client!.Terms.Where(t => t.Status != (int)TermStatus.TR && t.Id != termForWorker.Id)
                    .Where(t => EF.Functions.ILike(t.Lemma, $"%{termForWorker.Lemma}%") ||
                    EF.Functions.ILike(termForWorker.Lemma, $"%{t.Lemma}%")).Take(15).ToListAsync();

                    exampleTexts = await process.Data.Client!.Texts.Where(t => t.Status != (int)TextStatus.DUPE)
                    .Where(t => EF.Functions.ILike(t.Lemma, $"%{termForWorker.Lemma}%") ||
                    EF.Functions.ILike(termForWorker.Lemma, $"%{t.Lemma}%")).Take(15).ToListAsync();

                    contexts = await process.Data.Client!.Contexts.Where(t => EF.Functions.ILike(t.CombinedText, $"%{termForWorker.En}%"))
                    .Take(5).ToListAsync();

                }
                var contextsStage2 = await process.Data.Client!.Contexts.Where(t => t.Embedding != null)
                .Where(t => t.Embedding!.CosineDistance(termForWorker.Embedding!) < 0.15f)
                .OrderBy(t => t.Embedding!.CosineDistance(termForWorker.Embedding!))
                .Take(5).ToListAsync();

                contexts = contexts.Union(contextsStage2).ToList();


                return (similarTerms, contexts, exampleTexts);
            }
            catch (Exception ex)

            {
                _logger.LogError(ex, "Worker {WorkerId} için terim verileri alınamadı: {TermId}", workerId, termForWorker.Id);
                throw;

            }
        }
        /// <summary>
        /// AI dönüşünü TermTranslationResponse ile parse ederek terim çevirisi işlemini gerçekleştirir.
        /// </summary>
        private async Task TranslateTermAsync(Process process, int workerId, Term termForWorker, (List<Term> similarTerms, List<Context> contexts, List<Text> exampleTexts) termDatas, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Worker {WorkerId} terimi çeviriyor: {TermId}", workerId, termForWorker.Id);
            try
            {
                StringBuilder combinedText = new("Çevrilecek terim: " + termForWorker.En + "\n");

                var includeProperty = process.Settings?.RootElement.TryGetProperty("IncludeNamespaceAndKey", out var incProperty) == true &&
                   incProperty.GetBoolean();

                combinedText.AppendLine();
                combinedText.AppendLine("Terimin geçtiği metinler:");
                foreach (var text in termDatas.exampleTexts)
                {
                    if (includeProperty)
                    {
                        if (!string.IsNullOrEmpty(text!.Namespace))
                        {
                            combinedText.AppendLine($"Namespace: {text!.Namespace}");
                        }
                        if (!string.IsNullOrEmpty(text!.Key))
                        {
                            combinedText.AppendLine($"Key: {text!.Key}");
                        }
                    }
                    var en = await TextProcessingHelper.ProcessTextWithLemmaSearchAsync(text!.En, termForWorker.En!);
                    combinedText.AppendLine(en);
                    combinedText.AppendLine("------");
                }
                if (termDatas.similarTerms.Count > 0)
                {
                    combinedText.AppendLine();
                    combinedText.AppendLine("Benzer terimler:\n");
                    foreach (var term in termDatas.similarTerms)
                    {
                        combinedText.AppendLine("Terim: " + term.En);
                        if (term.Status == (int)TermStatus.TR)
                        {
                            combinedText.AppendLine("Çeviri: " + term.Tr);
                            combinedText.AppendLine("Kategori: " + term.Category);
                            combinedText.AppendLine("Bilgi: " + term.Info);
                        }
                        combinedText.AppendLine("------");
                    }
                }
                if (termDatas.contexts.Count > 0)
                {
                    combinedText.AppendLine();
                    combinedText.AppendLine("Bağlamsal içerikler:\n");
                    foreach (var context in termDatas.contexts)
                    {
                        combinedText.AppendLine($"{context.Category} - {context.Title}");
                        combinedText.AppendLine(context.Content);
                        combinedText.AppendLine("------");
                    }
                }

                var request = new GeminiContentRequest
                {
                    Prompt = combinedText.ToString(),
                    Model = process.Settings?.RootElement.TryGetProperty("Model", out var modelProperty) == true ? modelProperty.GetString() ?? "" : "",
                    SystemInstruction = process.TermPrompts.Translate,
                    Temperature = (float)1.3,
                    ThinkingBudget = process.Settings?.RootElement.TryGetProperty("ThinkingBudget", out var thinkingBudgetProperty) == true &&
                    thinkingBudgetProperty.TryGetInt32(out var thinkingBudgetValue) ? thinkingBudgetValue : 0
                };

                var response = await _geminiService.GenerateContentAsync(request, cancellationToken, true);
                if (!response.Success)
                {
                    throw new InvalidOperationException($"Translate request failed. Error: {response.ErrorMessage}. Retriable Process.");
                }

                try
                {
                    // AI dönüşünü TermTranslationResponse ile parse et
                    var termTranslationResponse = JsonSerializer.Deserialize<TermTranslationResponse>(response.JsonOutput!.ToString());
                    if (termTranslationResponse == null)
                    {
                        throw new InvalidOperationException("Gemini API response TermTranslationResponse olarak parse edilemedi. Retriable Process");
                    }

                    termForWorker.Tr = termTranslationResponse.Translation;
                    termForWorker.Status = (int)TermStatus.TR;
                    termForWorker.Category = termTranslationResponse.Category;
                    termForWorker.Info = termTranslationResponse.Info;
                    await process.Data.ExecuteAsync(async () =>
                                   {
                                       var termTranslations = await process.Data.Client!.TermTranslations.Where(t => t.TermId == termForWorker.Id).FirstOrDefaultAsync();
                                       if (termTranslations != null)
                                       {
                                           process.Data.Client!.TermTranslations.Remove(termTranslations);
                                       }
                                       TermTranslation termTranslation = new()
                                       {
                                           TermId = termForWorker.Id,
                                           AiModel = response.Model,
                                           Prompt = request.Prompt,
                                           Response = response.Text,
                                           TokenCount = response.TokensUsed ?? 0,
                                           Result = response.JsonOutput!,
                                           ProcessedAt = DateTime.UtcNow
                                       };

                                       process.Data.IsProcessed.Add(termForWorker.Id);

                                       await process.Data.Client!.TermTranslations.AddAsync(termTranslation);
                                       await process.Data.Client!.SaveChangesAsync();

                                   }, cancellationToken);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Gemini API response işleme hatası: {Response}", response.Text);
                    throw;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Worker {WorkerId} için terim çevirme hatası: {TermId}", workerId, termForWorker.Id);
                if (ex is not InvalidOperationException && !ex.Message.Contains("Retriable Process"))
                {
                    throw;
                }
            }
        }
    }
}