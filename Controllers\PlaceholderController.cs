using Microsoft.AspNetCore.Mvc;
using TranslationAgentServer.Interfaces;
using TranslationAgentServer.Models;
using System.Threading.Tasks;
using System.Collections.Generic;

namespace TranslationAgentServer.Controllers
{
    /// <summary>
    /// Placeholder tespiti ve yönetimi için API endpoint'lerini içerir.
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class PlaceholderController : ControllerBase
    {
        private readonly IPlaceholderService _placeholderService;
        private readonly IProjectService _projectService;

        /// <summary>
        /// PlaceholderController sınıfının bir örneğini oluşturur.
        /// </summary>
        /// <param name="placeholderService">Placeholder işlemleri servisi.</param>
        /// <param name="projectService">Proje yönetimi servisi.</param>
        public PlaceholderController(IPlaceholderService placeholderService, IProjectService projectService)
        {
            _placeholderService = placeholderService;
            _projectService = projectService;
        }

        /// <summary>
        /// Belirtilen proje için placeholder'ları tespit eder ve gruplanmış sonuçları döndürür.
        /// </summary>
        /// <param name="request">Proje ID'sini ve isteğe bağlı özel kalıpları içeren istek gövdesi.</param>
        /// <returns>Tespit edilen ve gruplanan placeholder'ların sözlüğü.</returns>
        [HttpPost("detect")]
        public async Task<IActionResult> DetectPlaceholders([FromBody] PlaceholderDetectionRequest request)
        {

            var project = await _projectService.GetProjectByIdAsync(request.ProjectId);
            if (project == null)
            {
                return NotFound($"Project with ID '{request.ProjectId}' not found.");
            }

            var result = await _placeholderService.DetectPlaceholdersAsync(project, request.CustomPatterns);

            return Ok(result);
        }

        /// <summary>
        /// Onaylanmış placeholder gruplarını işler ve veritabanına kaydeder.
        /// </summary>
        /// <param name="request">Proje ID'sini ve onaylanmış grupları içeren istek.</param>
        /// <returns>İşlem başarı durumunu belirten bir mesaj.</returns>
        [HttpPost("process")]
        public async Task<IActionResult> ProcessPlaceholders([FromBody] ProcessPlaceholdersRequest request)
        {
            await _placeholderService.ProcessPlaceholdersAsync(request.ProjectId, request.ApprovedGroups);
            return Ok("Placeholders processed and saved successfully.");
        }
    }
}

namespace TranslationAgentServer.Models
{
    /// <summary>
    /// Placeholder tespiti için istek modelini temsil eder.
    /// </summary>
    /// <param name="ProjectId">İşlemin yapılacağı projenin kimliği.</param>
    /// <param name="CustomPatterns">Varsayılanların üzerine eklenecek özel placeholder kalıpları.</param>
    public record PlaceholderDetectionRequest(int ProjectId, List<CustomPatternDto>? CustomPatterns);

    /// <summary>
    /// Onaylanmış placeholder gruplarını işlemek için istek modelini temsil eder.
    /// </summary>
    /// <param name="ProjectId">İşlemin yapılacağı projenin kimliği.</param>
    /// <param name="ApprovedGroups">Onaylanmış ve gruplanmış placeholder'ları içeren sözlük.</param>
    public record ProcessPlaceholdersRequest(int ProjectId, Dictionary<string, List<string>> ApprovedGroups);
}