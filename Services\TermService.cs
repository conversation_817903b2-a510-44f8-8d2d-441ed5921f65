using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Pgvector;
using TranslationAgentServer.Helpers;
using TranslationAgentServer.Interfaces;
using TranslationAgentServer.Models;
using TranslationAgentServer.Models.Filtering;
using System.Text.Json;
using TranslationAgentServer.Data;


namespace TranslationAgentServer.Services
{
    /// <summary>
    /// Term servisi implementasyonu
    /// Terim verilerinin CRUD işlemlerini ve özel sorguları gerçekleştirir
    /// </summary>
    public class TermService : ITermService
    {
        private readonly IDatabaseService _databaseService;
        private readonly ILogger<TermService> _logger;
        private readonly INlpService _nlpService;
        private readonly IGeminiService _geminiService;

        public TermService(IDatabaseService databaseService, ILogger<TermService> logger, INlpService nlpService, IGeminiService geminiService)
        {
            _databaseService = databaseService;
            _logger = logger;
            _nlpService = nlpService;
            _geminiService = geminiService;
        }

        public async Task<List<Term>> GetAllTermsAsync(int projectId)
        {
            try
            {
                await using var client = _databaseService.GetProjectContext(projectId);
                var result = await client.Terms.ToListAsync();
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Terimler getirilirken hata oluştu. Proje: {ProjectId}", projectId);
                throw;
            }
        }

        public async Task<List<Term>> GetAllTermsAsync(int projectId, TermFilterQuery filterQuery)
        {
            try
            {
                await using var client = _databaseService.GetProjectContext(projectId);
                var query = client.Terms.AsQueryable();


                var filteredQuery = FilteringService.ApplyFilters(query, filterQuery);


                return await filteredQuery.ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Gelişmiş filtreleme ile terimler getirilirken hata oluştu. Proje: {ProjectId}", projectId);
                throw;
            }
        }


        public async Task<List<Term>> GetAllTermsAsync(int projectId, JsonElement filterElement)
        {
            try
            {
                var filterQuery = TermFilterQuery.FromJsonElement(filterElement);
                return await GetAllTermsAsync(projectId, filterQuery);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "JsonElement filtreleme ile terimler getirilirken hata oluştu. Proje: {ProjectId}", projectId);
                throw;
            }
        }

        public async Task<Term?> GetTermByIdAsync(int id, int projectId)
        {
            try
            {
                await using var client = _databaseService.GetProjectContext(projectId);
                var response = await client.Terms
                    .Where(x => x.Id == id)
                    .SingleAsync();

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Terim getirilirken hata oluştu. ID: {Id}, Şema: {ProjectId}", id, projectId);
                throw;
            }
        }

        public async Task<TermTranslation?> GetTermTranslationsAsync(int id, int projectId)
        {
            try
            {
                await using var client = _databaseService.GetProjectContext(projectId);


                // İlgili çeviri geçmişini getir (varsa)
                var translation = await client.TermTranslations
                    .Where(x => x.Id == id) // Term ID ile eşleşen translation
                    .OrderByDescending(x => x.ProcessedAt)
                    .FirstOrDefaultAsync();

                if (translation == null)
                    return null;
                return translation;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Terim ve çeviri geçmişi getirilirken hata oluştu. ID: {Id}, Proje: {ProjectId}", id, projectId);
                throw;
            }
        }

        public async Task<TermStatistics> GetTermStatisticsAsync(int projectId)
        {
            try
            {
                await using var client = _databaseService.GetProjectContext(projectId);

                var allTerms = await client.Terms.ToListAsync();
                var terms = allTerms;

                var statistics = new TermStatistics
                {
                    TotalTerms = terms.Count,
                    TranslatedTerms = terms.Count(t => !string.IsNullOrEmpty(t.Tr)),
                    UntranslatedTerms = terms.Count(t => string.IsNullOrEmpty(t.Tr)),
                    TermsWithEmbedding = terms.Count(t => t.Embedding != null),
                    TermsWithoutEmbedding = terms.Count(t => t.Embedding == null),
                    EnglishTerms = terms.Count(t => t.Status == (int)TermStatus.EN),
                    TurkishTerms = terms.Count(t => t.Status == (int)TermStatus.TR)
                };

                return statistics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Terim istatistikleri alınırken hata oluştu. Şema: {projectId}", projectId);
                throw;
            }
        }

        public async Task<Term?> CreateTermAsync(TermCreateDto termCreateDto, int projectId)
        {
            try
            {
                var term = new Term
                {
                    RowId = termCreateDto.RowId,
                    En = termCreateDto.En,
                    Tr = termCreateDto.Tr,
                    Category = termCreateDto.Category,
                    Info = termCreateDto.Info,
                    Status = (int)termCreateDto.Status,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };
                if (!string.IsNullOrEmpty(termCreateDto.En))
                {
                    term.Lemma = await _nlpService.ProcessTextToLemmaAsync(termCreateDto.En);
                    var embeddingResponse = await _geminiService.GetEmbeddingAsync(termCreateDto.En);
                    if (embeddingResponse.Success)
                    {
                        term.Embedding = new Vector(embeddingResponse.Embedding);
                    }
                    else
                    {
                        throw new Exception($"Embedding oluşturulamadı. Hata: {embeddingResponse.ErrorMessage}");
                    }
                }
                return term;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Terim oluşturulurken hata oluştu. Şema: {projectId}", projectId);
                throw;
            }
        }

        public async Task<Term?> AddTermAsync(Term termCreateDto, int projectId)
        {
            try
            {
                await using var client = _databaseService.GetProjectContext(projectId);
                var response = await client.Terms.AddAsync(termCreateDto);
                var result = await client.SaveChangesAsync();

                return result > 0 ? response.Entity : null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Terim oluşturulurken hata oluştu. Şema: {projectId}", projectId);
                throw;
            }
        }

        public async Task<List<Term>> CreateTermsAsync(List<TermCreateDto> termCreateDtos, int projectId, CancellationToken cancellationToken)
        {
            try
            {
                await using var client = _databaseService.GetProjectContext(projectId);

                var terms = new List<Term>();
                foreach (var dto in termCreateDtos)
                {
                    cancellationToken.ThrowIfCancellationRequested();
                    var addterm = new Term();
                    addterm.RowId = dto.RowId;
                    addterm.En = dto.En;
                    addterm.Tr = dto.Tr;
                    addterm.Lemma = await _nlpService.ProcessTextToLemmaAsync(dto.En);
                    addterm.Category = dto.Category;
                    addterm.Info = dto.Info;
                    addterm.Status = (int)dto.Status;

                    addterm.CreatedAt = DateTime.UtcNow;
                    addterm.UpdatedAt = DateTime.UtcNow;

                    terms.Add(addterm);
                }


                var toEmbedTexts = terms.Where(t => t.Embedding == null).ToList();

                var batchSize = 100;
                for (int i = 0; i < toEmbedTexts.Count; i += batchSize)
                {
                    var batch = toEmbedTexts.Skip(i).Take(batchSize).ToList();

                    // Retry mekanizması ile embedding üretimi
                    var embeddingsResponse = await _geminiService.GetEmbeddingsAsync(batch.Select(t => t.En!).ToList(), cancellationToken);

                    if (!embeddingsResponse.Success)
                    {
                        throw new Exception($"Embedding oluşturulamadı. Hata: {embeddingsResponse.ErrorMessage}");
                    }

                    for (int j = 0; j < batch.Count; j++)
                    {
                        batch[j].Embedding = new Vector(embeddingsResponse.Embeddings[j]);
                    }
                    cancellationToken.ThrowIfCancellationRequested();
                    // Batch'ler arası bekleme süresi (rate limiting için)
                    await Task.Delay(3500);
                }

                return terms;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Çoklu terim oluşturulurken hata oluştu. Şema: {projectId}", projectId);
                throw;
            }
        }


        public async Task<int> AddTermsAsync(List<Term> termCreateDtos, int projectId, CancellationToken cancellationToken, DynamicSchemaDbContext useClient = null)
        {
            try
            {
                DynamicSchemaDbContext client;
                if (useClient == null)
                {
                    client = _databaseService.GetProjectContext(projectId);
                }
                else
                {
                    client = useClient;
                }
                var terms = new List<Term>();
                foreach (var dto in termCreateDtos)
                {
                    cancellationToken.ThrowIfCancellationRequested();
                    var addterm = new Term();
                    addterm.RowId = dto.RowId;
                    addterm.En = dto.En;
                    addterm.Tr = dto.Tr;
                    addterm.Lemma = await _nlpService.ProcessTextToLemmaAsync(dto.En);
                    addterm.Category = dto.Category;
                    addterm.Info = dto.Info;
                    addterm.Status = (int)dto.Status;
                    addterm.Embedding = null;
                    addterm.CreatedAt = DateTime.UtcNow;
                    addterm.UpdatedAt = DateTime.UtcNow;

                    terms.Add(addterm);
                }


                var toEmbedTexts = terms.Where(t => t.Embedding == null).ToList();

                var batchSize = 100;
                for (int i = 0; i < toEmbedTexts.Count; i += batchSize)
                {
                    var batch = toEmbedTexts.Skip(i).Take(batchSize).ToList();

                    // Retry mekanizması ile embedding üretimi
                    var embeddingsResponse = await _geminiService.GetEmbeddingsAsync(batch.Select(t => t.En!).ToList(), cancellationToken);

                    if (!embeddingsResponse.Success)
                    {
                        throw new Exception($"Embedding oluşturulamadı. Hata: {embeddingsResponse.ErrorMessage}");
                    }

                    for (int j = 0; j < batch.Count; j++)
                    {
                        batch[j].Embedding = new Vector(embeddingsResponse.Embeddings[j]);
                    }
                    cancellationToken.ThrowIfCancellationRequested();
                    // Batch'ler arası bekleme süresi (rate limiting için)
                    await Task.Delay(3500);
                }

                await using var transaction = await client.Database.BeginTransactionAsync(cancellationToken);
                try
                {
                    await client.Terms.AddRangeAsync(terms, cancellationToken);
                    await client.SaveChangesAsync(cancellationToken);
                    await transaction.CommitAsync(cancellationToken);
                }
                catch
                {
                    await transaction.RollbackAsync(cancellationToken);
                    throw;
                }
                finally
                {
                    if (useClient == null)
                    {
                        await client.DisposeAsync();
                    }
                }

                return terms.Count;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Çoklu terim eklerken hata oluştu. Şema: {projectId}", projectId);

                throw;
            }
        }


        public async Task<Term?> UpdateTermAsync(int id, TermUpdateDto termUpdateDto, int projectId)
        {
            try
            {
                var existingTerm = await GetTermByIdAsync(id, projectId);
                if (existingTerm == null)
                {
                    return null;
                }

                if (termUpdateDto.En != existingTerm.En && !string.IsNullOrEmpty(termUpdateDto.En))
                {
                    existingTerm.Lemma = await _nlpService.ProcessTextToLemmaAsync(termUpdateDto.En);
                    var embeddingResponse = await _geminiService.GetEmbeddingAsync(termUpdateDto.En);
                    if (embeddingResponse.Success)
                    {
                        existingTerm.Embedding = new Vector(embeddingResponse.Embedding);
                    }
                    else
                    {
                        throw new Exception($"Embedding oluşturulamadı. Hata: {embeddingResponse.ErrorMessage}");
                    }
                }

                if (termUpdateDto.RowId.HasValue) existingTerm.RowId = termUpdateDto.RowId.Value;
                if (termUpdateDto.En != null) existingTerm.En = termUpdateDto.En;
                if (termUpdateDto.Tr != null) existingTerm.Tr = termUpdateDto.Tr;
                if (termUpdateDto.Category != null) existingTerm.Category = termUpdateDto.Category;
                if (termUpdateDto.Info != null) existingTerm.Info = termUpdateDto.Info;
                if (termUpdateDto.Status.HasValue) existingTerm.Status = (int)termUpdateDto.Status.Value;
                existingTerm.UpdatedAt = DateTime.UtcNow;


                await using var client = _databaseService.GetProjectContext(projectId);
                existingTerm = client.Terms.Update(existingTerm).Entity;
                var result = await client.SaveChangesAsync();

                return result > 0 ? existingTerm : null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Terim güncellenirken hata oluştu. ID: {Id}, Şema: {ProjectId}", id, projectId);
                throw;
            }
        }


        public async Task<int> UpdateTermsAsync(List<(int Id, TermUpdateDto UpdateDto)> termUpdates, int projectId, CancellationToken cancellationToken)
        {
            try
            {
                await using var client = _databaseService.GetProjectContext(projectId);
                var updatedTerms = new List<Term>();

                foreach (var (id, updateDto) in termUpdates)
                {
                    var existingTerm = await GetTermByIdAsync(id, projectId);
                    if (existingTerm == null)
                    {
                        continue;
                    }

                    if (updateDto.En != existingTerm.En && !string.IsNullOrEmpty(updateDto.En))
                    {
                        existingTerm.Lemma = await _nlpService.ProcessTextToLemmaAsync(updateDto.En);
                    }
                    if (updateDto.En != existingTerm.En)
                    {
                        existingTerm.Embedding = null;
                    }

                    if (updateDto.RowId.HasValue) existingTerm.RowId = updateDto.RowId.Value;
                    if (updateDto.En != null) existingTerm.En = updateDto.En;
                    if (updateDto.Tr != null) existingTerm.Tr = updateDto.Tr;
                    if (updateDto.Category != null) existingTerm.Category = updateDto.Category;
                    if (updateDto.Info != null) existingTerm.Info = updateDto.Info;
                    if (updateDto.Status.HasValue) existingTerm.Status = (int)updateDto.Status.Value;
                    existingTerm.UpdatedAt = DateTime.UtcNow;
                    updatedTerms.Add(existingTerm);
                }

                var toEmbedTexts = updatedTerms.Where(t => t.Embedding == null).ToList();

                var batchSize = 100;
                for (int i = 0; i < toEmbedTexts.Count; i += batchSize)
                {
                    var batch = toEmbedTexts.Skip(i).Take(batchSize).ToList();
                    // Retry mekanizması ile embedding üretimi
                    var embeddingsResponse = await _geminiService.GetEmbeddingsAsync(batch.Select(t => t.En!).ToList(), cancellationToken);

                    if (!embeddingsResponse.Success)
                    {
                        throw new Exception($"Embedding oluşturulamadı. Hata: {embeddingsResponse.ErrorMessage}");
                    }

                    for (int j = 0; j < batch.Count; j++)
                    {
                        batch[j].Embedding = new Vector(embeddingsResponse.Embeddings[j]);
                    }

                    // Batch'ler arası bekleme süresi (rate limiting için)
                    await Task.Delay(3500);
                }

                await using var transaction = await client.Database.BeginTransactionAsync(cancellationToken);
                try
                {
                    client.Terms.UpdateRange(updatedTerms);
                    await client.SaveChangesAsync(cancellationToken);
                    await transaction.CommitAsync(cancellationToken);
                }
                catch
                {
                    await transaction.RollbackAsync(cancellationToken);
                    throw;
                }

                return updatedTerms.Count;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Terimler güncellenirken hata oluştu. Şema: {ProjectId}", projectId);
                throw;
            }
        }


        public async Task<bool> DeleteTermAsync(int id, int projectId)
        {
            try
            {
                await using var client = _databaseService.GetProjectContext(projectId);
                var deletedterm = await client.Terms.Where(x => x.Id == id).SingleAsync();
                if (deletedterm == null)
                {
                    return false;
                }
                client.Terms.Remove(deletedterm);
                var result = await client.SaveChangesAsync();

                return result > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Terim silinirken hata oluştu. ID: {Id}, Şema: {ProjectId}", id, projectId);
                throw;
            }
        }

        public async Task<int> DeleteTermsAsync(List<long> ids, int projectId)
        {
            try
            {
                await using var client = _databaseService.GetProjectContext(projectId);
                var deletedCount = 0;

                foreach (var id in ids)
                {
                    var deletedterm = await client.Terms.Where(x => x.Id == id).SingleAsync();
                    if (deletedterm == null)
                    {
                        continue;
                    }
                    client.Terms.Remove(deletedterm);
                    deletedCount++;
                }
                var result = await client.SaveChangesAsync();

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Çoklu terim silinirken hata oluştu. Şema: {projectId}", projectId);
                throw;
            }
        }


        private static float CalculateCosineSimilarity(float[] vectorA, float[] vectorB)
        {
            if (vectorA.Length != vectorB.Length)
                return 0f;

            float dotProduct = 0f;
            float magnitudeA = 0f;
            float magnitudeB = 0f;

            for (int i = 0; i < vectorA.Length; i++)
            {
                dotProduct += vectorA[i] * vectorB[i];
                magnitudeA += vectorA[i] * vectorA[i];
                magnitudeB += vectorB[i] * vectorB[i];
            }

            magnitudeA = (float)Math.Sqrt(magnitudeA);
            magnitudeB = (float)Math.Sqrt(magnitudeB);

            if (magnitudeA == 0f || magnitudeB == 0f)
                return 0f;

            return dotProduct / (magnitudeA * magnitudeB);
        }
    }
}