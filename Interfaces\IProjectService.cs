using TranslationAgentServer.Models;
using TranslationAgentServer.Models.Filtering;
using System.Text.Json;

namespace TranslationAgentServer.Interfaces;

/// <summary>
/// Proje yönetimi için servis arayüzü
/// CRUD operasyonları ve proje verilerini yönetir
/// </summary>
public interface IProjectService
{
    /// <summary>
    /// Tüm projeleri listeler
    /// </summary>
    /// <returns>Proje listesi</returns>
    Task<IEnumerable<Project>> GetAllProjectsAsync();

    /// <summary>
    /// Gelişmiş filtreleme ile projeleri getirir
    /// </summary>
    /// <param name="filterQuery">Filtreleme sorgusu</param>
    /// <returns>Filtrelenmiş proje listesi</returns>
    Task<IEnumerable<Project>> GetAllProjectsAsync(ProjectFilterQuery filterQuery);


    /// <summary>
    /// JsonElement ile filtreleme (backend kullanımı için)
    /// </summary>
    /// <param name="filterElement">JsonElement formatında filtreleme sorgusu</param>
    /// <returns>Filtrelenmiş proje listesi</returns>
    Task<IEnumerable<Project>> GetAllProjectsAsync(JsonElement filterElement);

    /// <summary>
    /// Belirtilen ID'ye sahip projeyi getirir
    /// </summary>
    /// <param name="id">Proje kimliği</param>
    /// <returns>Proje bilgileri veya null</returns>
    Task<Project?> GetProjectByIdAsync(int id);

    /// <summary>
    /// Yeni proje oluşturur
    /// </summary>
    /// <param name="projectDto">Proje bilgileri</param>
    /// <returns>Oluşturulan proje</returns>
    Task<Project> CreateProjectAsync(ProjectDto projectDto);

    /// <summary>
    /// Mevcut projeyi günceller
    /// </summary>
    /// <param name="id">Proje kimliği</param>
    /// <param name="projectUpdateDto">Güncellenecek proje bilgileri</param>
    /// <returns>Güncellenmiş proje veya null</returns>
    Task<Project?> UpdateProjectAsync(int id, ProjectUpdateDto projectUpdateDto);

    /// <summary>
    /// Projeyi siler
    /// </summary>
    /// <param name="id">Proje kimliği</param>
    /// <returns>Silme işleminin başarılı olup olmadığı</returns>
    Task<bool> DeleteProjectAsync(int id);

    /// <summary>
    /// Proje adına göre arama yapar
    /// </summary>
    /// <param name="name">Aranacak proje adı</param>
    /// <returns>Eşleşen projeler</returns>
    Task<IEnumerable<Project>> SearchProjectsByNameAsync(string name);

    /// <summary>
    /// Belirtilen Google Sheets ID'sine sahip projeyi bulur
    /// </summary>
    /// <param name="spreadsheetId">Google Sheets ID</param>
    /// <returns>Proje bilgileri veya null</returns>
    Task<Project?> GetProjectBySpreadsheetIdAsync(string spreadsheetId);


}