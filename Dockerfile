# Render.com için .NET Core Web API Dockerfile
# Playwright desteği ile

FROM mcr.microsoft.com/dotnet/sdk:9.0
WORKDIR /app

# Playwright için sistem bağımlılıkları

# Playwright için gerekli sistem bağımlılıklarını yükle
RUN apt-get update && apt-get install -y \
    wget \
    gnupg \
    ca-certificates \
    fonts-liberation \
    libgbm1 \
    libasound2 \
    libatk-bridge2.0-0 \
    libatk1.0-0 \
    libatspi2.0-0 \
    libcups2 \
    libdbus-1-3 \
    libdrm2 \
    libgtk-3-0 \
    libnspr4 \
    libnss3 \
    libwayland-client0 \
    libxcomposite1 \
    libxdamage1 \
    libxfixes3 \
    libxkbcommon0 \
    libxrandr2 \
    libxss1 \
    xvfb \
    && rm -rf /var/lib/apt/lists/*


# Önce sadece proje dosyalarını kopyala (cache optimization)
COPY *.csproj ./
RUN dotnet restore

# Sonra tüm kaynak kodları kopyala
COPY . ./

# Uygulamayı build et
RUN dotnet build -c Release

# Playwright CLI'yi global olarak yükle
RUN dotnet tool install --global Microsoft.Playwright.CLI
ENV PATH="${PATH}:/root/.dotnet/tools"

# Playwright tarayıcılarını yükle
RUN playwright install chromium
RUN playwright install-deps chromium

# Final publish
RUN dotnet publish -c Release -o out

# Render.com konfigürasyonu
EXPOSE 8080
ENV ASPNETCORE_URLS=http://+:8080
ENV ASPNETCORE_ENVIRONMENT=Production

# Playwright environment variables
ENV PLAYWRIGHT_BROWSERS_PATH=/root/.cache/ms-playwright
ENV PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD=false

# Uygulamayı başlat
ENTRYPOINT ["dotnet", "out/TranslationAgentServer.dll"]