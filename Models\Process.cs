using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using TranslationAgentServer.Data;
using TranslationAgentServer.Helpers;
using TranslationAgentServer.Interfaces;
using TranslationAgentServer.Models.Filtering;


namespace TranslationAgentServer.Models;

/// <summary>
/// Proje işlemlerini takip eder ve yönetir
/// </summary>

[Table("processes")]
public class Process
{
    /// <summary>
    /// İşlemin benzersiz UUID kimliği
    /// </summary>
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    [Column("id")]
    public Guid Id { get; set; }

    /// <summary>
    /// İşlemin oluşturulma tarihi
    /// </summary>
    [Column("created_at")]
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Bağlı olduğu proje kimliği
    /// </summary>
    [Column("project_id")]
    public int ProjectId { get; set; }

    [ForeignKey("ProjectId")]
    public Project? Project { get; set; }

    /// <summary>
    /// ilerleme
    /// </summary>
    [Column("progress")]
    public int Progress { get; set; }

    /// <summary>
    /// İşlem durumu (waiting, in_progress, completed, failed, paused, cancelled)
    /// </summary>
    [Column("status")]
    public int Status { get; set; } = (int)ProcessStatus.Pending;

    /// <summary>
    /// İşlemin tamamlanma zamanı
    /// </summary>
    [Column("completed_at")]
    public DateTime? CompletedAt { get; set; }

    /// <summary>
    /// İşlem sonucu
    /// </summary>
    [Column("result")]
    public string? Result { get; set; }

    /// <summary>
    /// Hata mesajı (varsa)
    /// </summary>
    [Column("error_message")]
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// İşlem türü (translation, proofreading, import, export)
    /// </summary>
    [Column("task_type")]
    public int TaskType { get; set; }

    /// <summary>
    /// Son iletişim zamanı
    /// </summary>
    [Column("last_ping")]
    public DateTime? LastPing { get; set; }

    /// <summary>
    /// İşlem ayarları (JSON formatında)
    /// </summary>
    [Column("settings")]
    [JsonConverter(typeof(JsonDocumentConverter))]
    public JsonDocument? Settings { get; set; }


    /// <summary>
    /// İşlem verileri
    /// </summary>
    [NotMapped]
    public ProcessData Data { get; set; } = new();


    public TermPrompts TermPrompts = new();
    public TextPrompts TextPrompts = new();
    public ContextPrompts ContextPrompts = new();

    /// <summary>
    /// İki Process nesnesinin eşitliğini kontrol eder
    /// </summary>
    /// <param name="obj"></param>
    /// <returns></returns>
    public override bool Equals(object? obj)
    {
        return obj is Process other && Id == other.Id;
    }

    public override int GetHashCode()
    {
        return Id.GetHashCode();
    }
}

/// <summary>
/// İşlem oluşturma için kullanılan DTO modeli
/// </summary>
public class ProcessCreateDto
{
    /// <summary>
    /// Bağlı olduğu proje kimliği
    /// </summary>
    [Required(ErrorMessage = "Proje kimliği zorunludur")]
    public int ProjectId { get; set; }

    /// <summary>
    /// İşlem türü
    /// </summary>
    [Required(ErrorMessage = "İşlem türü zorunludur")]
    public ProcessTaskType TaskType { get; set; }

    /// <summary>
    /// İşlem durumu (varsayılan: pending)
    /// </summary>
    public ProcessStatus Status { get; set; } = ProcessStatus.Pending;

    /// <summary>
    /// İşlem ayarları (JSON formatında)
    /// </summary>
    [JsonConverter(typeof(JsonDocumentConverter))]
    public JsonDocument? Settings { get; set; }



}

/// <summary>
/// İşlem güncelleme için kullanılan DTO modeli
/// </summary>
public class ProcessUpdateDto
{
    /// <summary>
    /// İşlem durumu
    /// </summary>
    public ProcessStatus Status { get; set; }

    /// <summary>
    /// İşlem sonucu
    /// </summary>
    public string? Result { get; set; }

    /// <summary>
    /// Hata mesajı
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Son iletişim zamanı
    /// </summary>
    public DateTime? LastPing { get; set; }

    /// <summary>
    /// ilerleme
    /// </summary>
    public int? Progress { get; set; }

    /// <summary>
    /// İşlem ayarları (JSON formatında)
    /// </summary>
    public JsonDocument? Settings { get; set; }
}



/// <summary>
/// İşlem durumu enum değerleri
/// </summary>
[JsonConverter(typeof(StringEnumConverter))]
public enum ProcessStatus
{
    /// <summary>
    /// Beklemede
    /// </summary>

    Pending,

    /// <summary>
    /// Devam ediyor
    /// </summary>
    InProgress,

    /// <summary>
    /// Tamamlandı
    /// </summary>
    Completed,

    /// <summary>
    /// Başarısız
    /// </summary>
    Failed,

    /// <summary>
    /// İptal edildi
    /// </summary>
    Cancelled
}

/// <summary>
/// İşlem türü enum değerleri
/// </summary>
public enum ProcessTaskType
{
    /// <summary>
    /// Proje oluşturma
    /// </summary>
    ProjectCreation,

    /// <summary>
    /// Metin çevirisi
    /// </summary>
    TextsTranslation,

    /// <summary>
    /// Terim tespiti
    /// </summary>
    TermsDetection,

    /// <summary>
    /// Terim çevirisi
    /// </summary>
    TermsTranslation,

    /// <summary>
    /// Verilerin google sheets'e aktarımı
    /// </summary>
    ExportToGoogleSheets,

    /// <summary>
    /// Web sitesi kazıma
    /// </summary>
    WebScraping,


    /// <summary>
    /// Context oluşturma
    /// </summary>
    ContextCreation,

    /// <summary>
    /// Proje verilerini Google Sheets ile senkronize etme
    /// </summary>
    ProjectSync
}



/// <summary>
/// İşlem takibi için thread-safe text sınıfı
/// </summary>
public class ProcessData : IDisposable
{
    private readonly SemaphoreSlim _semaphore = new(1, 1);

    public DynamicSchemaDbContext? Client { get; set; } = null;
    public TextFilterQuery? TextFilter { get; set; } = null;
    public List<int>? IsProcessed { get; set; } = [];
    public List<int>? IsUsing { get; set; } = [];

    public async Task<T> ExecuteAsync<T>(Func<Task<T>> action, CancellationToken cancellationToken = default)
    {
        await _semaphore.WaitAsync(cancellationToken);
        try
        {
            return await action();
        }
        finally
        {
            _semaphore.Release();
        }
    }

    public async Task ExecuteAsync(Func<Task> action, CancellationToken cancellationToken = default)
    {
        await _semaphore.WaitAsync(cancellationToken);
        try
        {
            await action();
        }
        finally
        {
            _semaphore.Release();
        }
    }

    public void Dispose() => _semaphore?.Dispose();
}

/// <summary>
/// Terim işlemleri için promptlar
/// </summary>
public class TermPrompts
{
    /// <summary>
    /// Terim tespit için kullanılan prompt
    /// </summary>
    public string? Detect { get; set; }

    /// <summary>
    /// Terim onayı için kullanılan prompt
    /// </summary>
    public string? Confirm { get; set; }

    /// <summary>
    /// Terim çevirisi için kullanılan prompt
    /// </summary>
    public string? Translate { get; set; }
}

/// <summary>
/// Metin işlemleri için promptlar
/// </summary>
public class TextPrompts
{
    /// <summary>
    /// Çeviri grubu için kullanılan prompt
    /// </summary>
    public string? Group { get; set; }

    /// <summary>
    /// Metin çevirisi için kullanılan prompt
    /// </summary>
    public string? Translate { get; set; }

    /// <summary>
    /// UI, Sistem ve Mekanik kategorisi için kullanılan prompt
    /// </summary>
    public string? UiSystemMechanics { get; set; }

    /// <summary>
    /// Sıralı Diyalog kategorisi için kullanılan prompt
    /// </summary>
    public string? SequentialDialogue { get; set; }

    /// <summary>
    /// Karışık Diyalog kategorisi için kullanılan prompt
    /// </summary>
    public string? MixedDialogue { get; set; }

    /// <summary>
    /// Oyun İçi İçerik kategorisi için kullanılan prompt
    /// </summary>
    public string? InGameContent { get; set; }

    /// <summary>
    /// Hikaye ve Lore kategorisi için kullanılan prompt
    /// </summary>
    public string? StoryLore { get; set; }

    /// <summary>
    /// Karışık Diğer kategorisi için kullanılan prompt
    /// </summary>
    public string? MixedOther { get; set; }
}

/// <summary>
/// Contexts için promptlar
/// </summary>
public class ContextPrompts
{
    /// <summary>
    /// Context oluşturma için kullanılan prompt
    /// </summary>
    public string? Create { get; set; }
}