using System.Data;
using Microsoft.EntityFrameworkCore;
using Npgsql;
using TranslationAgentServer.Data;
using TranslationAgentServer.Interfaces;
using TranslationAgentServer.Models;
using Pgvector;
namespace TranslationAgentServer.Services;

/// <summary>
/// PostgreSQL veritabanı servisi implementasyonu
/// Entity Framework Core ile veritabanı işlemlerini yönetir
/// </summary>
public class DatabaseService : IDatabaseService
{

    private readonly IConfiguration _configuration;
    private readonly ILogger<DatabaseService> _logger;
    private readonly string _connectionString;
    private NpgsqlDataSource? _dataSource;


    /// <summary>
    /// DatabaseService constructor
    /// </summary>
    /// <param name="context">Veritabanı bağlamı</param>
    /// <param name="configuration">Konfigürasyon servisi</param>
    /// <param name="logger">Logger servisi</param>

    public DatabaseService(
        IConfiguration configuration,
        ILogger<DatabaseService> logger)
    {

        _configuration = configuration;
        _logger = logger;
        _connectionString = _configuration.GetConnectionString("DefaultConnection") ?? throw new InvalidOperationException("DefaultConnection connection string not found.");
        var dataSourceBuilder = new NpgsqlDataSourceBuilder(_connectionString);
        dataSourceBuilder.UseVector();

        _dataSource = dataSourceBuilder.Build();

    }
    /// <summary>
    /// Contexti dönderir
    /// </summary>
    /// <returns>Context</returns>
    public ApplicationDbContext GetContext()
    {
        var _context = new ApplicationDbContext(new DbContextOptionsBuilder<ApplicationDbContext>()
                    .UseNpgsql(_dataSource)
                    .Options);
        return _context;
    }

    /// <summary>
    /// Proje için özel DbContext'i dönderir
    /// </summary>
    /// <returns>Context</returns>
    public DynamicSchemaDbContext GetProjectContext(int projectId)
    {
        var project = GetContext().Projects.FirstOrDefault(p => p.Id == projectId);
        if (project == null)
        {
            throw new Exception($"Proje bulunamadı: {projectId}");
        }

        var schemaName = project.SchemaName;

        return CreateDynamicContext(schemaName);
    }

    /// <summary>
    /// Raw SQL sorgusu çalıştırır
    /// </summary>
    /// <param name="sql">SQL sorgusu</param>
    /// <param name="parameters">Parametreler</param>
    /// <returns>Sorgu sonucu</returns>
    public async Task<List<Dictionary<string, object>>> ExecuteRawSqlAsync(string sql, params object[] parameters)
    {
        var results = new List<Dictionary<string, object>>();

        try
        {
            using var connection = new NpgsqlConnection(_connectionString);
            await connection.OpenAsync();

            using var command = new NpgsqlCommand(sql, connection);

            // Parametreleri ekle
            for (int i = 0; i < parameters.Length; i++)
            {
                command.Parameters.AddWithValue($"@p{i}", parameters[i]);
            }

            using var reader = await command.ExecuteReaderAsync();

            while (await reader.ReadAsync())
            {
                var row = new Dictionary<string, object>();
                for (int i = 0; i < reader.FieldCount; i++)
                {
                    row[reader.GetName(i)] = reader.GetValue(i);
                }
                results.Add(row);
            }

            return results;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Raw SQL sorgusu çalıştırılırken hata oluştu: {Sql}", sql);
            throw;
        }
    }

    /// <summary>
    /// Veritabanı backup'ı alır
    /// </summary>
    /// <param name="backupPath">Backup dosya yolu</param>
    /// <returns>Backup sonucu</returns>
    public async Task<bool> CreateBackupAsync(string backupPath)
    {
        try
        {
            // Bu metot pg_dump kullanarak backup alabilir
            // Şimdilik basit bir implementasyon
            _logger.LogWarning("Backup özelliği henüz implementasyona alınmamıştır.");
            await Task.CompletedTask;
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Backup alınırken hata oluştu.");
            return false;
        }
    }

    /// <summary>
    /// Backup'tan veritabanını geri yükler
    /// </summary>
    /// <param name="backupPath">Backup dosya yolu</param>
    /// <returns>Geri yükleme sonucu</returns>
    public async Task<bool> RestoreBackupAsync(string backupPath)
    {
        try
        {
            // Bu metot pg_restore kullanarak geri yükleme yapabilir
            // Şimdilik basit bir implementasyon
            _logger.LogWarning("Restore özelliği henüz implementasyona alınmamıştır.");
            await Task.CompletedTask;
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Restore işlemi sırasında hata oluştu.");
            return false;
        }
    }

    /// <summary>
    /// Belirtilen şema için dinamik DbContext oluşturur
    /// </summary>
    /// <param name="schemaName">Şema adı</param>
    /// <returns>Dinamik şema DbContext'i</returns>
    public DynamicSchemaDbContext CreateDynamicContext(string schemaName)
    {
      var optionsBuilder = new DbContextOptionsBuilder<DynamicSchemaDbContext>();
        optionsBuilder.UseNpgsql(_dataSource, o => o.UseVector()); // Pgvector desteği

        return new DynamicSchemaDbContext(optionsBuilder.Options, schemaName);
    }

    /// <summary>
    /// Belirtilen proje adını kullanarak şema ekler ve sonucu kontrol eder.
    /// </summary>
    /// <param name="projectName">Eklenecek projenin adı.</param>
    /// <returns>İşlemin başarılı olup olmadığını belirten bir boolean değer.</returns>
    public async Task<bool> AddSchemaAsync(string projectName)
    {
        try
        {
            var sql = $"SELECT * FROM public.create_schema_if_not_exists('{projectName}');";
            var result = await ExecuteRawSqlAsync(sql);

            if (result != null && result.Count > 0)
            {
                var row = result[0];

                // OUT parametreler artık direkt sütun olarak dönüyor
                var success = row.ContainsKey("success") ? Convert.ToBoolean(row["success"]) : false;
                var message = row.ContainsKey("message") ? row["message"]?.ToString() ?? "" : "";
                var schemaCreated = row.ContainsKey("schema_created") ? Convert.ToBoolean(row["schema_created"]) : false;
                var tablesCreated = row.ContainsKey("tables_created") ? row["tables_created"]?.ToString() ?? "" : "";

                if (!success)
                {
                    throw new InvalidOperationException($"Şema '{projectName}' oluşturulurken hata oluştu. Mesaj: {message}");
                }

                _logger.LogInformation("Şema '{SchemaName}' işlemi tamamlandı. Success: {Success}, Message: {Message}, Schema Created: {SchemaCreated}, Tables Created: {TablesCreated}",
                    projectName, success, message, schemaCreated, tablesCreated);

                return success;
            }
            else
            {
                _logger.LogWarning("Şema '{SchemaName}' fonksiyonundan beklenen formatta yanıt alınamadı.", projectName);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Şema '{SchemaName}' pgrst yapılandırmasına eklenirken hata oluştu.", projectName);
            return false;
        }
    }

}